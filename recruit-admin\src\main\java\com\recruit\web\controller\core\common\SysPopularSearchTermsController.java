package com.recruit.web.controller.core.common;

import java.util.ArrayList;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.recruit.common.annotation.Log;
import com.recruit.common.core.controller.BaseController;
import com.recruit.common.core.domain.AjaxResult;
import com.recruit.common.enums.BusinessType;
import com.recruit.core.domain.SysPopularSearchTerms;
import com.recruit.core.service.ISysPopularSearchTermsService;
import com.recruit.common.utils.poi.ExcelUtil;
import com.recruit.common.core.page.TableDataInfo;

/**
 * 搜索职位热词Controller
 *
 * <AUTHOR>
 * @date 2023-04-15
 */
@RestController
@RequestMapping("/core/searchTerms")
public class SysPopularSearchTermsController extends BaseController
{
    @Autowired
    private ISysPopularSearchTermsService sysPopularSearchTermsService;

    /**
     * 查询搜索职位热词列表
     */
    @PreAuthorize("@ss.hasPermi('core:searchTerms:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysPopularSearchTerms sysPopularSearchTerms)
    {
        startPage();
        List<SysPopularSearchTerms> list = sysPopularSearchTermsService.selectSysPopularSearchTermsList(sysPopularSearchTerms);
        return getDataTable(list);
    }

    /**
     * 导出搜索职位热词列表
     */
    @PreAuthorize("@ss.hasPermi('core:searchTerms:export')")
    @Log(title = "搜索职位热词", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysPopularSearchTerms sysPopularSearchTerms)
    {
        List<SysPopularSearchTerms> list = sysPopularSearchTermsService.selectSysPopularSearchTermsList(sysPopularSearchTerms);
        ExcelUtil<SysPopularSearchTerms> util = new ExcelUtil<SysPopularSearchTerms>(SysPopularSearchTerms.class);
        util.exportExcel(response, list, "搜索职位热词数据");
    }

    /**
     * 获取搜索职位热词详细信息
     */
    @PreAuthorize("@ss.hasPermi('core:searchTerms:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        SysPopularSearchTerms searchTerms = sysPopularSearchTermsService.selectSysPopularSearchTermsById(id);
        List<String> positionList = new ArrayList<>();
        positionList.add(searchTerms.getPositionCode());
        searchTerms.setPositionCodes(positionList);
        return success(searchTerms);
    }

    /**
     * 新增搜索职位热词
     */
    @PreAuthorize("@ss.hasPermi('core:searchTerms:add')")
    @Log(title = "搜索职位热词", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SysPopularSearchTerms sysPopularSearchTerms)
    {
        if(sysPopularSearchTerms.getPositionCodes().size() == 1){
            sysPopularSearchTerms.setPositionCode(sysPopularSearchTerms.getPositionCodes().get(0));
        }else if(sysPopularSearchTerms.getPositionCodes().size() == 2){
            sysPopularSearchTerms.setPositionCode(sysPopularSearchTerms.getPositionCodes().get(1));
        }else if(sysPopularSearchTerms.getPositionCodes().size() == 3){
            sysPopularSearchTerms.setPositionCode(sysPopularSearchTerms.getPositionCodes().get(2));
        }
        return toAjax(sysPopularSearchTermsService.insertSysPopularSearchTerms(sysPopularSearchTerms));
    }

    /**
     * 修改搜索职位热词
     */
    @PreAuthorize("@ss.hasPermi('core:searchTerms:edit')")
    @Log(title = "搜索职位热词", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SysPopularSearchTerms sysPopularSearchTerms)
    {
        if(sysPopularSearchTerms.getPositionCodes().size() == 1){
            sysPopularSearchTerms.setPositionCode(sysPopularSearchTerms.getPositionCodes().get(0));
        }else if(sysPopularSearchTerms.getPositionCodes().size() == 2){
            sysPopularSearchTerms.setPositionCode(sysPopularSearchTerms.getPositionCodes().get(1));
        }else if(sysPopularSearchTerms.getPositionCodes().size() == 3){
            sysPopularSearchTerms.setPositionCode(sysPopularSearchTerms.getPositionCodes().get(2));
        }
        return toAjax(sysPopularSearchTermsService.updateSysPopularSearchTerms(sysPopularSearchTerms));
    }

    /**
     * 删除搜索职位热词
     */
    @PreAuthorize("@ss.hasPermi('core:searchTerms:remove')")
    @Log(title = "搜索职位热词", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(sysPopularSearchTermsService.deleteSysPopularSearchTermsByIds(ids));
    }
}
