package com.recruit.core.domain.response;

import com.recruit.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 人才引进报名统计DTO
 *
 * <AUTHOR>
 * @date 2024-07-10
 */
@Data
@ApiModel("人才引进报名统计")
public class TalentIntroductionStatisticsDTO {

    @Excel(name = "岗位代码", sort = 1)
    @ApiModelProperty("岗位代码")
    private String nameOfSuperiorUnit;

    @Excel(name = "单位名称", sort = 2)
    @ApiModelProperty("单位名称")
    private String nameOfEmployer;

    @Excel(name = "岗位分类", sort = 3)
    @ApiModelProperty("岗位分类")
    private String jobCategory;

    @Excel(name = "岗位名称", sort = 4)
    @ApiModelProperty("岗位名称")
    private String recruitmentPositions;

    @Excel(name = "招聘人数", sort = 5)
    @ApiModelProperty("招聘人数")
    private String recruitingNum;

    @Excel(name = "报名人数", sort = 6)
    @ApiModelProperty("报名人数")
    private Integer enrollCount;

    @Excel(name = "未审核人数", sort = 7)
    @ApiModelProperty("未审核人数")
    private Integer pendingCount;

    @Excel(name = "审核通过人数", sort = 8)
    @ApiModelProperty("审核通过人数")
    private Integer approvedCount;

    @Excel(name = "审核不通过人数", sort = 9)
    @ApiModelProperty("审核不通过人数")
    private Integer rejectedCount;
} 