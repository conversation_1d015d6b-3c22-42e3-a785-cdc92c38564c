package com.recruit.web.controller.core.common;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.recruit.core.domain.RecruitAdvert;
import com.recruit.core.service.IRecruitAdvertService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.recruit.common.annotation.Log;
import com.recruit.common.core.controller.BaseController;
import com.recruit.common.core.domain.AjaxResult;
import com.recruit.common.enums.BusinessType;
import com.recruit.common.utils.poi.ExcelUtil;
import com.recruit.common.core.page.TableDataInfo;

/**
 * 广告Controller
 *
 * <AUTHOR>
 * @date 2023-03-15
 */
@RestController
@RequestMapping("/core/advert")
public class RecruitAdvertController extends BaseController
{
    @Autowired
    private IRecruitAdvertService recruitAdvertService;

    /**
     * 查询广告列表
     */
    @PreAuthorize("@ss.hasPermi('core:advert:list')")
    @GetMapping("/list")
    public TableDataInfo list(RecruitAdvert recruitAdvert)
    {
        startPage();
        List<RecruitAdvert> list = recruitAdvertService.selectRecruitAdvertList(recruitAdvert);
        return getDataTable(list);
    }

    /**
     * 导出广告列表
     */
    @PreAuthorize("@ss.hasPermi('core:advert:export')")
    @Log(title = "广告", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RecruitAdvert recruitAdvert)
    {
        List<RecruitAdvert> list = recruitAdvertService.selectRecruitAdvertList(recruitAdvert);
        ExcelUtil<RecruitAdvert> util = new ExcelUtil<RecruitAdvert>(RecruitAdvert.class);
        util.exportExcel(response, list, "广告数据");
    }

    /**
     * 获取广告详细信息
     */
    @PreAuthorize("@ss.hasPermi('core:advert:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(recruitAdvertService.selectRecruitAdvertById(id));
    }

    /**
     * 新增广告
     */
    @PreAuthorize("@ss.hasPermi('core:advert:add')")
    @Log(title = "广告", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RecruitAdvert recruitAdvert)
    {
        return toAjax(recruitAdvertService.insertRecruitAdvert(recruitAdvert));
    }

    /**
     * 修改广告
     */
    @PreAuthorize("@ss.hasPermi('core:advert:edit')")
    @Log(title = "广告", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RecruitAdvert recruitAdvert)
    {
        return toAjax(recruitAdvertService.updateRecruitAdvert(recruitAdvert));
    }

    /**
     * 删除广告
     */
    @PreAuthorize("@ss.hasPermi('core:advert:remove')")
    @Log(title = "广告", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(recruitAdvertService.deleteRecruitAdvertByIds(ids));
    }

    @PreAuthorize("@ss.hasPermi('core:advert:edit')")
    @Log(title = "广告", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody RecruitAdvert recruitAdvert)
    {
        recruitAdvert.setUpdateBy(getUsername());
        return toAjax(recruitAdvertService.updateRecruitAdvert(recruitAdvert));
    }
}
