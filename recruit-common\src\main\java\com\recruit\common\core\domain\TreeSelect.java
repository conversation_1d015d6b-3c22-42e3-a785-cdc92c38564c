package com.recruit.common.core.domain;

import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.recruit.common.core.domain.entity.RecruitCompanyIndustry;
import com.recruit.common.core.domain.entity.RecruitPosition;
import com.recruit.common.core.domain.entity.SysDept;
import com.recruit.common.core.domain.entity.SysMenu;
import com.recruit.common.core.domain.entity.SysRegion;

/**
 * Treeselect树结构实体类
 *
 * <AUTHOR>
 */
public class TreeSelect implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 节点ID */
    private Long id;

    /** 节点名称 */
    private String code;

    private String value;

    /** 节点名称 */
    private String name;

    /** 节点名称 */
    private String label;

    /** 子节点 */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<TreeSelect> children;

    /** 子节点 */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<TreeSelect> subLevelModelList;

    public TreeSelect()
    {

    }

    public TreeSelect(SysDept dept)
    {
        this.id = dept.getDeptId();
        this.label = dept.getDeptName();
        this.children = dept.getChildren().stream().map(TreeSelect::new).collect(Collectors.toList());
    }

    public TreeSelect(SysMenu menu)
    {
        this.id = menu.getMenuId();
        this.label = menu.getMenuName();
        this.children = menu.getChildren().stream().map(TreeSelect::new).collect(Collectors.toList());
    }

    public TreeSelect(RecruitCompanyIndustry menu)
    {
        this.code = String.valueOf(menu.getId());
        this.name = menu.getName();
        this.subLevelModelList = menu.getChildren().stream().map(TreeSelect::new).collect(Collectors.toList());
    }

    public TreeSelect(RecruitPosition menu)
    {
        this.code = String.valueOf(menu.getId());
        this.name = menu.getName();
        this.label = String.valueOf(menu.getGrade());
        this.subLevelModelList = menu.getChildren().stream().map(TreeSelect::new).collect(Collectors.toList());
    }

    public TreeSelect(SysRegion menu)
    {
        this.code = String.valueOf(menu.getId());
        this.value = String.valueOf(menu.getId());
        this.label = menu.getName();
        this.children = menu.getChildren().stream().map(TreeSelect::new).collect(Collectors.toList());
    }

    public Long getId()
    {
        return id;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getLabel()
    {
        return label;
    }

    public void setLabel(String label)
    {
        this.label = label;
    }

    public List<TreeSelect> getChildren()
    {
        return children;
    }

    public void setChildren(List<TreeSelect> children)
    {
        this.children = children;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public List<TreeSelect> getSubLevelModelList() {
        return subLevelModelList;
    }

    public void setSubLevelModelList(List<TreeSelect> subLevelModelList) {
        this.subLevelModelList = subLevelModelList;
    }
}
