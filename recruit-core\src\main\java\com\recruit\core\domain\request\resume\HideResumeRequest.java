package com.recruit.core.domain.request.resume;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @Auther: <PERSON> kong
 * @Date: 2023/4/3 16:22
 * @Description:
 */
@Data
@ApiModel("隐藏简历1")
public class HideResumeRequest {

    @NotBlank(message = "隐藏简历不能为空")
    @ApiModelProperty("隐藏简历，0关闭，1开启")
    private String hideResume;
}
