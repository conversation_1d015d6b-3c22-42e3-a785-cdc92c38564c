package com.recruit.core.domain;

import com.recruit.common.annotation.Excel;
import com.recruit.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 企业收到简历对象 recruit_enterprise_receives_resume
 *
 * <AUTHOR>
 * @date 2023-04-11
 */
@Data
@ApiModel("企业收到简历")
public class RecruitEnterpriseReceivesResume extends BaseEntity
{
    private static final long serialVersionUID = 1L;
    private Long id;
    @Excel(name = "企业id")
    @ApiModelProperty("企业id")
    private Long enterpriseId;

    @Excel(name = "投递")
    @ApiModelProperty("投递")
    private Long dselivererId;

    @ApiModelProperty("职位id")
    private Long positionInfoId;

    @Excel(name = "附件简历")
    @ApiModelProperty("附件简历")
    private String attachmentResumeUrl;

    @ApiModelProperty("姓名")
    private String userName;

    @ApiModelProperty("头像")
    private String headSculpture;

    @ApiModelProperty("简历名称")
    private String resumeName;

    @ApiModelProperty("工作经验")
    private String workExperience;

    @ApiModelProperty("工作经验名称")
    private String workExperienceName;

    @ApiModelProperty("最高学历")
    private String education;

    @ApiModelProperty("最高学历名称")
    private String educationName;

    @ApiModelProperty("期望岗位")
    private String expectedPosition;

    @ApiModelProperty("期望岗位名称")
    private String expectedPositionName;

    @ApiModelProperty("最高薪资")
    private String maximumSalary;

    @ApiModelProperty("最低薪资")
    private String minimumWage;

}
