package com.recruit.common.utils.ocr;

import okhttp3.*;
import java.io.*;
import org.json.JSONObject;


/**
 * @Auther: <PERSON> kong
 * @Date: 2023/3/24 16:41
 * @Description:
 */
public class OcrSampleUtils {

    public static final String API_KEY = "pYNeGlQ6Yy8pBXkteimKAGbs";
    public static final String SECRET_KEY = "FukXVNDEOiPivlGQVDvjlcHcXfA3jr9M";

    static final OkHttpClient HTTP_CLIENT = new OkHttpClient().newBuilder().build();

    /**
     * 校验营业执照
     * @param url
     * @return
     * @throws IOException
     */
    public static String verificationOfBusinessLicense(String url) {

        String ss = "";
        try {
            MediaType mediaType = MediaType.parse("application/x-www-form-urlencoded");
            RequestBody body = RequestBody.create(mediaType, "url=" + url);
            Request request = new Request.Builder()
                    .url("https://aip.baidubce.com/rest/2.0/ocr/v1/business_license?access_token=" + getAccessToken())
                    .method("POST", body)
                    .addHeader("Content-Type", "application/x-www-form-urlencoded")
                    .addHeader("Accept", "application/json")
                    .build();
            Response response = HTTP_CLIENT.newCall(request).execute();
            ss = response.body().string();
        }catch (Exception e){
            e.printStackTrace();
        }

        return ss;
    }

    /**
     * 从用户的AK，SK生成鉴权签名（Access Token）
     *
     * @return 鉴权签名（Access Token）
     * @throws IOException IO异常
     */
    static String getAccessToken() throws IOException {
        MediaType mediaType = MediaType.parse("application/x-www-form-urlencoded");
        RequestBody body = RequestBody.create(mediaType, "grant_type=client_credentials&client_id=" + API_KEY
                + "&client_secret=" + SECRET_KEY);
        Request request = new Request.Builder()
                .url("https://aip.baidubce.com/oauth/2.0/token")
                .method("POST", body)
                .addHeader("Content-Type", "application/x-www-form-urlencoded")
                .build();
        Response response = HTTP_CLIENT.newCall(request).execute();

        return new JSONObject(response.body().string()).getString("access_token");
    }


}
