package com.recruit.web.core.redisMSMQ;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

/**
 * @Auther: <PERSON> kong
 * @Date: 2023/5/17 9:19
 * @Description: redis消息队列生产者
 */
@Slf4j
@Component
public class MessageProducer {

    @Autowired
    private RedisTemplate<String,Object> redisTemplate;

    /**
     * 生产消息，传入类型
     * @param messageType
     * @param object
     */
    public void lPush(String messageType, Object object) {
        redisTemplate.opsForList().leftPush(messageType, object);
    }
}
