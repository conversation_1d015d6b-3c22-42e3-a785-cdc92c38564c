package com.recruit.webApi;

import com.recruit.common.core.controller.BaseController;
import com.recruit.common.core.domain.AjaxResult;
import com.recruit.common.core.page.TableDataInfo;
import com.recruit.common.utils.SnowFlakeUtil;
import com.recruit.common.utils.StringUtils;
import com.recruit.core.domain.RecruitUserInfo;
import com.recruit.core.domain.TemporaryOrderEvaluate;
import com.recruit.core.domain.TemporaryServiceGoodsPrice;
import com.recruit.core.domain.TemporaryServiceUserOrder;
import com.recruit.core.domain.request.merchant.*;
import com.recruit.core.domain.request.pay.MemberFeeRequest;
import com.recruit.core.service.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Auther: Wu kong
 * @Date: 2023/6/3 16:36
 * @Description:
 */
@Api(tags= "(6-3)零工商户订单接口")
@Slf4j
@RestController
@RequestMapping("/web/api/oddJobMerchantOrder")
public class OddJobMerchantOrderController extends BaseController {

    @Autowired
    private ISysPayChannelService sysPayChannelService;

    @Autowired
    private IRecruitUserInfoService recruitUserInfoService;

    @Autowired
    private ITemporaryServiceGoodsService temporaryServiceGoodsService;

    @Autowired
    private ITemporaryOrderEvaluateService temporaryOrderEvaluateService;

    @Autowired
    private ITemporaryServiceUserOrderService temporaryServiceUserOrderService;


    @ApiOperation(value = "获取用户零工订单",notes="orderStatus 0全部，1待确定，2待支付，3待服务，4待评价，5退款售后    " +
            "orderStatus订单状态 0预约，1下单，2预约确认，3待支付，4待服务，5订单完成，6取消订单，7退款中，8退款成功    " +
            "commentStatus评价状态，0未评价，1评价")
    @GetMapping(value = "/getUserOddJobOrder", produces = {"application/json"})
    @ApiResponses({
            @ApiResponse(code = 200, message = "返回实体", response = TemporaryServiceUserOrder.class)
    })
    public TableDataInfo getUserOddJobOrder(String orderStatus)
    {
        Long userId = getUserId();
        //Long userId = 103L;
        TemporaryServiceUserOrder serviceUserOrder = new TemporaryServiceUserOrder();
        serviceUserOrder.setPlaceOrderUserId(userId);
        serviceUserOrder.setOrderStatus(orderStatus);
        List<TemporaryServiceUserOrder> lists = temporaryServiceUserOrderService.selectTemporaryServiceUserOrderListTwo(serviceUserOrder);
        return getDataTable(lists);
    }

    @ApiOperation("获取用户零工订单详情")
    @GetMapping(value = "/getUserOddJobOrderDetails", produces = {"application/json"})
    @ApiResponses({
            @ApiResponse(code = 200, message = "返回实体", response = TemporaryServiceUserOrder.class)
    })
    public AjaxResult getUserOddJobOrderDetails(Long id)
    {
        Long userId = getUserId();
        //Long userId = 103L;
        TemporaryServiceUserOrder serviceUserOrder = temporaryServiceUserOrderService.selectServiceUserOrderByIdAndUserId(id, userId);
        if(serviceUserOrder != null){
            TemporaryOrderEvaluate orderEvaluate = new TemporaryOrderEvaluate();
            orderEvaluate.setServiceUserOrderId(serviceUserOrder.getId());
            orderEvaluate.setPlaceOrderUserId(userId);
            List<TemporaryOrderEvaluate> lists = temporaryOrderEvaluateService.selectTemporaryOrderEvaluateListTwo(orderEvaluate);
            if(!lists.isEmpty()) {
                serviceUserOrder.setOrderEvaluateList(lists);
            }
        }
        return success(serviceUserOrder);
    }


    @ApiOperation("用户下单接口")
    @PostMapping("/addUserPlacesAnOrder")
    public AjaxResult addUserPlacesAnOrder(@RequestBody @Validated UserPlacesAnOrderRequest request) {

        Long userId = getUserId();
        RecruitUserInfo userInfo = recruitUserInfoService.selectRecruitUserInfoById(userId);
        if(userInfo == null){
            throw new RuntimeException("用户信息不存在！");
        }
        TemporaryServiceUserOrder serviceUserOrder = new TemporaryServiceUserOrder();
        //下单人
        serviceUserOrder.setPlaceOrderUserId(userId);
        //下单人手机号
        serviceUserOrder.setPlaceOrderPhone(userInfo.getLiaisonNanPhone());
        //商品id
        serviceUserOrder.setServiceGoodsId(request.getServiceGoodsId());
        //商品价格id
        serviceUserOrder.setServiceGoodsPriceId(request.getServiceGoodsPriceId());
        //预约日期
        serviceUserOrder.setArrivalTime(request.getArrivalTime());
        //到门时间
        serviceUserOrder.setToDoorTime(request.getToDoorTime());

        if(StringUtils.equals(request.getType(), "1")) {
            //服务商品价格对象
            TemporaryServiceGoodsPrice serviceGoodsPrice = temporaryServiceGoodsService.selectServiceGoodsByGoodsPriceId(request.getServiceGoodsPriceId());
            if (serviceGoodsPrice != null) {
                //订单价格
                serviceUserOrder.setOrderPrice(serviceGoodsPrice.getGoodsPrice().multiply(new BigDecimal(serviceGoodsPrice.getGoodsNum())));
                //实际价格
                serviceUserOrder.setRealPrice(serviceGoodsPrice.getGoodsPrice().multiply(new BigDecimal(serviceGoodsPrice.getGoodsNum())));
                //订单状态
                serviceUserOrder.setOrderStatus("3");
            }else {
                throw new RuntimeException("服务信息不存在！");
            }
        }else {
            //订单状态
            serviceUserOrder.setOrderStatus("0");
            //订单价格
            serviceUserOrder.setOrderPrice(new BigDecimal(0));
            //实际价格
            serviceUserOrder.setRealPrice(new BigDecimal(0));
        }
        //备注
        serviceUserOrder.setRemark(request.getRemark());
        //支付状态
        serviceUserOrder.setPayStatus("0");
        //支付渠道
        serviceUserOrder.setPayChannel(request.getPayChannel());
        //下单地址
        serviceUserOrder.setAddress(userInfo.getAddress());
        //门牌号
        serviceUserOrder.setHouseNumber(userInfo.getHouseNumber());
        //纬度
        serviceUserOrder.setLatitude(userInfo.getLatitude());
        //经度
        serviceUserOrder.setLongitude(userInfo.getLongitude());
        //联系人
        serviceUserOrder.setLiaisonNan(userInfo.getLiaisonNan());
        //评论状态
        serviceUserOrder.setCommentStatus("0");
        //派单状态
        serviceUserOrder.setDispatchStatus("0");
        int ss = temporaryServiceUserOrderService.insertTemporaryServiceUserOrder(serviceUserOrder);
        if(ss == 0){
            return error();
        }else {
            Map<String, Object> map = new HashMap<>();
            map.put("serviceUserOrderId", serviceUserOrder.getId());
            map.put("orderStatus", serviceUserOrder.getOrderStatus());
            return success(map);
        }
    }


    @ApiOperation("确认支付, orderStatus状态为 2或3的时候调用")
    @PostMapping("/confirmPayment")
    public AjaxResult confirmPayment(@RequestBody @Validated ConfirmPaymentRequest request) {
        //查询订单信息
        TemporaryServiceUserOrder serviceUserOrder = temporaryServiceUserOrderService.selectTemporaryServiceUserOrderById(request.getServiceUserOrderId());
        if(serviceUserOrder == null){
            throw new RuntimeException("订单信息不存在！");
        }

        if(request.getOrderStatus().equals("0")){
            throw new RuntimeException("预约订单须等待工作人员联系！");
        }else if(request.getOrderStatus().equals("2")){
            serviceUserOrder.setOrderStatus("3");
        }else if(request.getOrderStatus().equals("3")){
            serviceUserOrder.setOrderStatus("3");
        }
        serviceUserOrder.setPayStatus("1");
        // 生成支付订单
        String flowNo = StringUtils.getOrderNo(serviceUserOrder.getPlaceOrderUserId());
        serviceUserOrder.setBizPayNo(flowNo);
        temporaryServiceUserOrderService.updateTemporaryServiceUserOrderThree(serviceUserOrder);
        //支付跳转地址
        Map<String, Object> result = sysPayChannelService.rechargeMember(new MemberFeeRequest(){{
            setChannel(request.getChannel());
            setPayChannel(request.getPayChannel());
            setRechargeAmount(serviceUserOrder.getRealPrice());
            setOrderType("2");
            setFlowNo(flowNo);
            setDescription("零工服务");
        }});
        result.put("realPrice", serviceUserOrder.getRealPrice());
        return success(result);
    }

    @ApiOperation("保证金充值")
    @PostMapping("/earnestMoneyRecharge")
    public AjaxResult earnestMoneyRecharge(@RequestBody @Validated EarnestMoneyRechargeRequest request) {
        // 生成支付订单
        String flowNo = String.valueOf(SnowFlakeUtil.nextId());
        Map<String, Object> result = sysPayChannelService.rechargeMember(new MemberFeeRequest(){{
            setChannel(request.getChannel());
            setPayChannel(request.getPayChannel());
            setRechargeAmount(request.getEarnestMoney());
            setOrderType("3");
            setFlowNo(flowNo);
            setDescription("保证金充值");
        }});
        result.put("earnestMoney", request.getEarnestMoney());
        return success(result);
    }


    @ApiOperation("确认完成订单")
    @PostMapping("/confirmCompletionOrder")
    public AjaxResult confirmCompletionOrder(@RequestBody @Validated ConfirmCompletionOrderRequest request) {
        //查询订单信息
        TemporaryServiceUserOrder serviceUserOrder = temporaryServiceUserOrderService.selectTemporaryServiceUserOrderById(request.getServiceUserOrderId());
        if(serviceUserOrder == null){
            throw new RuntimeException("订单信息不存在！");
        }
        if(serviceUserOrder.getDispatchStatus().equals("0")){
            throw new RuntimeException("改订单未派单，请勿点击完成！");
        }
        //订单状态 0预约，1下单，2预约确认，3待支付，4待服务，5订单完成，6取消订单，7退款
        serviceUserOrder.setOrderStatus("5");
        return toAjax(temporaryServiceUserOrderService.updateTemporaryServiceUserOrderFour(serviceUserOrder));
    }


    @ApiOperation("取消订单")
    @PostMapping("/cancelOrder")
    public AjaxResult cancelOrder(@RequestBody @Validated CancelOrderRequest request) {
        //查询订单信息
        TemporaryServiceUserOrder serviceUserOrder = temporaryServiceUserOrderService.selectTemporaryServiceUserOrderById(request.getServiceUserOrderId());
        if(serviceUserOrder == null){
            throw new RuntimeException("订单信息不存在！");
        }
        //订单状态 0预约，1下单，2预约确认，3待支付，4待服务，5订单完成，6取消订单，7退款
        serviceUserOrder.setOrderStatus("6");
        return toAjax(temporaryServiceUserOrderService.updateTemporaryServiceUserOrderFour(serviceUserOrder));
    }


    @ApiOperation("添加订单评价")
    @PostMapping("/addOrderEvaluation")
    public AjaxResult addOrderEvaluation(@RequestBody @Validated OrderEvaluationRequest request) {

        TemporaryServiceUserOrder serviceUserOrder = temporaryServiceUserOrderService.selectTemporaryServiceUserOrderById(request.getServiceUserOrderId());
        if(serviceUserOrder == null){
            throw new RuntimeException("订单信息不存在！");
        }
        TemporaryOrderEvaluate orderEvaluate = new TemporaryOrderEvaluate();
        //用户订单id
        orderEvaluate.setServiceUserOrderId(request.getServiceUserOrderId());
        //评价星级
        orderEvaluate.setEvaluateStar(request.getEvaluateStar());
        //评价内容
        orderEvaluate.setEvaluationContent(request.getEvaluationContent());
        //下单人
        orderEvaluate.setPlaceOrderUserId(getUserId());
        //订单创建时间
        orderEvaluate.setOrderTime(serviceUserOrder.getCreateTime());
        int ss = temporaryOrderEvaluateService.insertTemporaryOrderEvaluate(orderEvaluate);
        if(ss > 0) {
            serviceUserOrder.setCommentStatus("1");
            temporaryServiceUserOrderService.updateTemporaryServiceUserOrder(serviceUserOrder);
        }
        return toAjax(ss);
    }

}
