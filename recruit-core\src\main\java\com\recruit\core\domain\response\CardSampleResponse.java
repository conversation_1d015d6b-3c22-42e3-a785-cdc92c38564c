package com.recruit.core.domain.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Auther: Wu kong
 * @Date: 2023/6/16 10:25
 * @Description:
 */
@Data
@ApiModel("身份证校验信息返回")
public class CardSampleResponse {

    @ApiModelProperty("识别结果")
    private String words_result;


    @ApiModelProperty("识别结果数量")
    private String words_result_num;

    @ApiModelProperty("用于校验身份证号码、性别、出生是否一致，输出结果及其对应关系如下：\n" +
            "-1： 身份证正面所有字段全为空\n" +
            "0： 身份证证号不合法，此情况下不返回身份证证号\n" +
            "1： 身份证证号和性别、出生信息一致\n" +
            "2： 身份证证号和性别、出生信息都不一致\n" +
            "3： 身份证证号和出生信息不一致\n" +
            "4： 身份证证号和性别信息不一致")
    private String idcard_number_type;

    @ApiModelProperty("-front：身份证含照片的一面\n" +
            "-back：身份证带国徽的一面\n" +
            "自动检测身份证正反面，如果传参指定方向与图片相反，支持正常识别，返回参数image_status字段为\"reversed_side\"")
    private String image_status;

    @ApiModelProperty("请求标识码，随机数，唯一。")
    private String log_id;

}
