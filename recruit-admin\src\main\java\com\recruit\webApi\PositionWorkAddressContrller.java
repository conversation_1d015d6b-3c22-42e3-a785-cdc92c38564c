package com.recruit.webApi;

import com.recruit.common.core.controller.BaseController;
import com.recruit.common.core.domain.AjaxResult;
import com.recruit.common.core.page.TableDataInfo;
import com.recruit.core.domain.RecruitWorkAddress;
import com.recruit.core.domain.request.position.SetPreferredAddressRequest;
import com.recruit.core.domain.request.position.WorkAddressRequest;
import com.recruit.core.domain.request.resume.RemoveRequest;
import com.recruit.core.service.IRecruitWorkAddressService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotBlank;

/**
 * @Auther: Wu kong
 * @Date: 2023/4/17 22:12
 * @Description:
 */
@Slf4j
@Api(tags= "职位工作地址接口")
@RestController
@RequestMapping("/web/api/position")
public class PositionWorkAddressContrller extends BaseController {


    @Autowired
    private IRecruitWorkAddressService recruitWorkAddressService;


    @ApiOperation("添加和更新工作地址，传id是更新，不传id是新增")
    @PostMapping("/addWorkAddress")
    public AjaxResult addWorkAddress(@RequestBody @Validated WorkAddressRequest request) {
        return toAjax(recruitWorkAddressService.addWorkAddress(request));
    }

    @ApiOperation("查询工作地址列表， selectedStatus 是否首选，只有一个地址首选，0未选中，1选中")
    @GetMapping(value = "/getWorkAddressList", produces = {"application/json"})
    @ApiResponses({
            @ApiResponse(code = 200, message = "返回实体", response = RecruitWorkAddress.class)
    })
    public TableDataInfo getWorkAddressList() {
        return getDataTable(recruitWorkAddressService.getWorkAddressList());
    }


    @ApiOperation("查询工作地址详情")
    @GetMapping(value = "/getWorkAddressInfo", produces = {"application/json"})
    @ApiResponses({
            @ApiResponse(code = 200, message = "返回实体", response = RecruitWorkAddress.class)
    })
    public AjaxResult getWorkAddressInfo(@NotBlank(message = "id不能为空") Long id) {
        return success(recruitWorkAddressService.selectRecruitWorkAddressById(id));
    }

    @ApiOperation("删除工作地址")
    @PostMapping("/removeWorkAddress")
    public AjaxResult removeWorkAddress(@RequestBody @Validated RemoveRequest request)
    {
        return toAjax(recruitWorkAddressService.deleteRecruitWorkAddressById(request.getId()));
    }

    @ApiOperation("设置首选地址")
    @PostMapping("/setPreferredAddress")
    public AjaxResult setPreferredAddress(@RequestBody @Validated SetPreferredAddressRequest request) {
        return toAjax(recruitWorkAddressService.setPreferredAddress(request));
    }
}
