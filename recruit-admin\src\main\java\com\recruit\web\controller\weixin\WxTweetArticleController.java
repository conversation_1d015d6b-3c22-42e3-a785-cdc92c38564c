package com.recruit.web.controller.weixin;

import java.math.BigDecimal;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.recruit.common.utils.DictUtils;
import com.recruit.common.utils.StringUtils;
import com.recruit.core.domain.RecruitPositionInfo;
import com.recruit.core.service.IRecruitPositionInfoService;
import com.recruit.core.service.IRecruitPositionService;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.recruit.common.annotation.Log;
import com.recruit.common.core.controller.BaseController;
import com.recruit.common.core.domain.AjaxResult;
import com.recruit.common.enums.BusinessType;
import org.springframework.web.multipart.MultipartFile;
import com.recruit.core.domain.WxTweetArticle;
import com.recruit.core.service.IWxTweetArticleService;
import com.recruit.common.utils.poi.ExcelUtil;
import com.recruit.common.core.page.TableDataInfo;

/**
 * 推文生成Controller
 *
 * <AUTHOR>
 * @date 2023-10-15
 */
@Slf4j
@RestController
@RequestMapping("/core/wxTweetArticle")
public class WxTweetArticleController extends BaseController
{
    @Autowired
    private WxMaService wxService;

    @Autowired
    private IWxTweetArticleService wxTweetArticleService;

    @Autowired
    private IRecruitPositionService recruitPositionService;

    @Autowired
    private IRecruitPositionInfoService recruitPositionInfoService;

    private static final String linkUrl = "https://api.weixin.qq.com/wxa/generate_urllink?access_token=ACCESS_TOKEN";


    /**
     * 查询推文生成列表
     */
    @GetMapping("/list")
    public TableDataInfo list(WxTweetArticle wxTweetArticle)
    {
        startPage();
        List<WxTweetArticle> list = wxTweetArticleService.selectWxTweetArticleList(wxTweetArticle);
        return getDataTable(list);
    }

    /**
     * 导出推文生成列表
     */
    @Log(title = "推文生成", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, WxTweetArticle wxTweetArticle)
    {
        List<WxTweetArticle> list = wxTweetArticleService.selectWxTweetArticleList(wxTweetArticle);
        ExcelUtil<WxTweetArticle> util = new ExcelUtil<WxTweetArticle>(WxTweetArticle.class);
        util.exportExcel(response, list, "推文生成数据");
    }

    /**
     * 获取推文生成详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(wxTweetArticleService.selectWxTweetArticleById(id));
    }

    /**
     * 新增推文生成
     */
    @Log(title = "新增推文生成", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody WxTweetArticle wxTweetArticle)
    {
        return toAjax(wxTweetArticleService.insertWxTweetArticle(wxTweetArticle));
    }

    /**
     * 修改推文生成
     */
    @Log(title = "推文生成", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody WxTweetArticle wxTweetArticle)
    {
        return toAjax(wxTweetArticleService.updateWxTweetArticle(wxTweetArticle));
    }

    /**
     * 删除推文生成
     */
    @Log(title = "推文生成", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(wxTweetArticleService.deleteWxTweetArticleByIds(ids));
    }


    /**
     * 导入推文生成
     * @param file
     * @param updateSupport
     * @return
     * @throws Exception
     */
    @Log(title = "推文生成", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<WxTweetArticle> util = new ExcelUtil<>(WxTweetArticle.class);
        List<WxTweetArticle> lists = util.importExcel(file.getInputStream());
        String message = wxTweetArticleService.importWxTweetArticle(lists, updateSupport);
        return AjaxResult.success(message);
    }


    @Log(title = "推文生成", businessType = BusinessType.INSERT)
    @PostMapping("/generateTweets")
    public AjaxResult generateTweets(@RequestBody WxTweetArticle wxTweetArticle)
    {
        StringBuilder content = new StringBuilder();
        //职位
        if(StringUtils.isNotEmpty(wxTweetArticle.getPositionInfoIds())){
            for(Long positionId : wxTweetArticle.getPositionInfoIds()){
                RecruitPositionInfo positionInfo = recruitPositionInfoService.selectRecruitPositionInfoById(positionId);
                if(positionInfo.getPositionCode() != null && !StringUtils.equals(positionInfo.getPositionCode(), "")) {
                    positionInfo.setPositionName(recruitPositionService.getMap(positionInfo.getPositionCode()));
                }
                //公司福利
                StringBuilder materialBenefits = new StringBuilder();
                if(positionInfo.getMaterialBenefits() != null && !StringUtils.equals(positionInfo.getMaterialBenefits(), "")){
                    String[] sss = positionInfo.getMaterialBenefits().split(",");
                    for(String s : sss){
                        if(StringUtils.equals(materialBenefits, "")){
                            materialBenefits.append(DictUtils.getDictLabel("company_benefits",s));
                        }else {
                            materialBenefits.append(" | ").append(DictUtils.getDictLabel("company_benefits",s));
                        }
                    }
                }

                //薪资
                String minimumWage = "";
                //如果最低最高金额相同，则最高金额赋值为空
                if (positionInfo.getMinimumWage() != null && !StringUtils.equals(positionInfo.getMinimumWage(), "")) {
                    minimumWage = positionInfo.getMinimumWage() + "元/月";
                }
                if (positionInfo.getMaximumSalary() != null && !StringUtils.equals(positionInfo.getMaximumSalary(), "")) {
                    if (!positionInfo.getMinimumWage().equals(positionInfo.getMaximumSalary())) {
                        minimumWage = positionInfo.getMinimumWage() + "-" + positionInfo.getMaximumSalary() + "元/月";
                    }
                }


                String mainAdditionString = "";
                String minimumWageString = "";
                String maximumSalaryString = "";
                try {
                    //最高薪资
                    if(positionInfo.getMaximumSalary() != null && !StringUtils.equals(positionInfo.getMaximumSalary(), "")) {
                        BigDecimal maximumSalaryTwo = new BigDecimal(positionInfo.getMaximumSalary());
                        maximumSalaryString = maximumSalaryTwo.divide(new BigDecimal(1000))+"K";
                    }
                }catch (Exception s){
                    log.error("最高薪资转换问题，无需关注");
                }
                try {
                    //最低薪资
                    if(positionInfo.getMinimumWage() != null && !StringUtils.equals(positionInfo.getMinimumWage(), "")) {
                        BigDecimal minimumWageTwo = new BigDecimal(positionInfo.getMinimumWage());
                        minimumWageString = minimumWageTwo.divide(new BigDecimal(1000))+"K";
                    }
                }catch (Exception s){
                    log.error("最低薪资转换问题，无需关注");
                }
                //如果最低最高金额相同，则最高金额赋值为空
                if(!StringUtils.equals(maximumSalaryString, "") && !StringUtils.equals(minimumWageString, "")) {
                    if (!maximumSalaryString.equals(minimumWageString)) {
                        mainAdditionString = minimumWageString+"-"+maximumSalaryString;
                    }else {
                        mainAdditionString = minimumWageString;
                    }
                }else {
                    if(!StringUtils.equals(minimumWageString, "")){
                        mainAdditionString = minimumWageString;
                    }else if(!StringUtils.equals(maximumSalaryString, "")){
                        mainAdditionString = maximumSalaryString;
                    }
                }

                //工作地址
                String workAddress = "无";
                if (positionInfo.getHouseNumber() != null) {
                    workAddress = positionInfo.getHouseNumber();
                } else if (positionInfo.getWorkAddress() != null) {
                    workAddress = positionInfo.getWorkAddress();
                }

                //推文类型
                if(StringUtils.equals(wxTweetArticle.getTweetType(), "1")) {
                    if (StringUtils.equals(content, "")) {
                        //生成小程序连接
                        String positionLink = this.jumpAppletShortUrl("pages/positionDetail/index", "id=" + positionInfo.getId(), 30);
                        content.append("<p>【").append(positionInfo.getPositionName()).append(materialBenefits).append("】").append(minimumWage).append("</p>")
                                .append("<p>").append("企业：").append(positionInfo.getEnterpriseName()).append("</p>")
                                .append("<p>").append("✅ 投简历☞：").append(positionLink).append("</p>");
                    } else {
                        //生成小程序连接
                        String positionLink = this.jumpAppletShortUrl("pages/positionDetail/index", "id=" + positionInfo.getId(), 30);
                        content.append("<br>").append("<p>【").append(positionInfo.getPositionName()).append(materialBenefits).append("】").append(minimumWage).append("</p>")
                                .append("<p>").append("企业：").append(positionInfo.getEnterpriseName()).append("</p>")
                                .append("<p>").append("✅ 投简历☞：").append(positionLink).append("</p>");
                    }
                }else {
                    //公众号推文生成
                    switch (wxTweetArticle.getPublicTemplateType()){
                        case "1":
                            content.append(generateTemplateOne(positionInfo, materialBenefits, minimumWage, mainAdditionString));
                            break;
                        case "2":
                            content.append(generateTemplateTwo(positionInfo, minimumWage, mainAdditionString));
                            break;
                        case "3":
                            content.append(generateTemplateThree(positionInfo, minimumWage, mainAdditionString));
                            break;
                        case "4":
                            content.append(generateTemplateFour(positionInfo, minimumWage, mainAdditionString));
                            break;
                        case "5":
                            content.append(generateTemplateFive(positionInfo, minimumWage, mainAdditionString));
                            break;
                    }

                }
            }
            //推文类型
            if(StringUtils.equals(wxTweetArticle.getTweetType(), "1")) {
                content.append("<br>").append("<p>").append("【点击链接登录，并完善简历，在线投递】#小程序://博汇人才/9PeJMdfZAFJEEqt").append("</p>");
            }
        }else if(StringUtils.isNotEmpty(wxTweetArticle.getEnterpriseInfoIds())){
            for(Long enterpriseInfoId : wxTweetArticle.getEnterpriseInfoIds()){
                List<RecruitPositionInfo> positionInfoList = recruitPositionInfoService.selectRecruitPositionInfoList(new RecruitPositionInfo(){{
                    setEnterpriseId(enterpriseInfoId);
                }});
                positionInfoList.forEach(e->{
                    if(e.getPositionCode() != null && !StringUtils.equals(e.getPositionCode(), "")) {
                        e.setPositionName(recruitPositionService.getMap(e.getPositionCode()));
                    }
                    //公司福利
                    StringBuilder materialBenefits = new StringBuilder();
                    if(e.getMaterialBenefits() != null && !StringUtils.equals(e.getMaterialBenefits(), "")){
                        String[] sss = e.getMaterialBenefits().split(",");
                        for(String s : sss){
                            if(StringUtils.equals(materialBenefits, "")){
                                materialBenefits.append(DictUtils.getDictLabel("company_benefits",s));
                            }else {
                                materialBenefits.append(" | ").append(DictUtils.getDictLabel("company_benefits",s));
                            }
                        }
                    }
                    //薪资
                    String minimumWage = "";
                    //如果最低最高金额相同，则最高金额赋值为空
                    if (e.getMinimumWage() != null && !StringUtils.equals(e.getMinimumWage(), "")) {
                        minimumWage = e.getMinimumWage() + "元/月";
                    }
                    if (e.getMaximumSalary() != null && !StringUtils.equals(e.getMaximumSalary(), "")) {
                        if (!e.getMinimumWage().equals(e.getMaximumSalary())) {
                            minimumWage = e.getMinimumWage() + "-" + e.getMaximumSalary() + "元/月";
                        }
                    }

                    String mainAdditionString = "";
                    String minimumWageString = "";
                    String maximumSalaryString = "";
                    try {
                        //最高薪资
                        if(e.getMaximumSalary() != null && !StringUtils.equals(e.getMaximumSalary(), "")) {
                            BigDecimal maximumSalaryTwo = new BigDecimal(e.getMaximumSalary());
                            maximumSalaryString = maximumSalaryTwo.divide(new BigDecimal(1000))+"K";
                        }
                    }catch (Exception s){
                        log.error("最高薪资转换问题，无需关注");
                    }
                    try {
                        //最低薪资
                        if(e.getMinimumWage() != null && !StringUtils.equals(e.getMinimumWage(), "")) {
                            BigDecimal minimumWageTwo = new BigDecimal(e.getMinimumWage());
                            minimumWageString = minimumWageTwo.divide(new BigDecimal(1000))+"K";
                        }
                    }catch (Exception s){
                        log.error("最低薪资转换问题，无需关注");
                    }
                    //如果最低最高金额相同，则最高金额赋值为空
                    if(!StringUtils.equals(maximumSalaryString, "") && !StringUtils.equals(minimumWageString, "")) {
                        if (!maximumSalaryString.equals(minimumWageString)) {
                            mainAdditionString = minimumWageString+"-"+maximumSalaryString;
                        }else {
                            mainAdditionString = minimumWageString;
                        }
                    }else {
                        if(!StringUtils.equals(minimumWageString, "")){
                            mainAdditionString = minimumWageString;
                        }else if(!StringUtils.equals(maximumSalaryString, "")){
                            mainAdditionString = maximumSalaryString;
                        }
                    }

                    //企业地址
                    String workAddress = "无";
                    if (e.getHouseNumber() != null) {
                        workAddress = e.getHouseNumber();
                    } else if (e.getWorkAddress() != null) {
                        workAddress = e.getWorkAddress();
                    }

                    //推文类型
                    if(StringUtils.equals(wxTweetArticle.getTweetType(), "1")) {
                        if (StringUtils.equals(content, "")) {
                            //生成小程序连接
                            String positionLink = this.jumpAppletShortUrl("pages/positionDetail/index", "id=" + e.getId(), 30);
                            content.append("<p>【").append(e.getPositionName()).append(materialBenefits).append("】").append(minimumWage).append("</p>")
                                    .append("<p>").append("企业：").append(e.getEnterpriseName()).append("</p>")
                                    .append("<p>").append("✅ 投简历☞：").append(positionLink).append("</p>");
                        } else {
                            //生成小程序连接
                            String positionLink = this.jumpAppletShortUrl("pages/positionDetail/index", "id=" + e.getId(), 30);
                            content.append("<br>").append("<p>【").append(e.getPositionName()).append(materialBenefits).append("】").append(minimumWage).append("</p>")
                                    .append("<p>").append("企业：").append(e.getEnterpriseName()).append("</p>")
                                    .append("<p>").append("✅ 投简历☞：").append(positionLink).append("</p>");
                        }
                    }else {
                        //公众号推文生成
                        switch (wxTweetArticle.getPublicTemplateType()){
                            case "1":
                                content.append(generateTemplateOne(e, materialBenefits, minimumWage, mainAdditionString));
                                break;
                            case "2":
                                content.append(generateTemplateTwo(e, minimumWage, mainAdditionString));
                                break;
                            case "3":
                                content.append(generateTemplateThree(e, minimumWage, mainAdditionString));
                                break;
                            case "4":
                                content.append(generateTemplateFour(e, minimumWage, mainAdditionString));
                                break;
                            case "5":
                                content.append(generateTemplateFive(e, minimumWage, mainAdditionString));
                                break;
                        }
                    }
                });
            }
            //推文类型
            if(StringUtils.equals(wxTweetArticle.getTweetType(), "1")) {
                content.append("<br>").append("<p>").append("【点击链接登录，并完善简历，在线投递】#小程序://博汇人才/9PeJMdfZAFJEEqt").append("</p>");
            }
        }
        return success(content);
    }


    /**
     * 模板1
     * @return
     */
    public String generateTemplateOne(RecruitPositionInfo positionInfo, StringBuilder materialBenefits, String minimumWage, String mainAdditionString){
        StringBuilder templateOneInfo = new StringBuilder();
        StringBuilder jobDescription = new StringBuilder();

        if(positionInfo.getJobDescription() != null && !StringUtils.equals(positionInfo.getJobDescription(), "")) {
            jobDescription.append("<section style=\"outline: 0px;display: flex;font-size: 13px;\">")
                    .append("<section style=\"outline: 0px;font-size: inherit;word-break: keep-all;\">工作要求：").append(positionInfo.getJobDescription()).append("</section>")
                    .append("</section>");
        }
        StringBuilder materialBenefitsTwo = new StringBuilder();
        if(!StringUtils.equals(materialBenefits, "")){
            materialBenefitsTwo.append("<p style=\"outline: 0px;\">")
                    .append("<span style=\"outline: 0px;word-break: keep-all;\">").append(materialBenefits).append("</span>")
                    .append("</p>");
        }


        templateOneInfo.append("<section index=\"1\" style=\"margin-bottom: 0px;outline: 0px;\">")
                .append("<p style=\"outline: 0px;\"><br style=\"outline: 0px;\"  /></p>")
                .append("<section style=\"margin: 10px auto;padding-top: 20px;padding-bottom: 25px;outline: 0px;text-align: end;\">")
                .append("<section style=\"margin-right: 10px;outline: 0px;display: inline-block;\">")
                .append("<section style=\"outline: 0px;display: flex;justify-content: flex-end;align-items: flex-end;flex-direction: column;background: rgb(255, 255, 255);transform: rotate(0deg);\">")
                .append("<section style=\"outline: 0px;width: 10px;height: 10px;background-image: -webkit-linear-gradient(right, rgb(130, 78, 255), rgb(82, 127, 255));transform: rotate(0deg);flex-shrink: 0;\">")
                .append("<br style=\"outline: 0px;\"  /></section>")
                .append("<br style=\"outline: 0px;\"  /></section>")
                .append("<section style=\"margin-top: 5px;outline: 0px;width: 10px;height: 10px;background-image: -webkit-linear-gradient(right, rgb(130, 78, 255), rgb(82, 127, 255));transform: rotate(0deg);flex-shrink: 0;\">")
                .append("<br style=\"outline: 0px;\"  />")
                .append("</section></section></section>")
                .append("<section style=\"margin-top: -50px;margin-right: 15px;margin-left: 15px;padding: 10px;outline: 0px;border-width: 1px;border-style: solid;border-color: rgb(108, 121, 255) rgb(152, 109, 255) rgb(114, 135, 255) rgb(153, 180, 255);background: rgb(251, 251, 251);\">")
                .append("<section dacta-autoskip=\"1\" style=\"outline: 0px;text-align: justify;line-height: 1.75em;letter-spacing: 1.5px;font-size: 14px;\">")
                .append("<section style=\"outline: 0px;\">")
                .append("<section style=\"outline: 0px;\">")
                .append("<p style=\"outline: 0px;\">")
                .append("<br style=\"outline: 0px;\"  />")
                .append("</p>")
                .append("<section info=\"[object Object]\" style=\"outline: 0px;text-align: center;\">")
                .append("<section style=\"margin: auto;outline: 0px;width: 53px;\">")
                .append("<img data-ratio=\"0.2868217054263566\" data-type=\"gif\" data-w=\"129\" data-src=\"https://mmbiz.qpic.cn/sz_mmbiz_gif/A0h5MqL6Iibp0WapBzpHcj12ObL6HtAWUY1Mz6pRjynHusXnJ0hYAyBL3TrE0EQlN6o5aCAnmCpWmnAibnFSBK8g/640?wx_fmt=gif&amp;wxfrom=5&amp;wx_lazy=1&amp;wx_co=1\" style=\"outline: 0px;display: block;width: 53px;visibility: visible !important;\"  />")
                .append("</section>")
                .append("<section style=\"margin-top: 6px;outline: 0px;display: inline-block;\">")
                .append("<section style=\"padding-right: 10px;padding-left: 10px;outline: 0px;font-size: 16px;color: rgb(52,209,245);\">")
                .append("<p style=\"outline: 0px;\">").append(positionInfo.getPositionName()).append("</p>")
                .append("</section></section>")
                .append("<p style=\"outline: 0px;\">")
                .append("<br style=\"outline: 0px;\"  />")
                .append("</p>")
                .append("<section style=\"margin-top: -8px;outline: 0px;display: flex;justify-content: center;\">")
                .append("<section style=\"outline: 0px;width: 312.5px;height: 1px;background-color: rgb(52,209,245);\">")
                .append("<br style=\"outline: 0px;\"  />")
                .append("</section></section></section>")
                .append("<p style=\"outline: 0px;\">")
                .append("<br style=\"outline: 0px;\"  />")
                .append("</p>")
                .append("<section style=\"margin-right: auto;margin-left: auto;outline: 0px;text-align: center;font-size: 15px;\">")
                .append("<p style=\"padding: 2px 1em;outline: 0px;display: inline-block;color: rgb(216, 40, 33);\">").append(minimumWage).append("</p>")
                .append("</section>")
                .append("<section style=\"outline: 0px;\">")
                .append("<section align=\"center\" style=\"margin-right: auto;margin-left: auto;outline: 0px;text-align: center;font-size: 13px;\">")
                //福利
                .append(materialBenefitsTwo)

                .append("</section></section>")
                .append("<p style=\"outline: 0px;\">")
                .append("<br style=\"outline: 0px;\"  />")
                .append("</p>")
                .append("<section data-autoskip=\"1\" style=\"margin-right: auto;margin-left: auto;outline: 0px;line-height: 1.75em;\">")
                .append("<section style=\"outline: 0px;display: flex;font-size: 13px;\">")
                .append("<section style=\"outline: 0px;font-size: inherit;word-break: keep-all;\">企业名称：").append(positionInfo.getEnterpriseName()).append("</section>")
                .append("</section>")
                /*.append("<section style=\"outline: 0px;display: flex;font-size: 13px;\">")
                .append("<section style=\"outline: 0px;font-size: inherit;word-break: keep-all;\">公司地址：</section>")
                .append("<section style=\"outline: 0px;flex: 1 1 0%;\"><p style=\"outline: 0px;\">哈密市广东路南粤文化中心</p></section>")
                .append("</section>")*/
                //工作要求
                .append(jobDescription)

                .append("</section></section></section>")
                .append("<p style=\"outline: 0px;\">")
                .append("<br style=\"outline: 0px;\"  />")
                .append("</p>")
                .append("<section index=\"1\" style=\"outline: 0px;\">")
                .append("<section style=\"outline: 0px;text-align: center;\">")
                .append("<p style=\"outline: 0px;\">")
                .append("<a data-miniprogram-type=\"text\" data-miniprogram-nickname=\"哈密博汇人才\" data-miniprogram-appid=\"wx9b1da9361e65e88d\" data-miniprogram-path=\"/pages/positionDetail/index?id=").append(positionInfo.getId()).append("\" ")
                .append("class=\"weapp_text_link wx_tap_link js_wx_tap_highlight\" style=\"padding: 6px 1.4em;outline: 0px;color: rgb(248, 248, 248);cursor: pointer;display: inline-block;font-size: 14px;border-radius: 50px;letter-spacing: 1.5px;background: rgb(0, 54, 251);\">查看详情</a>")
                .append("</p></section></section>")
                .append("<p style=\"outline: 0px;\">")
                .append("<br style=\"outline: 0px;\"  />")
                .append("</p></section></section>")
                .append("<section data-width=\"15%\" style=\"margin-top: -23px;outline: 0px;width: 101.547px;height: 1px;background-image: -webkit-linear-gradient(right, rgb(130, 78, 255), rgb(82, 127, 255));\"><br  /></section>")
                .append("</section></section>")
                .append("<section index=\"2\" style=\"margin-bottom: 0px;outline: 0px;\">")
                .append("<p style=\"outline: 0px;\">")
                .append("<br style=\"outline: 0px;font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif;letter-spacing: 0.544px;text-wrap: wrap;background-color: rgb(255, 255, 255);\"  /></p>")
                .append("</section>")
                .append("<p><br  /></p>")
                .append("<p style=\"display: none;\"><mp-style-type data-value=\"10000\"></mp-style-type></p>");

        return String.valueOf(templateOneInfo);
    }


    /**
     * 模板2
     * @return
     */
    public String generateTemplateTwo(RecruitPositionInfo positionInfo, String minimumWage, String mainAdditionString){
        StringBuilder templateOneInfo = new StringBuilder();
        StringBuilder jobDescription = new StringBuilder();
        //工作描述
        if(positionInfo.getJobDescription() != null && !StringUtils.equals(positionInfo.getJobDescription(), "")) {
            jobDescription.append("<section style=\"outline: 0px;display: flex;font-size: 13px;\">")
                    .append("<section style=\"outline: 0px;font-size: inherit;word-break: keep-all;\">工作要求：").append(positionInfo.getJobDescription()).append("</section>")
                    .append("</section>");
        }

        //福利待遇
        StringBuilder materialBenefits = new StringBuilder();
        if(positionInfo.getMaterialBenefits() != null && !StringUtils.equals(positionInfo.getMaterialBenefits(), "")){
            String[] sss = positionInfo.getMaterialBenefits().split(",");
            for(String s : sss){
                if(StringUtils.equals(materialBenefits, "")){
                    materialBenefits.append("<span style=\"outline: 0px;word-break: keep-all;\">").append(DictUtils.getDictLabel("company_benefits",s)).append("</span>");
                }else {
                    materialBenefits.append("<span style=\"margin-right: 8px;margin-left: 8px;outline: 0px;font-size: 12px;color: rgb(51, 51, 51);\">|</span>")
                            .append("<span style=\"outline: 0px;word-break: keep-all;\">").append(DictUtils.getDictLabel("company_benefits",s)).append("</span>");
                }
            }
        }




        templateOneInfo.append("<p><br /></p>").append("<section style=\"margin-bottom: -56px;margin-left: -10px;outline: 0px;width: 65px;height: 65px;background: rgb(254, 254, 254);transform: rotate(0deg);\">")
                .append("<section style=\"outline: 0px;text-align: left;margin-left: 10px;color: rgb(51, 51, 51);font-size: 22px;letter-spacing: 3px;font-weight: bold;line-height: 56px;\">诚聘</section>")
                .append("<section style=\"margin-top: 5px;margin-left: 10px;outline: 0px;width: 8px;height: 8px;border-radius: 100%;overflow: hidden;background: rgb(0, 54, 251);\"><br style=\"outline: 0px;\"  />")
                .append("</section></section>")
                .append("<section style=\"margin-bottom: 0px;padding-bottom: 5px;outline: 0px;border-radius: 8px;background-color: rgba(0, 54, 251, 0.49);\">")
                .append("<section style=\"margin-top: -10px;margin-right: 4px;margin-left: 4px;padding: 10px 1em 20px;outline: 0px;border-radius: 8px;background-color: rgb(255, 255, 255);border-width: 2px;border-style: solid;border-color: rgb(0, 54, 251);\">")
                .append("</p><section style=\"outline: 0px;\">")
                .append("<section style=\"outline: 0px;\">")
                .append("<section info=\"[object Object]\" style=\"outline: 0px;\">")
                .append("<section style=\"outline: 0px;text-align: center;\">")
                .append("<section style=\"margin-bottom: -25px;outline: 0px;font-size: 38px;letter-spacing: 1.5px;\">")
                .append("<strong style=\"outline: 0px;color: rgba(0, 54, 251, 0.05);\">诚聘</strong>")
                .append("</section><section style=\"outline: 0px;display: inline-block;\">")
                .append("<section style=\"padding: 3px 20px 7px;outline: 0px;background-color: rgba(0, 54, 251, 0.2);\">")
                .append("<section style=\"outline: 0px;font-size: 16px;letter-spacing: 1.5px;color: rgb(0, 54, 251);\">")
                .append("<p style=\"outline: 0px;\">").append(positionInfo.getPositionName()).append("</p>")
                .append("</section>")
                .append("</section>")
                .append("<section style=\"margin-top: -5px;margin-left: 10px;outline: 0px;width: 197.5px;height: 1px;background-color: rgb(0, 54, 251);\">")
                .append("<br style=\"outline: 0px;\"  />")
                .append("</section>")
                .append("</section>")
                .append("</section>")
                .append("</section>")
                .append("<section style=\"margin-right: auto;margin-left: auto;outline: 0px;text-align: center;font-size: 15px;letter-spacing: 1.5px;\">")
                .append("<p style=\"padding: 2px 1em;outline: 0px;display: inline-block;color: rgb(216, 40, 33);\">").append(minimumWage).append("</p>")
                .append("</section>")
                .append("<section style=\"outline: 0px;\">")
                .append("<section align=\"center\" style=\"margin-right: auto;margin-left: auto;outline: 0px;text-align: center;font-size: 13px;letter-spacing: 1.5px;\">")
                .append("<p style=\"outline: 0px;\">")

                //福利待遇
                .append(materialBenefits)

                .append("</p>")
                .append("</section>")
                .append("</section>")
                .append("<section data-autoskip=\"1\" style=\"margin-right: auto;margin-left: auto;outline: 0px;line-height: 1.75em;\">")
                .append("<section style=\"outline: 0px;display: flex;font-size: 13px;\">")
                .append("<section style=\"outline: 0px;font-size: inherit;word-break: keep-all;\">企业名称：").append(positionInfo.getEnterpriseName()).append("</section>")
                .append("</section>")
                ////工作要求
                .append(jobDescription)
                .append("</section></section></section>")
                .append("<p style=\"outline: 0px;\"><br style=\"outline: 0px;\"  /></p>")
                .append("<section index=\"0\" style=\"outline: 0px;\">")
                .append("<section style=\"outline: 0px;text-align: center;\">")
                .append("<p style=\"outline: 0px;\">")
                .append("<a data-miniprogram-type=\"text\" data-miniprogram-nickname=\"哈密博汇人才\" data-miniprogram-appid=\"wx9b1da9361e65e88d\" data-miniprogram-path=\"/pages/positionDetail/index?id=").append(positionInfo.getId()).append("\" ")
                .append("class=\"weapp_text_link wx_tap_link js_wx_tap_highlight\" style=\"padding: 6px 1.4em;outline: 0px;color: rgb(248, 248, 248);cursor: pointer;display: inline-block;font-size: 14px;border-radius: 50px;letter-spacing: 1.5px;background: rgb(0, 54, 251);\">查看详情</a>")
                .append("</p>")
                .append("</section></section></section></section>");
        return String.valueOf(templateOneInfo);
    }

    /**
     * 模板3
     * @return
     */
    public String generateTemplateThree(RecruitPositionInfo positionInfo, String minimumWage, String mainAdditionString){
        StringBuilder templateOneInfo = new StringBuilder();
        StringBuilder jobDescription = new StringBuilder();
        //工作描述
        if(positionInfo.getJobDescription() != null && !StringUtils.equals(positionInfo.getJobDescription(), "")) {
            jobDescription.append("<p style=\"outline: 0px;word-break: break-all;\">")
                    .append("<span style=\"outline: 0px;font-size: 16px;font-family: arial, helvetica, sans-serif;\">")
                    .append("<strong style=\"outline: 0px;\">工作要求：</strong>").append(positionInfo.getJobDescription())
                    .append("</span>")
                    .append("</p>");
        }

        //福利待遇
        StringBuilder materialBenefits = new StringBuilder();
        if(positionInfo.getMaterialBenefits() != null && !StringUtils.equals(positionInfo.getMaterialBenefits(), "")){
            StringBuilder materialBenefitsTwo = new StringBuilder();
            String[] sss = positionInfo.getMaterialBenefits().split(",");
            for(String s : sss){
                if(StringUtils.equals(materialBenefitsTwo, "")){
                    materialBenefitsTwo.append(DictUtils.getDictLabel("company_benefits",s));
                }else {
                    materialBenefitsTwo.append("|").append(DictUtils.getDictLabel("company_benefits",s));
                }
            }
            if(!StringUtils.equals(materialBenefitsTwo, "")) {
                materialBenefits.append("<p style=\"outline: 0px;word-break: break-all;\">")
                        .append("<span style=\"outline: 0px;font-size: 16px;font-family: arial, helvetica, sans-serif;\">")
                        .append("<strong style=\"outline: 0px;\">福利待遇：</strong>").append(materialBenefitsTwo)
                        .append("</span>")
                        .append("</p>");
            }
        }




        templateOneInfo.append("<section data-role=\"outer\" label=\"Powered by 135editor.com\" style=\"margin-bottom: 0em;outline: 0px;letter-spacing: 0.544px;text-wrap: wrap;background-color: rgb(255, 255, 255);color: rgb(34, 34, 34);font-size: medium;text-align: center;font-family: -apple-system-font, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif;visibility: visible;\">\n")
                .append("<section style=\"outline: 0px;visibility: visible;\">")
                .append("<section style=\"outline: 0px;visibility: visible;\">")
                .append("<p style=\"outline: 0px;visibility: visible;\"><br style=\"outline: 0px;\"  /></p>")
                .append("</section></section></section>")
                .append("<section data-tools=\"哈密博汇人才\" data-id=\"160539\" style=\"outline: 0px;text-wrap: wrap;background-color: rgb(255, 255, 255);color: rgb(0, 0, 0);font-family: &quot;Microsoft YaHei&quot;;font-size: medium;letter-spacing: normal;text-align: start;\">\n")
                .append("<section style=\"outline: 0px;\">")
                .append("<section style=\"margin: 10px;outline: 0px;color: rgb(51, 51, 51);\">")
                .append("<section style=\"margin-bottom: -3.6em;margin-left: -6px;outline: 0px;transform: rotate(0deg);\">")
                .append("<section style=\"outline: 0px;display: flex;justify-content: flex-start;align-items: center;\">")
                .append("<section style=\"padding-right: 0.5em;padding-left: 1em;outline: 0px;border-top-left-radius: 5px;background: rgb(17,123,239);height: 30px;letter-spacing: 1.5px;line-height: 30px;display: -webkit-box;overflow: hidden;text-overflow: ellipsis;-webkit-box-orient: vertical;-webkit-line-clamp: 1;word-break: break-all;\">")
                .append("<strong style=\"outline: 0px;\"><span style=\"outline: 0px;font-size: 19px;font-family: arial, helvetica, sans-serif;color: rgb(255,255,255);\">").append(positionInfo.getPositionName()).append("</span></strong>")
                .append("</section>")
                .append("<section style=\"outline: 0px;width: 0px;height: 0px;border-top: 30px solid transparent;border-left: 20px solid rgb(89,162,243);overflow: hidden;\"><br style=\"outline: 0px;\"  /></section>")
                .append("<section style=\"margin-left: -20px;outline: 0px;width: 0px;height: 30px;border-top: 30px solid rgb(89,162,243);border-right: 20px solid transparent;overflow: hidden;\">")
                .append("<br style=\"outline: 0px;\"  />")
                .append("</section></section>")
                .append("<section data-bdless=\"spin\" data-bdlessp=\"280\" data-bdopacity=\"10%\" style=\"outline: 0px;width: 0px;height: 6px;border-bottom: 6px solid transparent;border-right: 6px solid rgb(17,123,239);overflow: hidden;\"><br style=\"outline: 0px;\"  /></section>")
                .append("</section></section>")
                .append("<section style=\"padding: 0.4em;outline: 0px;background-image: initial;background-position: initial;background-size: initial;background-repeat: initial;background-attachment: initial;background-origin: initial;background-clip: initial;border-radius: 20px;box-shadow: rgb(211, 211, 211) 0px 0px 15px;\">")
                .append("<section style=\"padding: 3.4em 1em 1em;outline: 0px;border-width: 1px;border-style: solid;border-color: rgb(17,123,239);border-radius: 15px;\">")
                .append("<section style=\"outline: 0px;text-align: center;\">")
                .append("<section data-autoskip=\"1\" style=\"outline: 0px;text-align: justify;line-height: 1.75em;letter-spacing: 1.5px;font-size: 14px;\">")
                .append("<p style=\"outline: 0px;\">")
                .append("<span style=\"outline: 0px;font-size: 16px;font-family: arial, helvetica, sans-serif;\">")
                .append("<strong style=\"outline: 0px;\">薪资待遇：</strong>")
                .append("<strong style=\"outline: 0px;\">")
                .append("<span style=\"outline: 0px;color: rgb(255, 0, 0);\">").append(minimumWage).append("</span>")
                .append("</strong>")
                .append("</span>")
                .append("</p>")

                //工作描述
                .append(jobDescription)

                .append("<p style=\"outline: 0px;\">")
                .append("<span style=\"outline: 0px;color: rgb(62, 62, 62);white-space-collapse: preserve;caret-color: red;font-size: 16px;font-family: arial, helvetica, sans-serif;\">")
                .append("<strong style=\"outline: 0px;\">招聘企业：</strong>").append(positionInfo.getEnterpriseName())
                .append("</span>")
                .append("</p>")


                .append("<p style=\"outline: 0px;\"><br style=\"outline: 0px;\"  /></p>")
                .append("<p style=\"outline: 0px;text-align: center;letter-spacing: 2px;\">")
                /*.append("<span style=\"outline: 0px;font-size: 15px;font-family: arial, helvetica, sans-serif;\">详情请扫二维码</span>")*/

                .append("<a data-miniprogram-type=\"text\" data-miniprogram-nickname=\"哈密博汇人才\" data-miniprogram-appid=\"wx9b1da9361e65e88d\" data-miniprogram-path=\"/pages/positionDetail/index?id=").append(positionInfo.getId()).append("\" ")
                .append("class=\"weapp_text_link wx_tap_link js_wx_tap_highlight\" style=\"padding: 6px 1.4em;outline: 0px;color: rgb(248, 248, 248);cursor: pointer;display: inline-block;font-size: 14px;border-radius: 50px;letter-spacing: 1.5px;background: rgb(17,123,239);\">查看详情</a>")

                .append("<strong style=\"letter-spacing: 1.5px;font-family: arial, helvetica, sans-serif;font-size: 16px;text-align: justify;outline: 0px;\">")
                .append("<span style=\"outline: 0px;color: rgb(255, 0, 0);\"></span>")
                .append("</strong>")
                .append("</p>")
                .append("</section></section></section></section></section></section></section>");

        return String.valueOf(templateOneInfo);
    }


    /**
     * 模板4
     * @return
     */
    public String generateTemplateFour(RecruitPositionInfo positionInfo, String minimumWage, String mainAdditionString){
        StringBuilder templateOneInfo = new StringBuilder();
        StringBuilder jobDescription = new StringBuilder();
        //工作描述
        if(positionInfo.getJobDescription() != null && !StringUtils.equals(positionInfo.getJobDescription(), "")) {
            jobDescription.append("<p style=\"outline: 0px;\">")
                    .append("<span style=\"outline: 0px;\">工作要求：").append(positionInfo.getJobDescription()).append("</span>")
                    .append("</p>");
        }

        //福利待遇
        StringBuilder materialBenefits = new StringBuilder();
        if(positionInfo.getMaterialBenefits() != null && !StringUtils.equals(positionInfo.getMaterialBenefits(), "")){
            StringBuilder materialBenefitsTwo = new StringBuilder();
            String[] sss = positionInfo.getMaterialBenefits().split(",");
            for(String s : sss){
                if(StringUtils.equals(materialBenefitsTwo, "")){
                    materialBenefitsTwo.append(DictUtils.getDictLabel("company_benefits",s));
                }else {
                    materialBenefitsTwo.append("|").append(DictUtils.getDictLabel("company_benefits",s));
                }
            }
            if(!StringUtils.equals(materialBenefitsTwo, "")) {
                materialBenefits.append("<p style=\"outline: 0px;\">")
                        .append("<span style=\"outline: 0px;\">福利待遇：").append(materialBenefitsTwo).append("</span>")
                        .append("</p>");
            }
        }


        templateOneInfo.append("<section data-id=\"97125\" data-tools=\"135编辑器\" data-width=\"100%\" style=\"margin-right: auto;margin-bottom: 0px;margin-left: auto;outline: 0px;font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif;text-wrap: wrap;letter-spacing: 0.578px;width: 578px;flex: 0 0 100%;visibility: visible;\">\n")
                .append("<section style=\"margin: 10px auto;outline: 0px;text-align: center;visibility: visible;\">")
                .append("<section style=\"padding: 5px;outline: 0px;background: rgb(254, 254, 254);border-radius: 100%;border-width: 1px;border-style: solid;border-color: rgb(0, 112, 192);display: inline-block;transform: rotate(0deg);visibility: visible;\">")
                .append("<section data-brushtype=\"text\" style=\"outline: 0px;background: rgb(17,123,239);border-radius: 100%;width: 60px;height: 60px;color: rgb(255, 255, 255);line-height: 58px;font-size: 22px;visibility: visible;\">诚聘</section>")
                .append("</section>")
                .append("<section style=\"margin-top: -35px;padding-top: 5px;padding-right: 5px;padding-left: 5px;outline: 0px;border-width: 1px 1px medium;border-style: solid solid none;border-color: rgb(0, 112, 192) rgb(0, 112, 192) currentcolor;border-top-left-radius: 10px;border-top-right-radius: 10px;visibility: visible;\">")
                .append("<section style=\"padding-top: 5px;padding-right: 5px;padding-left: 5px;outline: 0px;border-width: 1px 1px medium;border-style: dashed dashed none;border-color: rgb(0, 112, 192) rgb(0, 112, 192) currentcolor;height: 40px;overflow: hidden;border-top-left-radius: 10px;border-top-right-radius: 10px;visibility: visible;\">")
                .append("<br style=\"outline: 0px;visibility: visible;\"  />")
                .append("</section></section>")
                .append("<section data-brushtype=\"text\" style=\"padding: 10px 6px;outline: 0px;background: rgb(0, 112, 192);letter-spacing: 1.5px;font-size: 18px;visibility: visible;\">")
                .append("<p style=\"outline: 0px;visibility: visible;\">")
                .append("<strong style=\"outline: 0px;visibility: visible;color:rgb(248, 248, 248)\">").append(positionInfo.getPositionName()).append("（").append(mainAdditionString).append("）</strong>")
                .append("</p></section>")
                .append("<section style=\"padding-right: 5px;padding-bottom: 5px;padding-left: 5px;outline: 0px;border-width: medium 1px 1px;border-style: none solid solid;border-color: currentcolor rgb(0, 112, 192) rgb(0, 112, 192);border-bottom-right-radius: 10px;border-bottom-left-radius: 10px;visibility: visible;\">")
                .append("<section style=\"padding-right: 5px;padding-bottom: 5px;padding-left: 5px;outline: 0px;border-width: medium 1px 1px;border-style: none dashed dashed;border-color: currentcolor rgb(0, 112, 192) rgb(0, 112, 192);border-bottom-right-radius: 10px;border-bottom-left-radius: 10px;visibility: visible;\">")
                .append("<section data-autoskip=\"1\" style=\"padding: 1em;outline: 0px;text-align: justify;letter-spacing: 1.5px;font-size: 14px;visibility: visible;\">")
                .append("<p style=\"outline: 0px;color: rgb(34, 34, 34);font-family: -apple-system-font, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif;letter-spacing: 0.5px;text-align: left;visibility: visible;\">")
                .append("<span style=\"outline: 0px;letter-spacing: 1.5px;text-align: justify;font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif;color: rgba(0, 0, 0, 0.9);\">").append("职位名称：").append(positionInfo.getPositionName()).append("（").append(minimumWage).append("）</span>")
                .append("<span style=\"outline: 0px;letter-spacing: 1.5px;text-align: justify;font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif;color: rgba(0, 0, 0, 0.9);\"></span>")
                .append("</p>")
                .append("<p style=\"outline: 0px;\">")
                .append("<span style=\"outline: 0px;\">").append("企业名称：").append(positionInfo.getEnterpriseName()).append("</span>")
                .append("</p>")
                //工作描述
                .append(jobDescription)
                //福利待遇
                .append(materialBenefits)

                .append("<p style=\"outline: 0px;\"><br style=\"outline: 0px;\"  /></p>")
                .append("<p style=\"outline: 0px;text-align: center;letter-spacing: 2px;\">")

                .append("<a data-miniprogram-type=\"text\" data-miniprogram-nickname=\"哈密博汇人才\" data-miniprogram-appid=\"wx9b1da9361e65e88d\" data-miniprogram-path=\"/pages/positionDetail/index?id=").append(positionInfo.getId()).append("\" ")
                .append("class=\"weapp_text_link wx_tap_link js_wx_tap_highlight\" style=\"padding: 6px 1.4em;outline: 0px;color: rgb(248, 248, 248);cursor: pointer;display: inline-block;font-size: 14px;border-radius: 50px;letter-spacing: 1.5px;background: rgb(17,123,239);\">查看详情</a>")


                .append("</p>")
                .append("<p style=\"outline: 0px;\">")
                .append("<span class=\"mp-caret\"></span>")
                .append("<span style=\"outline: 0px;color: rgb(255, 79, 121);font-size: 16px;\"></span>")
                .append("</p>")
                .append("</section></section></section></section></section>")
                .append("<p><br/></p>")
                .append("<p style=\"display: none;\">")
                .append("<mp-style-type data-value=\"10000\"></mp-style-type>")
                .append("</p>");
        return String.valueOf(templateOneInfo);
    }


    /**
     * 模板5
     * @return
     */
    public String generateTemplateFive(RecruitPositionInfo positionInfo, String minimumWage, String mainAdditionString){
        StringBuilder templateOneInfo = new StringBuilder();
        StringBuilder jobDescription = new StringBuilder();
        //工作描述
        if(positionInfo.getJobDescription() != null && !StringUtils.equals(positionInfo.getJobDescription(), "")) {
            jobDescription.append("<p style=\"outline: 0px;visibility: visible;\">")
                    .append("<strong style=\"outline: 0px;visibility: visible;\">工作要求：</strong>").append(positionInfo.getJobDescription())
                    .append("</p>");
        }

        //福利待遇
        StringBuilder materialBenefits = new StringBuilder();
        if(positionInfo.getMaterialBenefits() != null && !StringUtils.equals(positionInfo.getMaterialBenefits(), "")){
            StringBuilder materialBenefitsTwo = new StringBuilder();
            String[] sss = positionInfo.getMaterialBenefits().split(",");
            for(String s : sss){
                if(StringUtils.equals(materialBenefitsTwo, "")){
                    materialBenefitsTwo.append(DictUtils.getDictLabel("company_benefits",s));
                }else {
                    materialBenefitsTwo.append("|").append(DictUtils.getDictLabel("company_benefits",s));
                }
            }
            if(!StringUtils.equals(materialBenefitsTwo, "")) {
                materialBenefits.append("<p style=\"outline: 0px;visibility: visible;\">")
                        .append("<strong style=\"outline: 0px;visibility: visible;\">福利待遇：</strong>").append(materialBenefitsTwo)
                        .append("</p>");
            }
        }


        templateOneInfo.append("<p> <br/> </p>").append("<section powered-by=\"xiumi.us\" style=\"margin-bottom: 0px;outline: 0px;font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif;letter-spacing: 0.544px;text-wrap: wrap;background-color: rgb(255, 255, 255);visibility: visible;\">\n")
                .append("<section style=\"margin-bottom: 15px;outline: 0px;display: inline-block;width: 677px;vertical-align: top;border-width: 1px;border-style: solid;border-color: rgb(0, 112, 192);background-color: rgba(234, 73, 73, 0.05);visibility: visible;\">")
                .append("<section powered-by=\"xiumi.us\" style=\"outline: 0px;text-align: center;justify-content: center;visibility: visible;\">")
                .append("<section style=\"padding-right: 12px;padding-left: 12px;outline: 0px;display: inline-block;width: auto;vertical-align: top;min-width: 10%;height: auto;background-color: rgb(0, 112, 192);visibility: visible;\">")
                .append("<section powered-by=\"xiumi.us\" style=\"outline: 0px;color: rgb(255, 255, 255);visibility: visible;\">")
                .append("<p style=\"outline: 0px;visibility: visible;\">").append(positionInfo.getPositionName()).append("（").append(mainAdditionString).append("）</p>")
                .append("</section></section></section>")
                .append("<section powered-by=\"xiumi.us\" style=\"margin-top: -10px;margin-bottom: 10px;outline: 0px;visibility: visible;\">")
                .append("<section style=\"padding-top: 10px;padding-right: 10px;padding-left: 10px;outline: 0px;display: inline-block;width: 675px;vertical-align: top;visibility: visible;\">")
                .append("<section powered-by=\"xiumi.us\" style=\"outline: 0px;visibility: visible;\">")
                .append("<p> <br/> </p>")
                .append("<p style=\"outline: 0px;visibility: visible;\">")
                .append("<strong style=\"outline: 0px;visibility: visible;\">薪资详情：</strong>").append(minimumWage)
                .append("</p>")
                .append("<p style=\"outline: 0px;visibility: visible;\">")
                .append("<strong style=\"outline: 0px;visibility: visible;\">企业名称：</strong>").append(positionInfo.getEnterpriseName()).append("&nbsp;")
                .append("</p>")
                //工作描述
                .append(jobDescription)
                //福利待遇
                .append(materialBenefits)

                .append("<p> <br/> </p>")
                .append("<p style=\"outline: 0px;\"><br style=\"outline: 0px;\"  /></p>")
                .append("<p style=\"outline: 0px;visibility: visible;text-align: center;\">")
                .append("<a data-miniprogram-type=\"text\" data-miniprogram-nickname=\"哈密博汇人才\" data-miniprogram-appid=\"wx9b1da9361e65e88d\" data-miniprogram-path=\"/pages/positionDetail/index?id=").append(positionInfo.getId()).append("\" ")
                .append("class=\"weapp_text_link wx_tap_link js_wx_tap_highlight\" style=\"padding: 6px 1.4em;outline: 0px;color: rgb(248, 248, 248);cursor: pointer;display: inline-block;font-size: 14px;border-radius: 50px;letter-spacing: 1.5px;background: rgb(17,123,239);\">查看详情</a>")
                .append("</p>")
                .append("</section></section></section></section></section>")
                .append("<p style=\"display: none;\">")
                .append("<mp-style-type data-value=\"10000\"></mp-style-type>")
                .append("</p>")
                .append("<p> <br/> </p>");
        return String.valueOf(templateOneInfo);
    }
    /**
     * 生成小程序跳转短链 code
     * @param path 小程序页面路径 null为首页
     * @param query 与前端商量，携带的参数
     * @return
     */
    public String jumpAppletShortUrl(String path,String query,Integer days){
        String accessToken = null;
        try {
            accessToken = this.wxService.getAccessToken(true);
        } catch (WxErrorException e) {
            throw new RuntimeException(e);
        }
        String url= linkUrl.replace("ACCESS_TOKEN",accessToken);
        JSONObject body = JSONUtil.createObj();
        body.putOpt("path",path);
        body.putOpt("query",query);
        //链接过期类型：0时间戳 1间隔天数
        body.putOpt("expire_type",1);
        //指定失效天数，最多30
        days=(days==null||days>30)?30:days;
        body.putOpt("expire_interval",days);
        //小程序版本，正式版为 "release"，体验版为"trial"，开发版为"develop"
        body.putOpt("env_version","release");
        String post = HttpUtil.post(url, body.toJSONString(2));
        JSONObject jsonObject = JSONUtil.parseObj(post);
        return jsonObject.get("url_link", String.class, false);
    }


}
