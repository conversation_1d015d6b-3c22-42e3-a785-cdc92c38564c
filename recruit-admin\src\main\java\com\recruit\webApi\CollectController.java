package com.recruit.webApi;

import com.recruit.common.core.controller.BaseController;
import com.recruit.common.core.domain.AjaxResult;
import com.recruit.common.core.page.TableDataInfo;
import com.recruit.common.utils.DictUtils;
import com.recruit.common.utils.StringUtils;
import com.recruit.core.domain.RecruitCollect;
import com.recruit.core.domain.request.other.CompanyRequest;
import com.recruit.core.domain.request.other.PositionRequest;
import com.recruit.core.domain.request.other.UsersRequest;
import com.recruit.core.domain.response.CollectionCompanyResponse;
import com.recruit.core.domain.response.CollectionPositionResponse;
import com.recruit.core.domain.response.FavoriteUsersResponse;
import com.recruit.core.service.IRecruitCollectService;
import com.recruit.core.service.IRecruitPositionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Auther: Wu kong
 * @Date: 2023/4/12 15:33
 * @Description:
 */
@Api(tags= "(04-12)收藏接口")
@RestController
@RequestMapping("/web/api/collect")
public class CollectController extends BaseController {

    @Autowired
    private IRecruitCollectService recruitCollectService;

    @Autowired
    private IRecruitPositionService recruitPositionService;


    @ApiOperation("收藏职位（求职者使用）")
    @PostMapping("/collectionPosition")
    public AjaxResult collectionPosition(@RequestBody PositionRequest request)
    {
        RecruitCollect collect = new RecruitCollect();
        collect.setPositionInfoId(request.getPositionInfoId());
        collect.setUserId(getUserId());
        collect.setCollectType("1");
        return toAjax(recruitCollectService.insertRecruitCollect(collect));
    }

    @ApiOperation("查询收藏职位（求职者使用）")
    @GetMapping("/getCollectionPosition")
    public TableDataInfo getCollectionPosition(@NotNull(message = "页码不能为空") @RequestParam Integer page,
                                              @NotNull(message = "size不能为空") @RequestParam Integer size){
        startPageTwo(page, size);
        RecruitCollect collect = new RecruitCollect();
        collect.setUserId(getUserId());
        collect.setCollectType("1");
        List<CollectionPositionResponse> list = recruitCollectService.getCollectionPosition(collect);
        list.forEach(e->{
            if(e.getPositionCode() != null && !StringUtils.equals(e.getPositionCode(), "")) {
                e.setPositionName(recruitPositionService.getMap(e.getPositionCode()));
            }
            if(e.getWorkExperience() != null && !StringUtils.equals(e.getWorkExperience(), "")){
                e.setWorkExperienceName(DictUtils.getDictLabel("work_experience", e.getWorkExperience()));
                if(e.getWorkExperienceName().equals("不限")){
                    e.setWorkExperienceName("经验不限");
                }
            }else {
                e.setWorkExperienceName("经验不限");
            }
            if(e.getMinimumEducation() != null && !StringUtils.equals(e.getMinimumEducation(), "")) {
                e.setMinimumEducationName(DictUtils.getDictLabel("background_type", e.getMinimumEducation()));
                if (e.getMinimumEducationName().equals("不限")) {
                    e.setMinimumEducationName("学历不限");
                }
            }else {
                e.setMinimumEducationName("学历不限");
            }
        });
        return getDataTable(list);
    }

    @ApiOperation("查询收藏职位数（求职者使用）")
    @GetMapping("/getCollectionPositionNum")
    public AjaxResult getCollectionPositionNum(){
        RecruitCollect collect = new RecruitCollect();
        collect.setUserId(getUserId());
        collect.setCollectType("1");
        return success(recruitCollectService.getCollectionPositionNum(collect));
    }


    @ApiOperation("收藏公司（求职者使用）")
    @PostMapping("/collectionCompany")
    public AjaxResult collectionCompany(@RequestBody CompanyRequest request)
    {
        RecruitCollect collect = new RecruitCollect();
        collect.setEnterpriseId(request.getEnterpriseId());
        collect.setUserId(getUserId());
        collect.setCollectType("3");
        return toAjax(recruitCollectService.insertRecruitCollect(collect));
    }

    @ApiOperation("查询收藏公司（求职者使用）")
    @GetMapping("/getCollectionCompany")
    public TableDataInfo getCollectionCompany(@NotNull(message = "页码不能为空") @RequestParam Integer page,
                                              @NotNull(message = "size不能为空") @RequestParam Integer size){
        startPageTwo(page, size);
        RecruitCollect collect = new RecruitCollect();
        collect.setUserId(getUserId());
        collect.setCollectType("3");
        List<CollectionCompanyResponse> list = recruitCollectService.getCollectionCompany(collect);
        list.forEach(e->{
            if(e.getScale() != null && !StringUtils.equals(e.getScale(), "")) {
                e.setScaleName(DictUtils.getDictLabel("scale_enterprises", e.getScale()));
                if(e.getScaleName().equals("不限")){
                    e.setScaleName("规模不限");
                }
            }else {
                e.setScaleName("规模不限");
            }
            if(e.getEnterpriseNature() != null && !StringUtils.equals(e.getEnterpriseNature(), "")) {
                e.setEnterpriseNatureName(DictUtils.getDictLabel("enterprise_nature", e.getEnterpriseNature()));
            }
        });
        return getDataTable(list);
    }

    @ApiOperation("查询收藏公司数（求职者使用）")
    @GetMapping("/getCollectionCompanyNum")
    public AjaxResult getCollectionCompanyNum(){
        RecruitCollect collect = new RecruitCollect();
        collect.setUserId(getUserId());
        collect.setCollectType("3");
        return success(recruitCollectService.getCollectionCompanyNum(collect));
    }



    @ApiOperation("收藏用户（招聘者使用）")
    @PostMapping("/favoriteUsers")
    public AjaxResult favoriteUsers(@RequestBody UsersRequest request)
    {
        RecruitCollect collect = new RecruitCollect();
        collect.setUserId(request.getUserId());
        collect.setPublisherId(getUserId());
        collect.setCollectType("2");
        return toAjax(recruitCollectService.insertRecruitCollect(collect));
    }

    @ApiOperation("查询收藏用户（招聘者使用）")
    @GetMapping("/getFavoriteUsersList")
    public TableDataInfo getFavoriteUsersList(@NotNull(message = "页码不能为空") @RequestParam Integer page,
                                              @NotNull(message = "size不能为空") @RequestParam Integer size){

        startPageTwo(page, size);
        RecruitCollect collect = new RecruitCollect();
        collect.setPublisherId(getUserId());
        collect.setCollectType("2");
        List<FavoriteUsersResponse> list = recruitCollectService.getFavoriteUsersList(collect);
        list.forEach(e->{
            if(e.getWorkExperience() != null && !StringUtils.equals(e.getWorkExperience(), "")){
                e.setWorkExperienceName(DictUtils.getDictLabel("work_experience_two", e.getWorkExperience()));
                if(e.getWorkExperienceName().equals("不限")){
                    e.setWorkExperienceName("经验不限");
                }
            }else {
                e.setWorkExperienceName("经验不限");
            }
            if(e.getEducation() != null && !StringUtils.equals(e.getEducation(), "")) {
                e.setEducationName(DictUtils.getDictLabel("background_type", e.getEducation()));
                if (e.getEducationName().equals("不限")) {
                    e.setEducationName("学历不限");
                }
            }else {
                e.setEducationName("学历不限");
            }
        });
        return getDataTable(list);
    }

    @ApiOperation("查询收藏用户数（招聘者使用）")
    @GetMapping("/getFavoriteUsersNum")
    public AjaxResult getFavoriteUsersNum(){

        RecruitCollect collect = new RecruitCollect();
        collect.setPublisherId(getUserId());
        collect.setCollectType("2");
        return success(recruitCollectService.getFavoriteUsersNum(collect));
    }


    @ApiOperation(value = "取消收藏",notes="取消收藏")
    @DeleteMapping("/cancelFavorite/{id}")
    public AjaxResult cancelFavorite(@NotNull(message = "消息id不能为空") @PathVariable Long id){
        return success(recruitCollectService.deleteRecruitCollectById(id));
    }



}
