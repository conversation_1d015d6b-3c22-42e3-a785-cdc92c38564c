package com.recruit.core.domain.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Auther: <PERSON> kong
 * @Date: 2023/6/2 9:04
 * @Description:
 */
@Data
public class MerchantInfoResponse {

    @ApiModelProperty("服务行业编码")
    private String serviceTrade;

    @ApiModelProperty("服务行业名称")
    private String serviceTradeName;

    @ApiModelProperty("商户名称")
    private String serviceName;

    @ApiModelProperty("申请人姓名")
    private String userName;

    @ApiModelProperty("申请人姓名")
    private Long userId;

    @ApiModelProperty("申请状态")
    private String applicationStatus;

    @ApiModelProperty("所在地区")
    private String region;

    @ApiModelProperty("详细地址")
    private String address;

    @ApiModelProperty("经度")
    private String longitude;

    @ApiModelProperty("纬度")
    private String latitude;

    @ApiModelProperty("账户金额")
    private BigDecimal accountAmount;

    @ApiModelProperty("总收入")
    private BigDecimal totalIncome;

}
