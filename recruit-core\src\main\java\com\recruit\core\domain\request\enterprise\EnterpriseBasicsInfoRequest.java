package com.recruit.core.domain.request.enterprise;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;


/**
 * @Auther: <PERSON> kong
 * @Date: 2023/3/21 16:49
 * @Description:
 */
@Data
@ApiModel("企业基本信息1")
public class EnterpriseBasicsInfoRequest {

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("企业logo")
    private String enterpriseLogo;

    @NotBlank(message = "公司名称不能为空")
    @ApiModelProperty("公司名称")
    private String enterpriseName;

    @NotBlank(message = "从事行业不能为空")
    @ApiModelProperty("从事行业")
    private String industry;

    @ApiModelProperty("企业性质")
    private String enterpriseNature;

    @ApiModelProperty("企业规模")
    private String scale;

    @ApiModelProperty("所在地区")
    private String region;

    @ApiModelProperty("所在地区")
    private List<String> regions;

    @ApiModelProperty("详细地址")
    private String detailAddress;

    @ApiModelProperty("公司简称")
    private String abbreviation;

    @ApiModelProperty("公司福利")
    private String materialBenefits;

    @ApiModelProperty("企业邮箱")
    private String enterpriseMailbox;

    @ApiModelProperty("企业网址")
    private String companyWebsite;

    @ApiModelProperty("注册资金")
    private String registeredCapital;

    @ApiModelProperty("融资阶段")
    private String financingStage;

    @ApiModelProperty("公司简介")
    private String profile;

    @ApiModelProperty("企业地图经度")
    private String enterpriseMapLongitude;

    @ApiModelProperty("企业地图纬度")
    private String enterpriseMapLatitude;


}
