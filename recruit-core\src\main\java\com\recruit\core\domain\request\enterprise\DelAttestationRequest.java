package com.recruit.core.domain.request.enterprise;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Auther: <PERSON> kong
 * @Date: 2023/5/6 10:40
 * @Description:
 */
@Data
@ApiModel("解除同事绑定关系1")
public class DelAttestationRequest {

    @NotNull(message = "企业id不能为空")
    @ApiModelProperty("企业id")
    private Long enterpriseId;

    @NotNull(message = "用户id不能为空")
    @ApiModelProperty("用户id")
    private Long userId;
}
