package com.recruit.core.domain.request.talent;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("家庭成员")
public class FamilyRequest {

    @ApiModelProperty("称谓")
    private String appellation;

    @ApiModelProperty("姓名")
    private String name;

    @ApiModelProperty("出生年月")
    private String birthday;

    @ApiModelProperty("工作单位或学校")
    private String work_place;

    @ApiModelProperty("职务")
    private String job_name;

}
