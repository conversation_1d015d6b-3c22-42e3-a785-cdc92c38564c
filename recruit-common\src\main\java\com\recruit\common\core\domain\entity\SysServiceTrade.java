package com.recruit.common.core.domain.entity;

import com.recruit.common.annotation.Excel;
import com.recruit.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 服务行业对象 sys_service_trade
 *
 * <AUTHOR>
 * @date 2023-05-27
 */
@Data
@ApiModel("服务行业")
public class SysServiceTrade extends BaseEntity
{
    private static final long serialVersionUID = 1L;


    private Long id;

    private Long parentId;
    @Excel(name = "code")
    @ApiModelProperty("code")
    private String code;


    @Excel(name = "名称")
    @ApiModelProperty("名称")
    private String name;


    @Excel(name = "父code")
    @ApiModelProperty("父code")
    private String fCode;


    @Excel(name = "祖级列表")
    @ApiModelProperty("祖级列表")
    private String ancestors;


    @Excel(name = "显示顺序")
    @ApiModelProperty("显示顺序")
    private Integer orderNum;

    @ApiModelProperty("类型 1下单 2预约")
    private String type;

    @ApiModelProperty("资料描述")
    private String dataDescribe;

    @ApiModelProperty("是否禁用")
    private String display;

    @ApiModelProperty("行业logo")
    private String tradeLogo;

    /** 子部门 */
    private List<SysServiceTrade> children = new ArrayList<SysServiceTrade>();
}
