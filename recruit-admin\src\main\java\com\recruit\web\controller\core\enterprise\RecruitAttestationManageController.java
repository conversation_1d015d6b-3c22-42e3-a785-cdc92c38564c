package com.recruit.web.controller.core.enterprise;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.recruit.common.annotation.Log;
import com.recruit.common.core.controller.BaseController;
import com.recruit.common.core.domain.AjaxResult;
import com.recruit.common.enums.BusinessType;
import com.recruit.core.domain.RecruitAttestationManage;
import com.recruit.core.service.IRecruitAttestationManageService;
import com.recruit.common.utils.poi.ExcelUtil;
import com.recruit.common.core.page.TableDataInfo;

/**
 * 企业认证管理Controller
 *
 * <AUTHOR>
 * @date 2023-05-04
 */
@RestController
@RequestMapping("/core/attestationManage")
public class RecruitAttestationManageController extends BaseController
{
    @Autowired
    private IRecruitAttestationManageService recruitAttestationManageService;

    /**
     * 查询企业认证管理列表
     */
    @PreAuthorize("@ss.hasPermi('core:attestationManage:list')")
    @GetMapping("/list")
    public TableDataInfo list(RecruitAttestationManage recruitAttestationManage)
    {
        startPage();
        List<RecruitAttestationManage> list = recruitAttestationManageService.selectRecruitAttestationManageList(recruitAttestationManage);
        return getDataTable(list);
    }

    /**
     * 导出企业认证管理列表
     */
    @PreAuthorize("@ss.hasPermi('core:attestationManage:export')")
    @Log(title = "企业认证管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RecruitAttestationManage recruitAttestationManage)
    {
        List<RecruitAttestationManage> list = recruitAttestationManageService.selectRecruitAttestationManageList(recruitAttestationManage);
        ExcelUtil<RecruitAttestationManage> util = new ExcelUtil<RecruitAttestationManage>(RecruitAttestationManage.class);
        util.exportExcel(response, list, "企业认证管理数据");
    }

    /**
     * 获取企业认证管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('core:attestationManage:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(recruitAttestationManageService.selectRecruitAttestationManageById(id));
    }

    /**
     * 新增企业认证管理
     */
    @PreAuthorize("@ss.hasPermi('core:attestationManage:add')")
    @Log(title = "企业认证管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RecruitAttestationManage recruitAttestationManage)
    {
        return toAjax(recruitAttestationManageService.insertRecruitAttestationManage(recruitAttestationManage));
    }

    /**
     * 修改企业认证管理
     */
    @PreAuthorize("@ss.hasPermi('core:attestationManage:edit')")
    @Log(title = "企业认证管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RecruitAttestationManage recruitAttestationManage)
    {
        return toAjax(recruitAttestationManageService.updateRecruitAttestationManage(recruitAttestationManage));
    }

    /**
     * 删除企业认证管理
     */
    @PreAuthorize("@ss.hasPermi('core:attestationManage:remove')")
    @Log(title = "企业认证管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(recruitAttestationManageService.deleteRecruitAttestationManageByIds(ids));
    }
}
