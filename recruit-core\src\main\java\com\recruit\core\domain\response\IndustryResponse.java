package com.recruit.core.domain.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Auther: <PERSON> kong
 * @Date: 2023/3/20 10:50
 * @Description:
 */
@Data
@ApiModel("行业分类")
public class IndustryResponse {

    @ApiModelProperty("Code")
    private String code;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("下级列表")
    private List<IndustryResponse> subLevelModelList;
}
