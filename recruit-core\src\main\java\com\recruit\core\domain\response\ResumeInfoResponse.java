package com.recruit.core.domain.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.recruit.core.domain.RecruitResumeBasicInfo;
import com.recruit.core.domain.RecruitResumeEducation;
import com.recruit.core.domain.RecruitResumeJobDesire;
import com.recruit.core.domain.RecruitResumeOtherInfo;
import com.recruit.core.domain.RecruitResumeProfessionalSkills;
import com.recruit.core.domain.RecruitResumeProjectExperience;
import com.recruit.core.domain.RecruitResumeWorkHistory;
import com.recruit.core.domain.RecruitUserInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Auther: Wu kong
 * @Date: 2023/3/21 12:51
 * @Description:
 */
@Data
@ApiModel("在线简历信息返回")
public class ResumeInfoResponse {

    @ApiModelProperty("企业收藏用户")
    private int favoriteUsers;

    @ApiModelProperty("收藏表id")
    private Long collectId;
    @ApiModelProperty("个人信息")
    private RecruitUserInfo recruitUserInfo;

    @ApiModelProperty("基本资料")
    private RecruitResumeBasicInfo resumeBasicInfo;

    @ApiModelProperty("求职意愿")
    private RecruitResumeJobDesire resumeJobDesire;

    @ApiModelProperty("其它信息，自我评价")
    private RecruitResumeOtherInfo resumeOtherInfo;

    @ApiModelProperty("职业技能")
    private List<RecruitResumeProfessionalSkills> resumeProfessionalSkillsList;

    @ApiModelProperty("工作经历")
    private List<RecruitResumeWorkHistory> resumeWorkHistoryList;

    @ApiModelProperty("项目经历")
    private List<RecruitResumeProjectExperience> resumeProjectExperienceList;


    @ApiModelProperty("教育经历")
    private List<RecruitResumeEducation> resumeEducationList;
}
