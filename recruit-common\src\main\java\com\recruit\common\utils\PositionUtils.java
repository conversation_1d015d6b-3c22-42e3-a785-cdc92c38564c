package com.recruit.common.utils;

import com.recruit.common.constant.Constants;
import com.recruit.common.core.redis.RedisCache;
import com.recruit.common.utils.spring.SpringUtils;

import java.util.Collection;

/**
 * @Auther: <PERSON> kong
 * @Date: 2023/4/2 23:38
 * @Description:
 */
public class PositionUtils {

    /**
     * 设置地区缓存
     *
     * @param key 参数键
     * @param position 字典数据列表
     */
    public static void setPositionCache(String key, String position)
    {
        SpringUtils.getBean(RedisCache.class).setCacheObject(getPositionKey(key), position);
    }


    /**
     * 获取的确缓存
     *
     * @param key 参数键
     * @return dictDatas 字典数据列表
     */
    public static String getPositionCache(String key)
    {
        Object cacheObj = SpringUtils.getBean(RedisCache.class).getCacheObject(getPositionKey(key));
        if (StringUtils.isNotNull(cacheObj))
        {
            return StringUtils.cast(cacheObj);
        }
        return null;
    }


    /**
     * 删除指定字典缓存
     *
     * @param key 字典键
     */
    public static void removeUserCache(String key)
    {
        SpringUtils.getBean(RedisCache.class).deleteObject(getPositionKey(key));
    }

    /**
     * 清空字典缓存
     */
    public static void clearUserCache()
    {
        Collection<String> keys = SpringUtils.getBean(RedisCache.class).keys(Constants.POSITION + "*");
        SpringUtils.getBean(RedisCache.class).deleteObject(keys);
    }

    /**
     * 设置
     * @param configKey
     * @return
     */
    public static String getPositionKey(String configKey)
    {
        return Constants.POSITION + configKey;
    }
}
