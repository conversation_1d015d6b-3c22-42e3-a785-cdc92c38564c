package com.recruit.web.controller.core.common;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.recruit.common.core.domain.TreeSelect;
import com.recruit.common.core.domain.entity.SysRegion;
import com.recruit.common.utils.StringUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.recruit.common.annotation.Log;
import com.recruit.common.core.controller.BaseController;
import com.recruit.common.core.domain.AjaxResult;
import com.recruit.common.enums.BusinessType;
import org.springframework.web.multipart.MultipartFile;
import com.recruit.core.service.ISysRegionService;
import com.recruit.common.utils.poi.ExcelUtil;

/**
 * 系统区域Controller
 *
 * <AUTHOR>
 * @date 2023-06-13
 */
@RestController
@RequestMapping("/core/region")
public class SysRegionController extends BaseController
{
    @Autowired
    private ISysRegionService sysRegionService;

    /**
     * 查询系统区域列表
     */
    @PreAuthorize("@ss.hasPermi('core:region:list')")
    @GetMapping("/list")
    public AjaxResult list(SysRegion sysRegion)
    {
        List<SysRegion> list = sysRegionService.selectSysRegionList(sysRegion);
        return success(list);
    }

    @GetMapping("/list/exclude/{id}")
    public AjaxResult excludeChild(@PathVariable(value = "id", required = false) Long id)
    {
        List<SysRegion> list = sysRegionService.selectSysRegionList(new SysRegion());
        list.removeIf(d -> d.getId().intValue() == id || ArrayUtils.contains(StringUtils.split(d.getAncestors(), ","), id + ""));
        return success(list);
    }

    /**
     * 导出系统区域列表
     */
    @PreAuthorize("@ss.hasPermi('core:region:export')")
    @Log(title = "系统区域", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysRegion sysRegion)
    {
        List<SysRegion> list = sysRegionService.selectSysRegionList(sysRegion);
        ExcelUtil<SysRegion> util = new ExcelUtil<SysRegion>(SysRegion.class);
        util.exportExcel(response, list, "系统区域数据");
    }

    /**
     * 获取系统区域详细信息
     */
    @PreAuthorize("@ss.hasPermi('core:region:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(sysRegionService.selectSysRegionById(id));
    }

    /**
     * 新增系统区域
     */
    @PreAuthorize("@ss.hasPermi('core:region:add')")
    @Log(title = "系统区域", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SysRegion sysRegion)
    {
        return toAjax(sysRegionService.insertSysRegion(sysRegion));
    }

    /**
     * 修改系统区域
     */
    @PreAuthorize("@ss.hasPermi('core:region:edit')")
    @Log(title = "系统区域", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SysRegion sysRegion)
    {
        return toAjax(sysRegionService.updateSysRegion(sysRegion));
    }

    /**
     * 删除系统区域
     */
    @PreAuthorize("@ss.hasPermi('core:region:remove')")
    @Log(title = "系统区域", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids)
    {
        return toAjax(sysRegionService.deleteSysRegionByIds(ids));
    }


    /**
     * 导入系统区域
     * @param file
     * @param updateSupport
     * @return
     * @throws Exception
     */
    @PreAuthorize("@ss.hasPermi('core:region:import')")
    @Log(title = "系统区域", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<SysRegion> util = new ExcelUtil<>(SysRegion.class);
        List<SysRegion> lists = util.importExcel(file.getInputStream());
        String message = sysRegionService.importSysRegion(lists, updateSupport);
        return AjaxResult.success(message);
    }

    /**
     * 查询区域列表
     * @return
     */
    @GetMapping("/getRegionList")
    public AjaxResult getRegionList()
    {
        List<SysRegion> lists = sysRegionService.selectSysRegionList(new SysRegion());
        List<TreeSelect>  list = sysRegionService.buildtRegionTreeSelect(lists);
        return success(list);
    }


    /**
     * 查询市区
     * @return
     */
    @GetMapping("/getUrbanAreaList")
    public AjaxResult getUrbanAreaList()
    {
        List<SysRegion> lists = sysRegionService.selectSysRegionListTwo(new SysRegion());
        List<TreeSelect>  list = sysRegionService.buildtRegionTreeSelect(lists);
        return success(list);
    }

}
