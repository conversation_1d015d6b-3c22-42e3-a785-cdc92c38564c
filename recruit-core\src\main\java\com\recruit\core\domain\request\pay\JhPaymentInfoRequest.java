package com.recruit.core.domain.request.pay;

import lombok.Data;

/**
 * 建行请求参数
 */
@Data
public class JhPaymentInfoRequest {
    /**
     * 商户id
     */
    public String merchantId;
    /**
     * 建行公钥
     */
    public String pubkey;
    /**
     * 支付进入
     */
    public String payment;
    /**
     * 微信appid
     */
    public String appId;
    /**
     * 微信openId
     */
    public String openId;
    /**
     * 订单号
     */
    public String orderId;
}
