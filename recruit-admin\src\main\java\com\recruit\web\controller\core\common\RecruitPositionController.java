package com.recruit.web.controller.core.common;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.recruit.common.core.domain.TreeSelectTwo;
import com.recruit.common.core.domain.entity.RecruitPosition;
import com.recruit.common.utils.StringUtils;
import com.recruit.core.service.IRecruitCompanyIndustryService;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.recruit.common.annotation.Log;
import com.recruit.common.core.controller.BaseController;
import com.recruit.common.core.domain.AjaxResult;
import com.recruit.common.enums.BusinessType;
import com.recruit.core.service.IRecruitPositionService;
import com.recruit.common.utils.poi.ExcelUtil;
import com.recruit.common.core.page.TableDataInfo;

/**
 * 职位Controller
 *
 * <AUTHOR>
 * @date 2023-03-20
 */
@RestController
@RequestMapping("/core/position")
public class RecruitPositionController extends BaseController
{
    @Autowired
    private IRecruitPositionService recruitPositionService;

    @Autowired
    private IRecruitCompanyIndustryService recruitCompanyIndustryService;

    /**
     * 查询职位列表
     */
    @PreAuthorize("@ss.hasPermi('core:position:list')")
    @GetMapping("/list")
    public AjaxResult list(RecruitPosition recruitPosition)
    {
        List<RecruitPosition> list = recruitPositionService.selectRecruitPositionList(recruitPosition);
        return success(list);
    }

    @GetMapping("/list/exclude/{id}")
    public AjaxResult excludeChild(@PathVariable(value = "id", required = false) Long id)
    {
        List<RecruitPosition> depts = recruitPositionService.selectRecruitPositionList(new RecruitPosition());
        depts.removeIf(d -> d.getId().intValue() == id || ArrayUtils.contains(StringUtils.split(d.getAncestors(), ","), id + ""));
        return success(depts);
    }

    /**
     * 导出职位列表
     */
    @PreAuthorize("@ss.hasPermi('core:position:export')")
    @Log(title = "职位", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RecruitPosition recruitPosition)
    {
        List<RecruitPosition> list = recruitPositionService.selectRecruitPositionList(recruitPosition);
        ExcelUtil<RecruitPosition> util = new ExcelUtil<RecruitPosition>(RecruitPosition.class);
        util.exportExcel(response, list, "职位数据");
    }

    /**
     * 获取职位详细信息
     */
    @PreAuthorize("@ss.hasPermi('core:position:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(recruitPositionService.selectRecruitPositionById(id));
    }

    /**
     * 新增职位
     */
    @PreAuthorize("@ss.hasPermi('core:position:add')")
    @Log(title = "职位", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RecruitPosition recruitPosition)
    {
        return toAjax(recruitPositionService.insertRecruitPosition(recruitPosition));
    }

    /**
     * 修改职位
     */
    @PreAuthorize("@ss.hasPermi('core:position:edit')")
    @Log(title = "职位", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RecruitPosition recruitPosition)
    {
        return toAjax(recruitPositionService.updateRecruitPosition(recruitPosition));
    }

    /**
     * 删除职位
     */
    @PreAuthorize("@ss.hasPermi('core:position:remove')")
    @Log(title = "职位", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(recruitPositionService.deleteRecruitPositionByIds(ids));
    }

    @GetMapping("/getPositionList")
    public AjaxResult getPositionList()
    {
        List<RecruitPosition> lists = recruitPositionService.selectRecruitPositionList(new RecruitPosition());
        List<TreeSelectTwo>  list = recruitCompanyIndustryService.buildtPositionTreeSelectTwo(lists);
        return success(list);
    }

    @GetMapping("/getSubordinateGradePosition")
    public AjaxResult getSubordinateGradePosition()
    {
        RecruitPosition position = new RecruitPosition();
        position.setGrade(3);
        List<RecruitPosition> lists = recruitPositionService.selectRecruitPositionList(position);
        return success(lists);
    }

}
