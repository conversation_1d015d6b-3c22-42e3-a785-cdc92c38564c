package com.recruit.core.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.recruit.common.annotation.Excel;
import com.recruit.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 面试邀请对象 recruit_interview_info
 *
 * <AUTHOR>
 * @date 2023-04-23
 */
@Data
@ApiModel("面试邀请")
public class RecruitInterviewInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;


    private Long id;


    @Excel(name = "消息id")
    @ApiModelProperty("消息id")
    private Long privateMessageId;


    @ApiModelProperty("被邀人id")
    private Long inviteeId;


    @ApiModelProperty("邀请人id")
    private Long InviterId;


    @Excel(name = "职位id")
    @ApiModelProperty("职位id")
    private Long positionInfoId;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    @Excel(name = "面试时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm")
    @ApiModelProperty("面试时间")
    private Date interviewTime;


    @Excel(name = "联系人姓名")
    @ApiModelProperty("联系人姓名")
    private String contactsName;


    @Excel(name = "联系人电话")
    @ApiModelProperty("联系人电话")
    private String contactsPhone;


    @Excel(name = "面试地点")
    @ApiModelProperty("面试地点")
    private String interviewLocation;


    @Excel(name = "门牌号")
    @ApiModelProperty("门牌号")
    private String houseNumber;


    @Excel(name = "企业地图经度")
    @ApiModelProperty("企业地图经度")
    private String mapLongitude;


    @Excel(name = "企业地图纬度")
    @ApiModelProperty("企业地图纬度")
    private String mapLatitude;





    @ApiModelProperty("发布人")
    private Long publisherId;

    @ApiModelProperty("姓名")
    private String userName;

    @ApiModelProperty("职位名称")
    private String positionInfoName;

    @ApiModelProperty("最高薪资")
    private String maximumSalary;

    @ApiModelProperty("最低薪资")
    private String minimumWage;

    @ApiModelProperty("企业id")
    private Long enterpriseId;

    @ApiModelProperty("企业名称")
    private String enterpriseName;

    @ApiModelProperty("企业logo")
    private String enterpriseLogo;

    @ApiModelProperty("发布人名称")
    private String publisherName;

    @ApiModelProperty("招聘者职位")
    private String position;

    @ApiModelProperty("面试人姓名")
    private String inviteeName;

    @ApiModelProperty("面试人头像")
    private String inviteeHeadSculpture;

    @ApiModelProperty("状态 0未接受 1接受, 2拒绝,3取消")
    private String exchangeStatus;

}
