package com.recruit.core.domain.request.talent;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Auther: <PERSON> kong
 * @Date: 2023/5/11 23:53
 * @Description:
 */
@Data
@ApiModel("等级报名1")
public class LevelRegistrationRequest {

    @ApiModelProperty("用户id")
    public Long userId;

    @ApiModelProperty("用户微信id")
    public String openId;

    @ApiModelProperty("人才引进单位id")
    public Long talentIntroductionInfoId;

    @ApiModelProperty("姓名")
    public String name;

    @ApiModelProperty("手机")
    public String phone;

    @ApiModelProperty("一寸照片")
    public String near_photo;

    @JsonProperty("IdNum")
    @ApiModelProperty("身份证号")
    public String IdNum;

    @JsonProperty("ID_img_front")
    @ApiModelProperty("身份证正面")
    public String ID_img_front;

    @JsonProperty("ID_img_back")
    @ApiModelProperty("身份证反面")
    public String ID_img_back;

    @ApiModelProperty("性别")
    public String sex;

    @ApiModelProperty("民族")
    public String nation;

    @ApiModelProperty("出生日期")
    public String birthday;

    @ApiModelProperty("籍贯")
    public String native_place;

    @ApiModelProperty("生源地")
    public String college_place;

    @ApiModelProperty("政治面貌")
    public String politice_status;

    @ApiModelProperty("学历")
    public String educational_level;

    @ApiModelProperty("省市区")
    public String region;

    @ApiModelProperty("详细联系地址")
    public String address;

    @ApiModelProperty("本科学历")
    public List<QualificationBsRequest> qualification_bs_list;

    @ApiModelProperty("研究生学历")
    public List<QualificationBsRequest> qualification_grad_list;

    @ApiModelProperty("博士生学历")
    public List<QualificationBsRequest> qualification_scholar_list;

    @ApiModelProperty("大专学历")
    public List<QualificationBsRequest> qualification_college_list;

    @ApiModelProperty("高中学历")
    public List<QualificationBsRequest> qualification_high_list;

    @ApiModelProperty("应聘岗位代码")
    public String apply_job_code;

    @ApiModelProperty("声明")
    public String promise;

    @ApiModelProperty("学习、工作经历")
    public List<VitaeRequest> vitae_list;

    @ApiModelProperty("家庭成员")
    public List<FamilyRequest> family_list;

    @ApiModelProperty("爱好")
    public String hobby;

    @ApiModelProperty("论文、课题、社会实践经历")
    public String thesis_report;

    @ApiModelProperty("自我评价")
    public String self_evaluation;

    @ApiModelProperty("登记日期")
    public String apply_date;

    @ApiModelProperty("获奖证书照片")
    public List<String> certificate_img_list;

    @ApiModelProperty("其他材料照片")
    public List<String> other_img_list;

}
