package com.recruit.core.domain.response;

import com.recruit.common.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Auther: Wu kong
 * @Date: 2023/3/24 18:45
 * @Description:
 */
@Data
public class CompanyInfoResponse {

    @Excel(name = "法人名称")
    @ApiModelProperty("法人名称")
    private String corporationName;

    /** 注册资本 */
    @Excel(name = "注册资本")
    @ApiModelProperty("注册资本")
    private String registeredCapital;

    /** 成立时间 */
    @ApiModelProperty("成立时间")
    private String establishedTime;

    /** 营业执照 */
    @Excel(name = "营业执照")
    @ApiModelProperty("营业执照")
    private String businessUrl;

    /** 状态 0待审核，1审核通过，2审核不通过，3违规封号 */
    @Excel(name = "状态 0待审核，1审核通过，2审核不通过，3违规封号")
    @ApiModelProperty("状态 0待审核，1审核通过，2审核不通过，3违规封号")
    private String status;

    /** 经营范围 */
    @Excel(name = "经营范围")
    @ApiModelProperty("经营范围")
    private String businessScope;

    /** 组成形式 */
    @Excel(name = "组成形式")
    @ApiModelProperty("组成形式")
    private String compositionForm;

    /** 社会信用代码 */
    @Excel(name = "社会信用代码")
    @ApiModelProperty("社会信用代码")
    private String socialCreditCode;

    /** 地址 */
    @Excel(name = "地址")
    @ApiModelProperty("地址")
    private String address;

    /** 类型 */
    @Excel(name = "类型")
    @ApiModelProperty("类型")
    private String type;
    /** 类型 */
    @Excel(name = "单位名称")
    @ApiModelProperty("单位名称")
    private String enterpriseName;
}
