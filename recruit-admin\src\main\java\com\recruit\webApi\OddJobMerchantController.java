package com.recruit.webApi;

import com.recruit.common.core.controller.BaseController;
import com.recruit.common.core.domain.AjaxResult;
import com.recruit.common.core.page.TableDataInfo;
import com.recruit.common.utils.StringUtils;
import com.recruit.core.domain.MerchantInfoEntry;
import com.recruit.core.domain.TemporaryServiceUserOrder;
import com.recruit.core.domain.TemporaryServiceUserSubOrder;
import com.recruit.core.domain.request.merchant.MerchantAddressRequest;
import com.recruit.core.domain.response.MerchantInfoResponse;
import com.recruit.core.service.IMerchantInfoEntryService;
import com.recruit.core.service.IRecruitUserInfoService;
import com.recruit.core.service.ITemporaryServiceUserSubOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Auther: Wu kong
 * @Date: 2023/6/2 8:41
 * @Description:
 */
@Api(tags= "(6-2)零工商户接口")
@Slf4j
@RestController
@RequestMapping("/web/api/oddJobMerchant")
public class OddJobMerchantController extends BaseController {

    @Autowired
    private IMerchantInfoEntryService merchantInfoEntryService;

    @Autowired
    private ITemporaryServiceUserSubOrderService temporaryServiceUserSubOrderService;



    @ApiOperation("获取是否是商户")
    @GetMapping("/getMerchantOrNot")
    public AjaxResult getMerchantOrNot()
    {
        Long userId = 0L;
        try {
            userId = getUserId();
            //userId = 91L;
        }catch (Exception e){
            return success();
        }
        MerchantInfoEntry merchantInfo = merchantInfoEntryService.selectMerchantInfoEntryByUserId(userId);
        Map<String, Object> maps = new HashMap<>();
        if(merchantInfo != null){
            maps.put("merchantOrNot", true);
        }else {
            maps.put("merchantOrNot", false);
        }
        return success(maps);
    }

    @ApiOperation("获取商户信息, applicationStatus 申请状态为 0时，则显示审核中页面")
    @GetMapping("/getMerchantInfo")
    public AjaxResult getMerchantInfo()
    {
        Long userId = getUserId();
        //Long userId = 103L;
        MerchantInfoEntry merchantInfo = merchantInfoEntryService.selectMerchantInfoEntryByUserId(userId);
        if(merchantInfo == null){
            throw new RuntimeException("该商户信息不存在！");
        }
        MerchantInfoResponse response = new MerchantInfoResponse();
        //服务行业编码
        response.setServiceTrade(merchantInfo.getServiceTrade());
        //服务行业名称
        response.setServiceTradeName(merchantInfo.getServiceTradeName());
        //商户名称
        response.setServiceName(merchantInfo.getServiceName());
        //申请人姓名
        response.setUserName(merchantInfo.getUserName());
        //申请人姓名
        response.setUserId(Long.valueOf(merchantInfo.getUserId()));
        //申请状态
        response.setApplicationStatus(merchantInfo.getApplicationStatus());
        //所在地区
        response.setRegion(merchantInfo.getRegion());
        //详细地址
        response.setAddress(merchantInfo.getAddress());
        //经度
        response.setLongitude(merchantInfo.getLongitude());
        //纬度
        response.setLatitude(merchantInfo.getLatitude());
        //账户金额
        if(merchantInfo.getAccountAmount() != null){
            response.setAccountAmount(merchantInfo.getAccountAmount());
        }else {
            response.setAccountAmount(new BigDecimal(0));
        }

        //获取总收入
        Integer totalIncome = temporaryServiceUserSubOrderService.getObtainOrderAmount(merchantInfo.getId(), userId);
        if(totalIncome == null){
            response.setTotalIncome(new BigDecimal(0));
        }else {
            //总收入
            response.setTotalIncome(new BigDecimal(totalIncome));
        }
        return success(response);
    }


    @ApiOperation("设置商户详细地址")
    @PostMapping("/setMerchantAddress")
    public AjaxResult setMerchantAddress(@RequestBody @Validated MerchantAddressRequest request) {

        Long userId = getUserId();
        //Long userId = 91L;
        MerchantInfoEntry merchantInfo = merchantInfoEntryService.selectMerchantInfoEntryByUserId(userId);
        if(merchantInfo == null){
            throw new RuntimeException("该商户信息不存在！");
        }
        //所在地区
        merchantInfo.setRegion(request.getRegion());
        //详细地址
        merchantInfo.setAddress(request.getAddress());
        //经度
        merchantInfo.setLongitude(request.getLongitude());
        //纬度
        merchantInfo.setLatitude(request.getLatitude());
        int ss = merchantInfoEntryService.updateMerchantInfoEntry(merchantInfo);
        return toAjax(ss);
    }


    @ApiOperation(value = "获取商户订单",notes="orderStatus All全部，0预约订单, 1待服务，2已完成，3订单取消，4退款售后    " +
            "orderStatus 0预约订单，1待服务，2已完成，3订单取消，4退款 ")
    @GetMapping(value = "/getObtainMerchantOrders", produces = {"application/json"})
    @ApiResponses({
            @ApiResponse(code = 200, message = "返回实体", response = TemporaryServiceUserOrder.class)
    })
    public TableDataInfo getObtainMerchantOrders(String orderStatus)
    {

        Long userId = getUserId();
        MerchantInfoEntry merchantInfo = merchantInfoEntryService.selectMerchantInfoEntryByUserId(userId);
        if(merchantInfo == null){
            throw new RuntimeException("该商户信息不存在！");
        }
        switch (orderStatus){
            case "All":
                orderStatus = null;
                break;
        }
        TemporaryServiceUserSubOrder serviceUserSubOrder = new TemporaryServiceUserSubOrder();
        serviceUserSubOrder.setReceiverUserId(userId);
        serviceUserSubOrder.setMerchantInfoId(merchantInfo.getId());
        serviceUserSubOrder.setOrderStatus(orderStatus);
        List<TemporaryServiceUserSubOrder> lists = temporaryServiceUserSubOrderService.selectTemporaryServiceUserSubOrderListTwo(serviceUserSubOrder);
        return getDataTable(lists);
    }

}
