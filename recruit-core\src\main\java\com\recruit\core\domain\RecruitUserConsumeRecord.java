package com.recruit.core.domain;

import java.math.BigDecimal;
import com.recruit.common.annotation.Excel;
import com.recruit.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 用户消费记录对象 recruit_user_consume_record
 *
 * <AUTHOR>
 * @date 2023-05-23
 */
@Data
@ApiModel("用户消费记录")
public class RecruitUserConsumeRecord extends BaseEntity
{
    private static final long serialVersionUID = 1L;


    private Long id;


    @Excel(name = "用户id")
    @ApiModelProperty("用户id")
    private Long userId;

    @ApiModelProperty("用户名称")
    private String userName;


    @Excel(name = "消费金额")
    @ApiModelProperty("消费金额")
    private BigDecimal amount;


    @Excel(name = "消费积分")
    @ApiModelProperty("消费积分")
    private String integral;


    @Excel(name = "道具类型")
    @ApiModelProperty("道具类型")
    private String propType;


    @Excel(name = "进出，1进账，2消费")
    @ApiModelProperty("进出，1进账，2消费")
    private String access;


    @Excel(name = "描述")
    @ApiModelProperty("描述")
    private String describe;


}
