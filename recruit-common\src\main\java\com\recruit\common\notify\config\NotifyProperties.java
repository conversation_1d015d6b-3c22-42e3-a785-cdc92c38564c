package com.recruit.common.notify.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
@Data
@ConfigurationProperties(prefix = "litemall.notify")
public class NotifyProperties {
    /**
     * 邮件
     */
    private Mail mail;
    /**
     * 短信
     */
    private Sms sms;


    /**
     * 邮件
     */
    @Data
    public static class Mail {
        private boolean enable;
        private String host;
        private String username;
        private String password;
        private String sendfrom;
        private String sendto;
        private Integer port;

    }

    /**
     * 短信
     */
    @Data
    public static class Sms {
        private boolean enable;
        private String active;
        private String sign;
        private Tencent tencent;
        private Aliyun aliyun;
        private Huawei huawei;
        private List<Map<String, String>> template = new ArrayList<>();


        @Data
        public static class Tencent {
            private int appid;
            private String appkey;
        }

        @Data
        public static class Aliyun {
            private String regionId;
            private String accessKeyId;
            private String accessKeySecret;

        }

        @Data
        public static class Huawei {
            private String sender;
            private String accessKeyId;
            private String secretAccessKey;

        }
    }

}
