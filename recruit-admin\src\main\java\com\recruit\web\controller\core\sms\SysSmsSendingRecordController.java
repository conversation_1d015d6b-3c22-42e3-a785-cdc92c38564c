package com.recruit.web.controller.core.sms;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.recruit.common.annotation.Log;
import com.recruit.common.core.controller.BaseController;
import com.recruit.common.core.domain.AjaxResult;
import com.recruit.common.enums.BusinessType;
import org.springframework.web.multipart.MultipartFile;
import com.recruit.core.domain.SysSmsSendingRecord;
import com.recruit.core.service.ISysSmsSendingRecordService;
import com.recruit.common.utils.poi.ExcelUtil;
import com.recruit.common.core.page.TableDataInfo;

/**
 * 短信发送记录Controller
 *
 * <AUTHOR>
 * @date 2023-05-31
 */
@RestController
@RequestMapping("/core/smsSendingRecord")
public class SysSmsSendingRecordController extends BaseController
{
    @Autowired
    private ISysSmsSendingRecordService sysSmsSendingRecordService;

    /**
     * 查询短信发送记录列表
     */
    @PreAuthorize("@ss.hasPermi('core:smsSendingRecord:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysSmsSendingRecord sysSmsSendingRecord)
    {
        startPage();
        List<SysSmsSendingRecord> list = sysSmsSendingRecordService.selectSysSmsSendingRecordList(sysSmsSendingRecord);
        return getDataTable(list);
    }

    /**
     * 导出短信发送记录列表
     */
    @PreAuthorize("@ss.hasPermi('core:smsSendingRecord:export')")
    @Log(title = "短信发送记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysSmsSendingRecord sysSmsSendingRecord)
    {
        List<SysSmsSendingRecord> list = sysSmsSendingRecordService.selectSysSmsSendingRecordList(sysSmsSendingRecord);
        ExcelUtil<SysSmsSendingRecord> util = new ExcelUtil<SysSmsSendingRecord>(SysSmsSendingRecord.class);
        util.exportExcel(response, list, "短信发送记录数据");
    }

    /**
     * 获取短信发送记录详细信息
     */
    @PreAuthorize("@ss.hasPermi('core:smsSendingRecord:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(sysSmsSendingRecordService.selectSysSmsSendingRecordById(id));
    }

    /**
     * 新增短信发送记录
     */
    @PreAuthorize("@ss.hasPermi('core:smsSendingRecord:add')")
    @Log(title = "短信发送记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SysSmsSendingRecord sysSmsSendingRecord)
    {
        return toAjax(sysSmsSendingRecordService.insertSysSmsSendingRecord(sysSmsSendingRecord));
    }

    /**
     * 修改短信发送记录
     */
    @PreAuthorize("@ss.hasPermi('core:smsSendingRecord:edit')")
    @Log(title = "短信发送记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SysSmsSendingRecord sysSmsSendingRecord)
    {
        return toAjax(sysSmsSendingRecordService.updateSysSmsSendingRecord(sysSmsSendingRecord));
    }

    /**
     * 删除短信发送记录
     */
    @PreAuthorize("@ss.hasPermi('core:smsSendingRecord:remove')")
    @Log(title = "短信发送记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(sysSmsSendingRecordService.deleteSysSmsSendingRecordByIds(ids));
    }


    /**
     * 导入短信发送记录
     * @param file
     * @param updateSupport
     * @return
     * @throws Exception
     */
    @PreAuthorize("@ss.hasPermi('core:smsSendingRecord:import')")
    @Log(title = "短信发送记录", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<SysSmsSendingRecord> util = new ExcelUtil<>(SysSmsSendingRecord.class);
        List<SysSmsSendingRecord> lists = util.importExcel(file.getInputStream());
        String message = sysSmsSendingRecordService.importSysSmsSendingRecord(lists, updateSupport);
        return AjaxResult.success(message);
    }


}
