package com.recruit.core.domain.request.im;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Auther: <PERSON> kong
 * @Date: 2023/4/7 22:34
 * @Description:
 */
@Data
@ApiModel("好友信息")
public class FriendRequest {

    @ApiModelProperty("接收者id")
    private Long friendId;

    @ApiModelProperty("职位表id")
    private Long positionInfoId;

    @ApiModelProperty("企业id")
    private Long enterpriseId;

}
