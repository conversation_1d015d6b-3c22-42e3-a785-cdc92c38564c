package com.recruit.common.utils;

import java.util.regex.Pattern;

/**
 * @Auther: <PERSON> kong
 * @Date: 2023/3/16 9:46
 * @Description:
 */
public class EmailUtil {

    final static Pattern partern = Pattern.compile("[a-zA-Z0-9]+[\\.]{0,1}[a-zA-Z0-9]+@[a-zA-Z0-9]+\\.[a-zA-Z]+");
    /**
     * 验证输入的邮箱格式是否符合
     * @param email
     * @return 是否合法
     */
    public static boolean emailFormat(String email){
        boolean isMatch = partern.matcher(email).matches();
        return isMatch;
    }

}
