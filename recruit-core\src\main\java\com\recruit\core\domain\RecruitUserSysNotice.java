package com.recruit.core.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.recruit.common.annotation.Excel;
import com.recruit.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 用户系统通知公告对象 recruit_user_sys_notice
 *
 * <AUTHOR>
 * @date 2023-05-16
 */
@Data
@ApiModel("用户系统通知公告")
public class RecruitUserSysNotice extends BaseEntity
{
    private static final long serialVersionUID = 1L;


    private Long id;


    @Excel(name = "公告id")
    @ApiModelProperty("公告id")
    private Long noticeId;


    @Excel(name = "用户id")
    @ApiModelProperty("用户id")
    private Long userId;


    @ApiModelProperty("用户名称")
    private String userName;


    @Excel(name = "公告标题")
    @ApiModelProperty("公告标题")
    private String noticeTitle;


    @Excel(name = "公告类型")
    @ApiModelProperty("公告类型")
    private String noticeType;


    @Excel(name = "公告内容")
    @ApiModelProperty("公告内容")
    private String noticeContent;


    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "发送时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("发送时间")
    private Date sendTime;

    /**
     * 发送类型，0全部发送，1仅给企业发送，2仅给个人发送，3仅新用户发送，4仅老用户发送
     */
    private String sentType;
    /**
     * 发送方式，1系统发送，2短信推送，3微信推送，4短信+微信推送
     */
    private String sendMode;
    /**
     * 短信模板
     */
    private Long smsTemplateId;

    @ApiModelProperty("手机号")
    private String phone;

    @ApiModelProperty("微信登录openid")
    private String openId;

    private String latestNewsTime;

    /**
     * 积分
     */
    private String integral;

}
