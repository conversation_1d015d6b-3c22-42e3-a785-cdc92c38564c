package com.recruit.web.controller.core.resume;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.recruit.common.annotation.Log;
import com.recruit.common.core.controller.BaseController;
import com.recruit.common.core.domain.AjaxResult;
import com.recruit.common.enums.BusinessType;
import com.recruit.core.domain.RecruitResumeBasicInfo;
import com.recruit.core.service.IRecruitResumeBasicInfoService;
import com.recruit.common.utils.poi.ExcelUtil;
import com.recruit.common.core.page.TableDataInfo;

/**
 * 简历基本信息Controller
 *
 * <AUTHOR>
 * @date 2023-03-19
 */
@RestController
@RequestMapping("/core/resumeBasicInfo")
public class RecruitResumeBasicInfoController extends BaseController
{
    @Autowired
    private IRecruitResumeBasicInfoService recruitResumeBasicInfoService;

    /**
     * 查询简历基本信息列表
     */
    @PreAuthorize("@ss.hasPermi('core:resumeBasicInfo:list')")
    @GetMapping("/list")
    public TableDataInfo list(RecruitResumeBasicInfo recruitResumeBasicInfo)
    {
        startPage();
        List<RecruitResumeBasicInfo> list = recruitResumeBasicInfoService.selectRecruitResumeBasicInfoList(recruitResumeBasicInfo);
        return getDataTable(list);
    }

    /**
     * 导出简历基本信息列表
     */
    @PreAuthorize("@ss.hasPermi('core:resumeBasicInfo:export')")
    @Log(title = "简历基本信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RecruitResumeBasicInfo recruitResumeBasicInfo)
    {
        List<RecruitResumeBasicInfo> list = recruitResumeBasicInfoService.selectRecruitResumeBasicInfoList(recruitResumeBasicInfo);
        ExcelUtil<RecruitResumeBasicInfo> util = new ExcelUtil<RecruitResumeBasicInfo>(RecruitResumeBasicInfo.class);
        util.exportExcel(response, list, "简历基本信息数据");
    }

    /**
     * 获取简历基本信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('core:resumeBasicInfo:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(recruitResumeBasicInfoService.selectRecruitResumeBasicInfoById(id));
    }

    /**
     * 新增简历基本信息
     */
    @PreAuthorize("@ss.hasPermi('core:resumeBasicInfo:add')")
    @Log(title = "简历基本信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RecruitResumeBasicInfo recruitResumeBasicInfo)
    {
        return toAjax(recruitResumeBasicInfoService.insertRecruitResumeBasicInfo(recruitResumeBasicInfo));
    }

    /**
     * 修改简历基本信息
     */
    @PreAuthorize("@ss.hasPermi('core:resumeBasicInfo:edit')")
    @Log(title = "简历基本信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RecruitResumeBasicInfo recruitResumeBasicInfo)
    {
        return toAjax(recruitResumeBasicInfoService.updateRecruitResumeBasicInfo(recruitResumeBasicInfo));
    }

    /**
     * 删除简历基本信息
     */
    @PreAuthorize("@ss.hasPermi('core:resumeBasicInfo:remove')")
    @Log(title = "简历基本信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(recruitResumeBasicInfoService.deleteRecruitResumeBasicInfoByIds(ids));
    }
}
