package com.recruit.web.controller.core.position;

import com.recruit.common.annotation.Log;
import com.recruit.common.core.controller.BaseController;
import com.recruit.common.core.domain.AjaxResult;
import com.recruit.common.core.page.TableDataInfo;
import com.recruit.common.enums.BusinessType;
import com.recruit.common.utils.StringUtils;
import com.recruit.core.domain.RecruitPositionInfo;
import com.recruit.core.domain.RecruitWorkAddress;
import com.recruit.core.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import java.util.List;

/**
 * 职位信息Controller
 *
 * <AUTHOR>
 * @date 2023-03-26
 */
@RestController
@RequestMapping("/core/popularPositions")
public class PopularPositionsController extends BaseController
{

    @Autowired
    private IRecruitPositionService recruitPositionService;

    @Autowired
    private IRecruitPositionInfoService recruitPositionInfoService;

    @Autowired
    private IRecruitWorkAddressService recruitWorkAddressService;


    /**
     * 查询职位信息列表
     */
    @PreAuthorize("@ss.hasPermi('core:popularPositions:list')")
    @GetMapping("/list")
    public TableDataInfo list(RecruitPositionInfo recruitPositionInfo)
    {
        startPage();
        List<RecruitPositionInfo> list = recruitPositionInfoService.selectPopularPositionsList(recruitPositionInfo);
        list.forEach(e->{
            if(e.getPositionCode() != null && !StringUtils.equals(e.getPositionCode(), "")) {
                e.setPositionName(recruitPositionService.getMap(e.getPositionCode()));
            }
        });
        return getDataTable(list);
    }

    /**
     * 获取职位信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('core:popularPositions:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        RecruitPositionInfo positionInfo = recruitPositionInfoService.selectRecruitPositionInfoById(id);
        if(positionInfo != null) {
            RecruitWorkAddress workAddress = new RecruitWorkAddress();
            workAddress.setUserId(positionInfo.getPublisherId());
            workAddress.setEnterpriseId(positionInfo.getEnterpriseId());
            List<RecruitWorkAddress> list = recruitWorkAddressService.selectRecruitWorkAddressList(workAddress);
            positionInfo.setWorkAddressList(list);
        }
        return success(positionInfo);
    }

    /**
     * 新增职位信息
     */
    @PreAuthorize("@ss.hasPermi('core:popularPositions:add')")
    @Log(title = "职位信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RecruitPositionInfo recruitPositionInfo)
    {
        int ss = recruitPositionInfoService.insertRecruitPositionInfo(recruitPositionInfo);
        if(ss > 0){
            if(recruitPositionInfo.getWorkAddressId() != null) {
                RecruitWorkAddress workAddress = new RecruitWorkAddress();
                workAddress.setId(recruitPositionInfo.getWorkAddressId());
                workAddress.setWorkAddress(recruitPositionInfo.getWorkAddress());
                workAddress.setHouseNumber(recruitPositionInfo.getHouseNumber());
                recruitWorkAddressService.updateRecruitWorkAddress(workAddress);
            }else {
                RecruitWorkAddress workAddress = new RecruitWorkAddress();
                workAddress.setWorkAddress(recruitPositionInfo.getWorkAddress());
                workAddress.setHouseNumber(recruitPositionInfo.getHouseNumber());
                workAddress.setUserId(recruitPositionInfo.getPublisherId());
                workAddress.setEnterpriseId(recruitPositionInfo.getEnterpriseId());
                recruitWorkAddressService.insertRecruitWorkAddress(workAddress);
                recruitPositionInfo.setWorkAddressId(workAddress.getId());
            }
        }
        ss = recruitPositionInfoService.updateRecruitPositionInfo(recruitPositionInfo);
        return toAjax(ss);
    }

    /**
     * 修改职位信息
     */
    @PreAuthorize("@ss.hasPermi('core:popularPositions:edit')")
    @Log(title = "职位信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RecruitPositionInfo recruitPositionInfo)
    {
        if(recruitPositionInfo.getWorkAddressId() != null) {
            RecruitWorkAddress workAddress = new RecruitWorkAddress();
            workAddress.setId(recruitPositionInfo.getWorkAddressId());
            workAddress.setWorkAddress(recruitPositionInfo.getWorkAddress());
            workAddress.setHouseNumber(recruitPositionInfo.getHouseNumber());
            recruitWorkAddressService.updateRecruitWorkAddress(workAddress);
        }else {
            RecruitWorkAddress workAddress = new RecruitWorkAddress();
            workAddress.setWorkAddress(recruitPositionInfo.getWorkAddress());
            workAddress.setHouseNumber(recruitPositionInfo.getHouseNumber());
            workAddress.setUserId(recruitPositionInfo.getPublisherId());
            workAddress.setEnterpriseId(recruitPositionInfo.getEnterpriseId());
            recruitWorkAddressService.insertRecruitWorkAddress(workAddress);
            recruitPositionInfo.setWorkAddressId(workAddress.getId());
        }
        int ss = recruitPositionInfoService.updateRecruitPositionInfo(recruitPositionInfo);
        return toAjax(ss);
    }


    /**
     * 删除职位信息
     */
    @PreAuthorize("@ss.hasPermi('core:popularPositions:remove')")
    @Log(title = "职位信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(recruitPositionInfoService.deleteRecruitPositionInfoByIds(ids));
    }



}
