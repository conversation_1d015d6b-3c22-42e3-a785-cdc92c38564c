package com.recruit.common.core.domain.entity;

import lombok.Data;

@Data
public class AppletPayment {
    /**
     * 返回状态
     */
    private String SUCCESS;
    /**
     * 错误码
     */
    private String ERRCODE;
    /**
     * 错误信息
     */
    private String ERRMSG;
    /**
     * 交易码
     */
    private String TXCODE;
    /**
     * appid
     */
    private String appId;
    /**
     * 时间戳
     */
    private String timeStamp;
    /**
     * 随机串
     */
    private String nonceStr;
    /**
     * 数据包
     */
    private String packages;
    /**
     * 签名类型
     */
    private String signType;
    /**
     * 支付签名
     */
    private String paySign;
    /**
     * 子商户的商户号
     */
    private String partnerid;
    /**
     * 预支付交易会话ID
     */
    private String prepayid;
    /**
     * 微信H5支付中间页面URL
     */
    private String mweb_url;


}
