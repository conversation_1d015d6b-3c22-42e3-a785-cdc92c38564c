package com.recruit.core.domain;

import com.recruit.common.annotation.Excel;
import com.recruit.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 系统短信模板对象 sys_sms_template
 *
 * <AUTHOR>
 * @date 2023-05-12
 */
@Data
@ApiModel("系统短信模板")
public class SysSmsTemplate extends BaseEntity
{
    private static final long serialVersionUID = 1L;


    private Long id;


    @Excel(name = "模板id")
    @ApiModelProperty("模板id")
    private String templateId;


    @Excel(name = "模板名称")
    @ApiModelProperty("模板名称")
    private String templateName;


    @Excel(name = "模板内容")
    @ApiModelProperty("模板内容")
    private String templateContent;


    @Excel(name = "短信类型")
    @ApiModelProperty("短信类型")
    private String smsType;


    @Excel(name = "是否国际")
    @ApiModelProperty("是否国际")
    private String international;


    @ApiModelProperty("状态编码")
    private String statusCode;


    @ApiModelProperty("审阅答复")
    private String reviewReply;

    @ApiModelProperty("发送类型")
    private String sendType;


}
