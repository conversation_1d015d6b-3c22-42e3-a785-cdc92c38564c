package com.recruit.webApi;

import com.recruit.common.core.controller.BaseController;
import com.recruit.common.core.domain.AjaxResult;
import com.recruit.common.core.page.TableDataInfo;
import com.recruit.common.utils.DesensitizeUtil;
import com.recruit.common.utils.DictUtils;
import com.recruit.common.utils.StringUtils;
import com.recruit.common.utils.bean.BeanUtils;
import com.recruit.core.domain.*;
import com.recruit.core.domain.request.position.ResumeListRequest;
import com.recruit.core.domain.request.resume.AttachmentResumeRequest;
import com.recruit.core.domain.request.resume.BasicInfoRequest;
import com.recruit.core.domain.request.resume.HideResumeRequest;
import com.recruit.core.domain.request.resume.RemoveRequest;
import com.recruit.core.domain.request.resume.ResumeEducationRequest;
import com.recruit.core.domain.request.resume.ResumeJobDesireRequest;
import com.recruit.core.domain.request.resume.ResumeOtherInfoRequest;
import com.recruit.core.domain.request.resume.ResumeProfessionalSkillsRequest;
import com.recruit.core.domain.request.resume.ResumeProjectExperienceRequest;
import com.recruit.core.domain.request.resume.ResumeWorkHistoryRequest;
import com.recruit.core.domain.response.ResumeCompletionRateResponse;
import com.recruit.core.domain.response.ResumeInfoResponse;
import com.recruit.core.service.IRecruitCollectService;
import com.recruit.core.service.IRecruitPositionService;
import com.recruit.core.service.IRecruitResumeBasicInfoService;
import com.recruit.core.service.IRecruitResumeEducationService;
import com.recruit.core.service.IRecruitResumeJobDesireService;
import com.recruit.core.service.IRecruitResumeOtherInfoService;
import com.recruit.core.service.IRecruitResumeProfessionalSkillsService;
import com.recruit.core.service.IRecruitResumeProjectExperienceService;
import com.recruit.core.service.IRecruitResumeWorkHistoryService;
import com.recruit.core.service.IRecruitUserInfoService;
import com.recruit.core.service.ISysRegionService;
import com.recruit.core.service.ISysSensitiveService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Auther: Wu kong
 * @Date: 2023/3/19 9:05
 * @Description:
 */
@Api(tags= "求职者基本信息管理")
@Slf4j
@RestController
@RequestMapping("/web/api/jobSeekers")
public class JobSeekersContrller extends BaseController {

    @Autowired
    private ISysRegionService sysRegionService;

    @Autowired
    private IRecruitCollectService recruitCollectService;

    @Autowired
    private ISysSensitiveService sysSensitiveService;

    @Autowired
    private IRecruitUserInfoService recruitUserInfoService;

    @Autowired
    private IRecruitPositionService recruitPositionService;

    @Autowired
    private IRecruitResumeJobDesireService recruitResumeJobDesireService;

    @Autowired
    private IRecruitResumeEducationService recruitResumeEducationService;

    @Autowired
    private IRecruitResumeOtherInfoService recruitResumeOtherInfoService;

    @Autowired
    private IRecruitResumeBasicInfoService recruitResumeBasicInfoService;

    @Autowired
    private IRecruitResumeWorkHistoryService recruitResumeWorkHistoryService;

    @Autowired
    private IRecruitResumeProjectExperienceService recruitResumeProjectExperienceService;

    @Autowired
    private IRecruitResumeProfessionalSkillsService recruitResumeProfessionalSkillsService;



    @ApiOperation("设置基本资料")
    @PostMapping("/setBasicInfo")
    public AjaxResult setBasicInfo(@RequestBody @Validated BasicInfoRequest request) {
        if (request.getCitys() != null) {
            StringBuilder city = new StringBuilder();
            request.getCitys().forEach(e->{
                if(StringUtils.equals(city, "")){
                    city.append(e);
                }else {
                    city.append(",").append(e);
                }
            });
            request.setCity(String.valueOf(city));
        }
        return success(recruitUserInfoService.setBasicInfo(request));
    }

    @ApiOperation("设置隐藏简历")
    @PostMapping("/setHideResume")
    public AjaxResult setHideResume(@RequestBody @Validated HideResumeRequest request) {
        return success(recruitUserInfoService.setHideResume(request));
    }

    @ApiOperation("查看隐藏简历")
    @GetMapping("/getHideResume")
    public AjaxResult getHideResume() {

        Map<String, String> map = new HashMap<>();
        RecruitResumeBasicInfo resumeBasicInfo = recruitUserInfoService.getHideResume();
        map.put("hideResume", resumeBasicInfo.getHideResume());
        return success(map);
    }

    @ApiOperation("求职意愿")
    @PostMapping(value = "/jobSeekingIntention")
    public AjaxResult jobSeekingIntention(@RequestBody @Validated ResumeJobDesireRequest request)
    {
        if (request.getExpectedCitys() != null) {
            StringBuilder expectedCity = new StringBuilder();
            request.getExpectedCitys().forEach(e->{
                if(StringUtils.equals(expectedCity, "")){
                    expectedCity.append(e);
                }else {
                    expectedCity.append(",").append(e);
                }
            });
            request.setExpectedCity(String.valueOf(expectedCity));
        }
        //根据用户id查询
        RecruitResumeJobDesire resumeJobDesire = recruitResumeJobDesireService.selectRecruitResumeJobDesireByUserId(getUserId());
        RecruitUserInfo recruitUserInfo = new RecruitUserInfo();
        recruitUserInfo.setId(getUserId());
        recruitUserInfoService.updateRecruitUserInfo(recruitUserInfo);
        //判空，如果存在则更新，不存在则新增
        if(StringUtils.isNotNull(resumeJobDesire)){
            BeanUtils.copyBeanProp(resumeJobDesire, request);
            return toAjax(recruitResumeJobDesireService.updateRecruitResumeJobDesire(resumeJobDesire));
        }else {
            RecruitResumeJobDesire recruitResumeJobDesire = new RecruitResumeJobDesire();
            BeanUtils.copyBeanProp(recruitResumeJobDesire, request);
            recruitResumeJobDesire.setUserId(getUserId());
            return toAjax(recruitResumeJobDesireService.insertRecruitResumeJobDesire(recruitResumeJobDesire));
        }
    }

    @ApiOperation("教育经历")
    @PostMapping(value = "/resumeEducation")
    public AjaxResult resumeEducation(@RequestBody @Validated ResumeEducationRequest request)
    {
        //根据用户id查询
        RecruitResumeEducation resumeEducation = recruitResumeEducationService.selectRecruitResumeEducationByUserId(request.getId(), getUserId());
        RecruitUserInfo recruitUserInfo = new RecruitUserInfo();
        recruitUserInfo.setId(getUserId());
        recruitUserInfoService.updateRecruitUserInfo(recruitUserInfo);
        //判空，如果存在则更新，不存在则新增
        if(StringUtils.isNotNull(resumeEducation)){
            BeanUtils.copyBeanProp(resumeEducation, request);
            return toAjax(recruitResumeEducationService.updateRecruitResumeEducation(resumeEducation));
        }else {
            RecruitResumeEducation resumeEducations = new RecruitResumeEducation();
            BeanUtils.copyBeanProp(resumeEducations, request);
            resumeEducations.setUserId(getUserId());
            return toAjax(recruitResumeEducationService.insertRecruitResumeEducation(resumeEducations));
        }
    }

    @ApiOperation("工作经历，其中 至今 0否 1是")
    @PostMapping(value = "/workHistory")
    public AjaxResult workHistory(@RequestBody @Validated ResumeWorkHistoryRequest request)
    {
        //敏感词校验
        if(request.getJobDescription() != null && !StringUtils.equals(request.getJobDescription(), "")) {
            request.setJobDescription(sysSensitiveService.verifySensitiveWords(getUserId(), "", request.getJobDescription()));
        }

        //根据用户id查询
        RecruitResumeWorkHistory resumeWorkHistory = recruitResumeWorkHistoryService.selectRecruitResumeWorkHistoryByIdAndUserId(request.getId(), getUserId());
        RecruitUserInfo recruitUserInfo = new RecruitUserInfo();
        recruitUserInfo.setId(getUserId());
        recruitUserInfoService.updateRecruitUserInfo(recruitUserInfo);
        //判空，如果存在则更新，不存在则新增
        if(StringUtils.isNotNull(resumeWorkHistory)){
            BeanUtils.copyBeanProp(resumeWorkHistory, request);
            return toAjax(recruitResumeWorkHistoryService.updateRecruitResumeWorkHistory(resumeWorkHistory));
        }else {
            RecruitResumeWorkHistory resumeWorkHistorys = new RecruitResumeWorkHistory();
            BeanUtils.copyBeanProp(resumeWorkHistorys, request);
            resumeWorkHistorys.setUserId(getUserId());
            return toAjax(recruitResumeWorkHistoryService.insertRecruitResumeWorkHistory(resumeWorkHistorys));
        }
    }

    @ApiOperation("项目经历")
    @PostMapping(value = "/projectExperience")
    public AjaxResult projectExperience(@RequestBody @Validated ResumeProjectExperienceRequest request)
    {
        //敏感词校验
        if(request.getProjectDescription() != null && !StringUtils.equals(request.getProjectDescription(), "")) {
            request.setProjectDescription(sysSensitiveService.verifySensitiveWords(getUserId(), "", request.getProjectDescription()));
        }

        //根据用户id查询
        RecruitResumeProjectExperience resumeProjectExperience = recruitResumeProjectExperienceService.selectResumeProjectExperienceByIdAndUserId(request.getId(), getUserId());
        RecruitUserInfo recruitUserInfo = new RecruitUserInfo();
        recruitUserInfo.setId(getUserId());
        recruitUserInfoService.updateRecruitUserInfo(recruitUserInfo);
        //判空，如果存在则更新，不存在则新增
        if(StringUtils.isNotNull(resumeProjectExperience)){
            BeanUtils.copyBeanProp(resumeProjectExperience, request);
            return toAjax(recruitResumeProjectExperienceService.updateRecruitResumeProjectExperience(resumeProjectExperience));
        }else {
            RecruitResumeProjectExperience resumeProjectExperiences = new RecruitResumeProjectExperience();
            BeanUtils.copyBeanProp(resumeProjectExperiences, request);
            resumeProjectExperiences.setUserId(getUserId());
            return toAjax(recruitResumeProjectExperienceService.insertRecruitResumeProjectExperience(resumeProjectExperiences));
        }
    }

    @ApiOperation("其它信息，自我评价")
    @PostMapping(value = "/resumeOtherInfo")
    public AjaxResult resumeOtherInfo(@RequestBody @Validated ResumeOtherInfoRequest request)
    {
        //敏感词校验
        if(request.getSelfEvaluation() != null && !StringUtils.equals(request.getSelfEvaluation(), "")) {
            request.setSelfEvaluation(sysSensitiveService.verifySensitiveWords(getUserId(), "", request.getSelfEvaluation()));
        }

        //根据用户id查询
        RecruitResumeOtherInfo resumeOtherInfo = recruitResumeOtherInfoService.selectRecruitResumeOtherInfoByUserId(getUserId());
        RecruitUserInfo recruitUserInfo = new RecruitUserInfo();
        recruitUserInfo.setId(getUserId());
        recruitUserInfoService.updateRecruitUserInfo(recruitUserInfo);
        //判空，如果存在则更新，不存在则新增
        if(StringUtils.isNotNull(resumeOtherInfo)){
            resumeOtherInfo.setSelfEvaluation(request.getSelfEvaluation());
            return toAjax(recruitResumeOtherInfoService.updateRecruitResumeOtherInfo(resumeOtherInfo));
        }else {
            RecruitResumeOtherInfo resumeOtherInfos = new RecruitResumeOtherInfo();
            resumeOtherInfos.setUserId(getUserId());
            resumeOtherInfos.setSelfEvaluation(request.getSelfEvaluation());
            return toAjax(recruitResumeOtherInfoService.insertRecruitResumeOtherInfo(resumeOtherInfos));
        }
    }

    @ApiOperation("职业技能")
    @PostMapping(value = "/resumeProfessionalSkills")
    public AjaxResult resumeProfessionalSkills(@RequestBody @Validated ResumeProfessionalSkillsRequest request)
    {
        //根据用户id查询
        RecruitResumeProfessionalSkills resumeProfessionalSkills = recruitResumeProfessionalSkillsService.selectRecruitResumeProfessionalSkillsByIdAndUserId(request.getId(), getUserId());
        RecruitUserInfo recruitUserInfo = new RecruitUserInfo();
        recruitUserInfo.setId(getUserId());
        recruitUserInfoService.updateRecruitUserInfo(recruitUserInfo);
        //判空，如果存在则更新，不存在则新增
        if(StringUtils.isNotNull(resumeProfessionalSkills)){
            BeanUtils.copyBeanProp(resumeProfessionalSkills, request);
            return toAjax(recruitResumeProfessionalSkillsService.updateRecruitResumeProfessionalSkills(resumeProfessionalSkills));
        }else {
            RecruitResumeProfessionalSkills resumeProfessionalSkillss = new RecruitResumeProfessionalSkills();
            BeanUtils.copyBeanProp(resumeProfessionalSkillss, request);
            resumeProfessionalSkillss.setUserId(getUserId());
            return toAjax(recruitResumeProfessionalSkillsService.insertRecruitResumeProfessionalSkills(resumeProfessionalSkillss));
        }
    }

    @ApiOperation("查询在线简历信息")
    @GetMapping(value = "/getResumeInfo", produces = {"application/json"})
    @ApiResponses({
            @ApiResponse(code = 200, message = "返回实体", response = ResumeInfoResponse.class)
    })
    public AjaxResult getResumeInfo(@NotBlank(message = "用户id不能为空") Long userInfoId)
    {
        ResumeInfoResponse resumeInfoResponse = recruitUserInfoService.getResumeInfo(userInfoId);
        if(resumeInfoResponse != null){

            //企业收藏用户数量
            RecruitCollect collect = new RecruitCollect();
            collect.setPublisherId(getUserId());
            collect.setCollectType("2");
            collect.setUserId(userInfoId);
            RecruitCollect collects = recruitCollectService.getRecruitCollect(collect);
            if(collects == null){
                resumeInfoResponse.setFavoriteUsers(0);
            }else {
                resumeInfoResponse.setCollectId(collects.getId());
                resumeInfoResponse.setFavoriteUsers(1);
            }

            //个人信息
            if(resumeInfoResponse.getRecruitUserInfo() != null){
                if(resumeInfoResponse.getRecruitUserInfo().getTopping() == null || StringUtils.equals(resumeInfoResponse.getRecruitUserInfo().getTopping(), "")){
                    resumeInfoResponse.getRecruitUserInfo().setTopping("0");
                }
                //性别
                if(resumeInfoResponse.getRecruitUserInfo().getSex() != null && !StringUtils.equals(resumeInfoResponse.getRecruitUserInfo().getSex(), "")){
                    resumeInfoResponse.getRecruitUserInfo().setSexName(DictUtils.getDictLabel("sys_user_sex", resumeInfoResponse.getRecruitUserInfo().getSex()));
                    if (resumeInfoResponse.getRecruitUserInfo().getSexName().equals("不限")) {
                        resumeInfoResponse.getRecruitUserInfo().setSexName("性别不限");
                    }
                }else {
                    resumeInfoResponse.getRecruitUserInfo().setSexName("性别不限");
                }
                /*if(!verifyResumeFilling){
                    resumeInfoResponse.getRecruitUserInfo().setUserName(DesensitizeUtil.left(resumeInfoResponse.getRecruitUserInfo().getUserName(), 1));
                    resumeInfoResponse.getRecruitUserInfo().setAuthenticationName(DesensitizeUtil.left(resumeInfoResponse.getRecruitUserInfo().getAuthenticationName(), 1));
                    resumeInfoResponse.getRecruitUserInfo().setIdCard(DesensitizeUtil.around(resumeInfoResponse.getRecruitUserInfo().getIdCard(), 6, 4));
                    resumeInfoResponse.getRecruitUserInfo().setPhone(DesensitizeUtil.around(resumeInfoResponse.getRecruitUserInfo().getPhone(), 3, 4));
                }*/
            }
            //基本资料
            if(resumeInfoResponse.getResumeBasicInfo() != null){
                //工作经验
                if(resumeInfoResponse.getResumeBasicInfo().getWorkExperience() != null && !StringUtils.equals(resumeInfoResponse.getResumeBasicInfo().getWorkExperience(), "")) {
                    resumeInfoResponse.getResumeBasicInfo().setWorkExperienceName(DictUtils.getDictLabel("work_experience_two", resumeInfoResponse.getResumeBasicInfo().getWorkExperience()));
                    if(resumeInfoResponse.getResumeBasicInfo().getWorkExperienceName().equals("不限")){
                        resumeInfoResponse.getResumeBasicInfo().setWorkExperienceName("经验不限");
                    }
                }else {
                    resumeInfoResponse.getResumeBasicInfo().setWorkExperienceName("经验不限");
                }
                //最高学历
                if(resumeInfoResponse.getResumeBasicInfo().getEducation() != null && !StringUtils.equals(resumeInfoResponse.getResumeBasicInfo().getEducation(), "")) {
                    resumeInfoResponse.getResumeBasicInfo().setEducationName(DictUtils.getDictLabel("background_type", resumeInfoResponse.getResumeBasicInfo().getEducation()));
                    if (resumeInfoResponse.getResumeBasicInfo().getEducationName().equals("不限")) {
                        resumeInfoResponse.getResumeBasicInfo().setEducationName("学历不限");
                    }
                }else {
                    resumeInfoResponse.getResumeBasicInfo().setEducationName("学历不限");
                }
                //所在城市
                if(resumeInfoResponse.getResumeBasicInfo().getCity() != null && !StringUtils.equals(resumeInfoResponse.getResumeBasicInfo().getCity(), "")){
                    String[] city = resumeInfoResponse.getResumeBasicInfo().getCity().split(",");
                    StringBuilder cityName = new StringBuilder();
                    for(String ss : city){
                        if(StringUtils.equals(cityName, "")){
                            cityName.append(sysRegionService.getMap(ss));
                        }else {
                            cityName.append(",").append(sysRegionService.getMap(ss));
                        }
                    }
                    resumeInfoResponse.getResumeBasicInfo().setAreaName(String.valueOf(cityName));
                }
            }
            //求职意愿
            if(resumeInfoResponse.getResumeJobDesire() != null){
                //求职状态
                if(resumeInfoResponse.getResumeJobDesire().getJobStatus() != null && !StringUtils.equals(resumeInfoResponse.getResumeJobDesire().getJobStatus(), "")) {
                    resumeInfoResponse.getResumeJobDesire().setJobStatusName(DictUtils.getDictLabel("job_status", resumeInfoResponse.getResumeJobDesire().getJobStatus()));
                }
                //工作职能
                if(resumeInfoResponse.getResumeJobDesire().getWorkFunction() != null){
                    String[] ss = resumeInfoResponse.getResumeJobDesire().getWorkFunction().split(",");
                    StringBuilder workFunctionName = new StringBuilder();
                    for(String s : ss){
                        if(StringUtils.equals(workFunctionName, "")){
                            workFunctionName.append(recruitPositionService.getMap(s));
                        }else {
                            workFunctionName.append(",").append(recruitPositionService.getMap(s));
                        }
                    }
                    resumeInfoResponse.getResumeJobDesire().setWorkFunctionName(String.valueOf(workFunctionName));
                }
                //期望岗位
                if(resumeInfoResponse.getResumeJobDesire().getExpectedPosition() != null){
                    resumeInfoResponse.getResumeJobDesire().setExpectedPositionName(recruitPositionService.getMap(resumeInfoResponse.getResumeJobDesire().getExpectedPosition()));
                }
                //期望城市
                if(resumeInfoResponse.getResumeJobDesire().getExpectedCity() != null && !StringUtils.equals(resumeInfoResponse.getResumeJobDesire().getExpectedCity(), "")){
                    String[] expectedCity = resumeInfoResponse.getResumeJobDesire().getExpectedCity().split(",");
                    StringBuilder expectedCityName = new StringBuilder();
                    for(String ss : expectedCity){
                        if(StringUtils.equals(expectedCityName, "")){
                            expectedCityName.append(sysRegionService.getMap(ss));
                        }else {
                            expectedCityName.append(",").append(sysRegionService.getMap(ss));
                        }
                    }
                    resumeInfoResponse.getResumeJobDesire().setExpectedCityName(String.valueOf(expectedCityName));
                }

                try {
                    //最高薪资
                    if(resumeInfoResponse.getResumeJobDesire().getMaximumSalary() != null && !StringUtils.equals(resumeInfoResponse.getResumeJobDesire().getMaximumSalary(), "")) {
                        BigDecimal maximumSalary = new BigDecimal(resumeInfoResponse.getResumeJobDesire().getMaximumSalary());
                        resumeInfoResponse.getResumeJobDesire().setMaximumSalary(maximumSalary.divide(new BigDecimal(1000))+"K");
                    }
                }catch (Exception s){
                    log.error("最高薪资转换问题，无需关注");
                }
                try {
                    //最低薪资
                    if(resumeInfoResponse.getResumeJobDesire().getMinimumWage() != null && !StringUtils.equals(resumeInfoResponse.getResumeJobDesire().getMinimumWage(), "")) {
                        BigDecimal minimumWage = new BigDecimal(resumeInfoResponse.getResumeJobDesire().getMinimumWage());
                        resumeInfoResponse.getResumeJobDesire().setMinimumWage(minimumWage.divide(new BigDecimal(1000))+"K");
                    }
                }catch (Exception s){
                    log.error("最低薪资转换问题，无需关注");
                }
                //如果最低最高金额相同，则最高金额赋值为空
                if(resumeInfoResponse.getResumeJobDesire().getMinimumWage() != null && resumeInfoResponse.getResumeJobDesire().getMaximumSalary() != null) {
                    if (resumeInfoResponse.getResumeJobDesire().getMinimumWage().equals(resumeInfoResponse.getResumeJobDesire().getMaximumSalary())) {
                        resumeInfoResponse.getResumeJobDesire().setMaximumSalary(null);
                    }
                }

            }
            //职业技能
            RecruitResumeProfessionalSkills resumeProfessionalSkills = new RecruitResumeProfessionalSkills();
            resumeProfessionalSkills.setUserId(userInfoId);
            List<RecruitResumeProfessionalSkills> list = recruitResumeProfessionalSkillsService.selectRecruitResumeProfessionalSkillsList(resumeProfessionalSkills);
            list.forEach(e->{
                e.setProficiencyName(DictUtils.getDictLabel("proficiency_type", e.getProficiency()));
            });
            resumeInfoResponse.setResumeProfessionalSkillsList(list);
            //项目经历
            RecruitResumeProjectExperience resumeProjectExperience = new RecruitResumeProjectExperience();
            resumeProjectExperience.setUserId(userInfoId);
            resumeInfoResponse.setResumeProjectExperienceList(recruitResumeProjectExperienceService.selectRecruitResumeProjectExperienceList(resumeProjectExperience));
            //工作经历
            RecruitResumeWorkHistory resumeWorkHistory = new RecruitResumeWorkHistory();
            resumeWorkHistory.setUserId(userInfoId);
            List<RecruitResumeWorkHistory> lista  = recruitResumeWorkHistoryService.selectRecruitResumeWorkHistoryList(resumeWorkHistory);
            lista.forEach(e->{
                if(e.getPosition() != null && !StringUtils.equals(e.getPosition(), "")) {
                    e.setPositionName(recruitPositionService.getMap(e.getPosition()));
                }
            });
            resumeInfoResponse.setResumeWorkHistoryList(lista);
            //教育经历
            RecruitResumeEducation resumeEducation = new RecruitResumeEducation();
            resumeEducation.setUserId(userInfoId);
            List<RecruitResumeEducation> lists = recruitResumeEducationService.selectRecruitResumeEducationList(resumeEducation);
            lists.forEach(s->{
                if(s.getHighestEducationName() != null && !StringUtils.equals(s.getHighestEducationName(), "")) {
                    s.setHighestEducationName(DictUtils.getDictLabel("background_type", s.getHighestEducation()));
                    if (s.getHighestEducationName().equals("不限")) {
                        s.setHighestEducationName("学历不限");
                    }
                }else {
                    s.setHighestEducationName("学历不限");
                }
            });
            resumeInfoResponse.setResumeEducationList(lists);
        }
        return success(resumeInfoResponse);
    }

    @ApiOperation("上传附件简历,通过通用上传上传简历，将返回地址填入")
    @PostMapping("/attachmentResume")
    public AjaxResult attachmentResume(@RequestBody @Validated AttachmentResumeRequest request) {
        return success(recruitUserInfoService.setAttachmentResume(request));
    }

    @ApiOperation("附件简历重命名")
    @PostMapping("/attachmentResumeRename")
    public AjaxResult attachmentResumeRename(@RequestBody @Validated AttachmentResumeRequest request) {
        return success(recruitUserInfoService.setAttachmentResume(request));
    }

    @ApiOperation("删除附件简历")
    @DeleteMapping("/delAttachmentResume")
    public AjaxResult delAttachmentResume() {
        return success(recruitUserInfoService.delAttachmentResume());
    }

    @ApiOperation("查看附件简历")
    @GetMapping("/getAttachmentResume")
    public AjaxResult getAttachmentResume() {
        return success(recruitUserInfoService.getAttachmentResume());
    }

    @ApiOperation("删除职业技能")
    @PostMapping("/removeProfessionalSkills")
    public AjaxResult removeProfessionalSkills(@RequestBody @Validated RemoveRequest request)
    {
        return toAjax(recruitResumeProfessionalSkillsService.deleteRecruitResumeProfessionalSkillsById(request.getId()));
    }

    @ApiOperation("删除项目经历")
    @PostMapping("/removeProjectExperience")
    public AjaxResult removeProjectExperience(@RequestBody @Validated RemoveRequest request)
    {
        return toAjax(recruitResumeProjectExperienceService.deleteRecruitResumeProjectExperienceById(request.getId()));
    }

    @ApiOperation("删除工作经历")
    @PostMapping("/removeWorkHistory")
    public AjaxResult removeWorkHistory(@RequestBody @Validated RemoveRequest request)
    {
        return toAjax(recruitResumeWorkHistoryService.deleteRecruitResumeWorkHistoryById(request.getId()));
    }

    @ApiOperation("删除教育经历")
    @PostMapping("/removeEducation")
    public AjaxResult removeEducation(@RequestBody @Validated RemoveRequest request)
    {
        return toAjax(recruitResumeEducationService.deleteRecruitResumeEducationById(request.getId()));
    }


    @ApiOperation("查询简历列表 查询类别queryCategory，查询类别，1推荐，2最新，3置顶")
    @GetMapping(value = "/getResumeList", produces = {"application/json"})
    @ApiResponses({
            @ApiResponse(code = 200, message = "返回实体", response = ResumeInfoResponse.class)
    })
    public TableDataInfo getResumeList(ResumeListRequest request)
    {
        startPage();
        try {
            request.setPublisherId(getUserId());
        }catch (Exception e){
            log.error("获取用户id失败，该用户未登录，可忽略该信息");
        }

        if(request.getExpectedPosition() != null && !StringUtils.equals(request.getExpectedPosition(), "")) {
            Integer grade = recruitPositionService.getMap1(request.getExpectedPosition());
            if (grade != null) {
                request.setGrade(grade);
            }
        }

        List<ResumeInfoResponse> lists = recruitUserInfoService.getResumeList(request);
        lists.forEach(s->{
            //个人信息
            if(s.getRecruitUserInfo() != null){
                if(s.getRecruitUserInfo().getTopping() == null || StringUtils.equals(s.getRecruitUserInfo().getTopping(), "")){
                    s.getRecruitUserInfo().setTopping("0");
                }
                //性别
                if(s.getRecruitUserInfo().getSex() != null && !StringUtils.equals(s.getRecruitUserInfo().getSex(), "")){
                    s.getRecruitUserInfo().setSexName(DictUtils.getDictLabel("sys_user_sex", s.getRecruitUserInfo().getSex()));
                    if (s.getRecruitUserInfo().getSexName().equals("不限")) {
                        s.getRecruitUserInfo().setSexName("性别不限");
                    }
                }else {
                    s.getRecruitUserInfo().setSexName("性别不限");
                }

                if(request.getPublisherId() == null){
                    s.getRecruitUserInfo().setUserName(DesensitizeUtil.left(s.getRecruitUserInfo().getUserName(), 1));
                    s.getRecruitUserInfo().setAuthenticationName(DesensitizeUtil.left(s.getRecruitUserInfo().getAuthenticationName(), 1));
                    s.getRecruitUserInfo().setIdCard(DesensitizeUtil.around(s.getRecruitUserInfo().getIdCard(), 6, 4));
                    s.getRecruitUserInfo().setPhone(DesensitizeUtil.around(s.getRecruitUserInfo().getPhone(), 3, 4));
                }
            }
            //基本资料
            if(s.getResumeBasicInfo() != null){
                //工作经验
                if(s.getResumeBasicInfo().getWorkExperience() != null && !StringUtils.equals(s.getResumeBasicInfo().getWorkExperience(), "")) {
                    s.getResumeBasicInfo().setWorkExperienceName(DictUtils.getDictLabel("work_experience_two", s.getResumeBasicInfo().getWorkExperience()));
                    if(s.getResumeBasicInfo().getWorkExperienceName().equals("不限")){
                        s.getResumeBasicInfo().setWorkExperienceName("经验不限");
                    }
                }else {
                    s.getResumeBasicInfo().setWorkExperienceName("经验不限");
                }
                //最高学历
                if(s.getResumeBasicInfo().getEducation() != null && !StringUtils.equals(s.getResumeBasicInfo().getEducation(), "")) {
                    s.getResumeBasicInfo().setEducationName(DictUtils.getDictLabel("background_type", s.getResumeBasicInfo().getEducation()));
                    if (s.getResumeBasicInfo().getEducationName().equals("不限")) {
                        s.getResumeBasicInfo().setEducationName("学历不限");
                    }
                }else {
                    s.getResumeBasicInfo().setEducationName("学历不限");
                }

                //所在城市
                if(s.getResumeBasicInfo().getCity() != null && !StringUtils.equals(s.getResumeBasicInfo().getCity(), "")){
                    String[] city = s.getResumeBasicInfo().getCity().split(",");
                    StringBuilder cityName = new StringBuilder();
                    for(String ss : city){
                        if(StringUtils.equals(cityName, "")){
                            cityName.append(sysRegionService.getMap(ss));
                        }else {
                            cityName.append(",").append(sysRegionService.getMap(ss));
                        }
                    }
                    s.getResumeBasicInfo().setAreaName(String.valueOf(cityName));
                }
            }
            //求职意愿
            if(s.getResumeJobDesire() != null){
                //求职状态
                if(s.getResumeJobDesire().getJobStatus() != null && !StringUtils.equals(s.getResumeJobDesire().getJobStatus(), "")) {
                    s.getResumeJobDesire().setJobStatusName(DictUtils.getDictLabel("job_status", s.getResumeJobDesire().getJobStatus()));
                }
                if(s.getResumeJobDesire().getWorkFunction() != null && !StringUtils.equals(s.getResumeJobDesire().getWorkFunction(), "")) {
                    String[] workFunctions = s.getResumeJobDesire().getWorkFunction().split(",");
                    StringBuilder workFunctionName = new StringBuilder();
                    for(String ss : workFunctions){
                        if(StringUtils.equals(workFunctionName, "")){
                            workFunctionName.append(recruitPositionService.getMap(ss));
                        }else {
                            workFunctionName.append(",").append(recruitPositionService.getMap(ss));
                        }
                    }
                    s.getResumeJobDesire().setWorkFunctionName(String.valueOf(workFunctionName));
                }

                //期望城市
                if(s.getResumeJobDesire().getExpectedCity() != null && !StringUtils.equals(s.getResumeJobDesire().getExpectedCity(), "")){
                    String[] expectedCity = s.getResumeJobDesire().getExpectedCity().split(",");
                    StringBuilder expectedCityName = new StringBuilder();
                    for(String ss : expectedCity){
                        if(StringUtils.equals(expectedCityName, "")){
                            expectedCityName.append(sysRegionService.getMap(ss));
                        }else {
                            expectedCityName.append(",").append(sysRegionService.getMap(ss));
                        }
                    }
                    s.getResumeJobDesire().setExpectedCityName(String.valueOf(expectedCityName));
                }


                try {
                    //最高薪资
                    if(s.getResumeJobDesire().getMaximumSalary() != null && !StringUtils.equals(s.getResumeJobDesire().getMaximumSalary(), "")) {
                        BigDecimal maximumSalary = new BigDecimal(s.getResumeJobDesire().getMaximumSalary());
                        s.getResumeJobDesire().setMaximumSalary(maximumSalary.divide(new BigDecimal(1000))+"K");
                    }
                }catch (Exception ss){
                    log.error("最高薪资转换问题，无需关注");
                }
                try {
                    //最低薪资
                    if(s.getResumeJobDesire().getMinimumWage() != null && !StringUtils.equals(s.getResumeJobDesire().getMinimumWage(), "")) {
                        BigDecimal minimumWage = new BigDecimal(s.getResumeJobDesire().getMinimumWage());
                        s.getResumeJobDesire().setMinimumWage(minimumWage.divide(new BigDecimal(1000))+"K");
                    }
                }catch (Exception ss){
                    log.error("最低薪资转换问题，无需关注");
                }
                //如果最低最高金额相同，则最高金额赋值为空
                if(s.getResumeJobDesire().getMinimumWage() != null && s.getResumeJobDesire().getMaximumSalary() != null) {
                    if (s.getResumeJobDesire().getMinimumWage().equals(s.getResumeJobDesire().getMaximumSalary())) {
                        s.getResumeJobDesire().setMaximumSalary(null);
                    }
                }
            }
            //职业技能
            /*RecruitResumeProfessionalSkills resumeProfessionalSkills = new RecruitResumeProfessionalSkills();
            try {
                resumeProfessionalSkills.setUserId(s.getRecruitUserInfo().getId());
                List<RecruitResumeProfessionalSkills> list = recruitResumeProfessionalSkillsService.selectRecruitResumeProfessionalSkillsList(resumeProfessionalSkills);
                list.forEach(e->{
                    //熟练程度
                    if(e.getProficiency() != null && !StringUtils.equals(e.getProficiency(), "")) {
                        e.setProficiencyName(DictUtils.getDictLabel("background_type", e.getProficiency()));
                    }
                });
                s.setResumeProfessionalSkillsList(list);
                //工作经历
                RecruitResumeWorkHistory resumeWorkHistory = new RecruitResumeWorkHistory();
                resumeWorkHistory.setUserId(s.getRecruitUserInfo().getId());
                s.setResumeWorkHistoryList(recruitResumeWorkHistoryService.selectRecruitResumeWorkHistoryList(resumeWorkHistory));
            }catch (Exception e){
                log.error("获取用户id失败，该用户未登录，可忽略该信息");
            }*/
        });
        return getDataTable(lists);
    }


    @ApiOperation("查询校验用户是否填写简历")
    @GetMapping("/getVerifyResumeFilling")
    public AjaxResult getVerifyResumeFilling() {
        try {
            return success(recruitResumeBasicInfoService.getVerifyResumeFilling(getUserId()));
        }catch (Exception e){
            log.error("获取用户id失败，该用户未登录，可忽略该信息");
        }
        return error();
    }

    @ApiOperation("查看简历完成度")
    @GetMapping(value = "/getResumeCompletionRate", produces = {"application/json"})
    @ApiResponses({
            @ApiResponse(code = 200, message = "返回实体", response = ResumeCompletionRateResponse.class)
    })
    public AjaxResult getResumeCompletionRate() {

        Long userId = getUserId();

        int ss = 0;
        ResumeCompletionRateResponse response = new ResumeCompletionRateResponse();
        //基本信息
        ResumeInfoResponse resumeInfoResponse = recruitUserInfoService.getResumeInfo(userId);
        if(resumeInfoResponse.getResumeBasicInfo().getWorkExperience() == null || resumeInfoResponse.getResumeBasicInfo().getEducation() == null){
            response.setBasicInfo(false);
        }else {
            ss = ss+20;
            response.setBasicInfo(true);
        }

        //职业技能
        RecruitResumeProfessionalSkills professionalSkills = new RecruitResumeProfessionalSkills();
        professionalSkills.setUserId(userId);
        List<RecruitResumeProfessionalSkills> professionalSkillsList = recruitResumeProfessionalSkillsService.selectRecruitResumeProfessionalSkillsList(professionalSkills);
        if(professionalSkillsList.size() > 0){
            ss = ss+10;
            response.setProfessionalSkills(true);
        }else {
            response.setProfessionalSkills(false);
        }

        //工作经历
        RecruitResumeWorkHistory resumeWorkHistory = new RecruitResumeWorkHistory();
        resumeWorkHistory.setUserId(userId);
        List<RecruitResumeWorkHistory> resumeWorkHistoryList = recruitResumeWorkHistoryService.selectRecruitResumeWorkHistoryList(resumeWorkHistory);
        if(resumeWorkHistoryList.size() > 0){
            ss = ss+20;
            response.setResumeWorkHistory(true);
        }else {
            response.setResumeWorkHistory(false);
        }

        //教育经历
        RecruitResumeEducation resumeEducation = new RecruitResumeEducation();
        resumeEducation.setUserId(userId);
        List<RecruitResumeEducation> resumeEducationList = recruitResumeEducationService.selectRecruitResumeEducationList(resumeEducation);
        if(resumeEducationList.size() > 0){
            ss = ss+10;
            response.setResumeEducation(true);
        }else {
            response.setResumeEducation(false);
        }

        //项目经历
        RecruitResumeProjectExperience resumeProjectExperience = new RecruitResumeProjectExperience();
        resumeProjectExperience.setUserId(userId);
        List<RecruitResumeProjectExperience> resumeProjectExperienceList = recruitResumeProjectExperienceService.selectRecruitResumeProjectExperienceList(resumeProjectExperience);
        if(resumeProjectExperienceList.size() > 0){
            ss = ss+10;
            response.setResumeProjectExperience(true);
        }else {
            response.setResumeProjectExperience(false);
        }

        //求职意愿 jobStatus
        if(resumeInfoResponse.getResumeJobDesire().getExpectedPosition() == null ||
                resumeInfoResponse.getResumeJobDesire().getWorkFunction() == null ||
                resumeInfoResponse.getResumeJobDesire().getJobStatus() == null){
            response.setJobSeekingIntention(false);
        }else {
            ss = ss+20;
            response.setJobSeekingIntention(true);
        }

        //其它信息，自我评价
        if(resumeInfoResponse.getResumeOtherInfo().getSelfEvaluation() == null){
            response.setOtherInfo(false);
        }else {
            ss = ss+10;
            response.setOtherInfo(true);
        }
        response.setCompletionDegree(ss);
        return success(response);
    }

}
