package com.recruit.core.domain.request.merchant;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Auther: <PERSON> kong
 * @Date: 2023/6/2 9:15
 * @Description:
 */
@Data
public class MerchantAddressRequest {

    @ApiModelProperty("所在地区")
    private String region;

    @ApiModelProperty("详细地址")
    private String address;

    @ApiModelProperty("经度")
    private String longitude;

    @ApiModelProperty("纬度")
    private String latitude;
}
