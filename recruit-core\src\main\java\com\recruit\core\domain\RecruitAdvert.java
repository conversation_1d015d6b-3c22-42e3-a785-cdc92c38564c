package com.recruit.core.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.recruit.common.annotation.Excel;
import com.recruit.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 广告对象 recruit_advert
 *
 * <AUTHOR>
 * @date 2023-03-15
 */
@Data
public class RecruitAdvert extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 图片地址 */
    @Excel(name = "图片地址")
    private String picUrl;

    /** 跳转地址 */
    @Excel(name = "跳转地址")
    private String linkUrl;

    /** 状态 */
    @Excel(name = "状态")
    private String status;

    /** 类型，时间控制，永久 */
    @Excel(name = "类型")
    private String type;

    @Excel(name = "展示，时间控制，永久")
    private String parade;

    @Excel(name = "位置")
    private String seat;

    @Excel(name = "招聘会")
    private String jobFairs = "N";

    @Excel(name = "人才引进")
    private String talentIntroduction = "N";

    @Excel(name = "直播")
    private String liveAdHoc = "N";

    /** 开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "开始时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date startTime;

    /** 结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "结束时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date endTime;

    @ApiModelProperty("期数")
    private String numberOfPeriods;
}
