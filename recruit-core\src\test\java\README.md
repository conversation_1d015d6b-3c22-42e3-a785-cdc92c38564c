# 系统用户弱口令检测测试

## 概述

本目录包含了系统用户弱口令检测的完整测试套件，用于验证密码强度检测功能的有效性。

## 文件说明

### 测试类文件
1. **`SysUserPasswordStrengthTest.java`** - 完整的JUnit测试类
   - 包含多种测试场景的单元测试
   - 支持JUnit框架运行
   - 提供详细的测试报告输出

2. **`PasswordStrengthTestRunner.java`** - 测试运行器
   - 独立的测试运行程序
   - 可以批量执行所有测试场景
   - 生成格式化的测试报告

3. **`SimplePasswordTest.java`** - 简化的独立测试类
   - 不依赖外部框架
   - 包含完整的密码验证逻辑
   - 可直接运行查看测试结果

### 文档文件
4. **`PasswordTestReport.md`** - 详细测试报告
   - 包含所有测试用例的执行结果
   - 提供密码安全建议
   - 统计分析和结论

5. **`README.md`** - 本说明文件

## 密码强度检测规则

### 基本要求
- **最小长度**: 8个字符
- **复杂度**: 至少包含以下4种字符类型中的3种：
  - 大写字母 (A-Z)
  - 小写字母 (a-z)
  - 数字 (0-9)
  - 特殊字符 (!@#$%^&*等)

### 安全检查
- **常见弱口令检测**: 防止使用123456、password、admin等常见弱口令
- **用户名相似性检测**: 密码不能与用户名相同或包含用户名
- **连续字符检测**: 避免使用123、abc等连续字符序列

### 评分系统
- **长度评分**: 8位以上得20分，12位以上额外得10分
- **复杂度评分**: 每种字符类型得15分
- **安全性评分**: 非常见弱口令得20分
- **独立性评分**: 与用户名不相似得15分
- **额外评分**: 无连续字符序列得10分

## 如何运行测试

### 方法1: 使用JUnit运行（推荐）
```bash
# 编译测试类
javac -cp "path/to/junit.jar:path/to/project/classes" SysUserPasswordStrengthTest.java

# 运行JUnit测试
java -cp "path/to/junit.jar:path/to/project/classes:." org.junit.runner.JUnitCore SysUserPasswordStrengthTest
```

### 方法2: 运行独立测试程序
```bash
# 编译
javac SimplePasswordTest.java

# 运行
java SimplePasswordTest
```

### 方法3: 在IDE中运行
1. 将测试文件导入到IDE项目中
2. 确保项目包含JUnit依赖（对于JUnit版本）
3. 右键点击测试类选择"Run"

## 测试用例说明

### 强密码测试用例
- `AdminPass2024!` - 包含所有字符类型的强密码
- `MySecure@Pass456` - 高复杂度密码
- `ComplexPass2024#` - 符合所有安全要求

### 弱密码测试用例
- `Pass1!` - 长度不足
- `password` - 复杂度不够，常见弱口令
- `123456` - 常见弱口令
- `testuser` - 与用户名相同

## 集成到项目中

### 1. 导入密码验证器
```java
// 将PasswordStrengthValidator类集成到项目中
public class PasswordService {
    private PasswordStrengthValidator validator = new PasswordStrengthValidator();
    
    public ValidationResult validateUserPassword(SysUser user) {
        return validator.validatePassword(user);
    }
}
```

### 2. 在用户注册时使用
```java
@PostMapping("/register")
public ResponseEntity<?> registerUser(@RequestBody SysUser user) {
    ValidationResult result = passwordService.validateUserPassword(user);
    
    if (!result.isPassed()) {
        return ResponseEntity.badRequest()
            .body(new ErrorResponse("密码强度不足", result.getFailureReasons()));
    }
    
    // 继续注册流程...
}
```

### 3. 在密码修改时使用
```java
@PostMapping("/change-password")
public ResponseEntity<?> changePassword(@RequestBody ChangePasswordRequest request) {
    SysUser user = new SysUser();
    user.setUserName(request.getUsername());
    user.setPassword(request.getNewPassword());
    
    ValidationResult result = passwordService.validateUserPassword(user);
    
    if (!result.isPassed()) {
        return ResponseEntity.badRequest()
            .body(new ErrorResponse("新密码强度不足", result.getFailureReasons()));
    }
    
    // 继续密码修改流程...
}
```

## 自定义配置

### 修改密码策略
可以通过修改`PasswordStrengthValidator`类中的常量来调整密码策略：

```java
private static final int MIN_LENGTH = 8; // 最小长度
private static final Set<String> COMMON_WEAK_PASSWORDS = new HashSet<>(Arrays.asList(
    // 添加或删除常见弱口令
));
```

### 调整评分权重
可以修改`validatePassword`方法中的评分逻辑来调整各项检查的权重。

## 扩展功能

### 1. 密码历史检查
```java
public boolean isPasswordReused(String username, String newPassword) {
    // 检查用户是否重复使用最近的密码
    List<String> passwordHistory = getPasswordHistory(username);
    return passwordHistory.contains(hashPassword(newPassword));
}
```

### 2. 密码过期检查
```java
public boolean isPasswordExpired(SysUser user) {
    // 检查密码是否已过期
    Date lastPasswordChange = user.getPasswordChangeDate();
    long daysSinceChange = ChronoUnit.DAYS.between(
        lastPasswordChange.toInstant(), 
        Instant.now()
    );
    return daysSinceChange > PASSWORD_EXPIRY_DAYS;
}
```

### 3. 账户锁定功能
```java
public void handleFailedLogin(String username) {
    int failedAttempts = getFailedLoginAttempts(username);
    if (failedAttempts >= MAX_FAILED_ATTEMPTS) {
        lockAccount(username);
    }
}
```

## 注意事项

1. **安全存储**: 密码应该使用安全的哈希算法（如BCrypt）进行存储
2. **传输安全**: 密码传输应该使用HTTPS加密
3. **日志安全**: 不要在日志中记录明文密码
4. **定期更新**: 定期更新弱口令字典和检测规则
5. **用户教育**: 向用户提供密码安全最佳实践指导

## 联系信息

如有问题或建议，请联系开发团队。

---
*文档更新时间: 2025-07-30*
