package com.recruit.core.domain.request.position;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @Auther: <PERSON> kong
 * @Date: 2023/3/26 10:25
 * @Description:
 */
@Data
@ApiModel("添加工作地址1")
public class WorkAddressRequest {

    @ApiModelProperty("主键")
    private Long id;


    @NotBlank(message = "工作地址不能为空")
    @ApiModelProperty("工作地址")
    private String workAddress;

    /** 经度 */
    @ApiModelProperty("经度")
    private String longitude;

    /** 纬度 */
    @ApiModelProperty("纬度")
    private String latitude;

    /** 门牌号 */
    @ApiModelProperty("门牌号")
    private String houseNumber;


}
