package com.recruit.core.domain.response;

import com.recruit.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

/**
 * @Auther: <PERSON> kong
 * @Date: 2023/4/21 20:12
 * @Description:
 */

@Data
@ApiModel("热门企业返回")
public class OnlineJobFairsResponse {

    @ApiModelProperty("企业id")
    private Long enterpriseId;

    @ApiModelProperty("企业名称")
    private String enterpriseName;

    @ApiModelProperty("企业logo")
    private String enterpriseLogo;

    @ApiModelProperty("企业性质")
    private String enterpriseNature;

    @ApiModelProperty("企业性质名称")
    private String enterpriseNatureName;

    @ApiModelProperty("企业规模")
    private String scale;

    @ApiModelProperty("企业规模名称")
    private String scaleName;

    @ApiModelProperty("所属行业")
    private String industry;

    @ApiModelProperty("所属行业名称")
    private String industryName;

    @ApiModelProperty("蓝V状态")
    private String state;

    @ApiModelProperty("职位信息返回")
    List<PositionInfoResponse> positionInfoList;
}
