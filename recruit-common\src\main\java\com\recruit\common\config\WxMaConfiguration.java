package com.recruit.common.config;


import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.api.impl.WxMaServiceImpl;
import cn.binarywang.wx.miniapp.config.WxMaConfig;
import cn.binarywang.wx.miniapp.config.impl.WxMaDefaultConfigImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Slf4j
@Configuration
@EnableConfigurationProperties(WxMaProperties.class)
public class WxMaConfiguration {

	private final WxMaProperties properties;

	@Autowired
	public WxMaConfiguration(WxMaProperties properties) {
		this.properties = properties;
	}

	@Bean
	@ConditionalOnMissingBean
	public WxMaConfig maConfig() {
		WxMaDefaultConfigImpl config = new WxMaDefaultConfigImpl();
		config.setAppid(this.properties.getAppid());
		config.setSecret(this.properties.getSecret());
		config.setToken(this.properties.getToken());
		config.setAesKey(this.properties.getAesKey());
		config.setMsgDataFormat(this.properties.getMsgDataFormat());
		return config;
	}

	@Bean
	@ConditionalOnMissingBean
	public WxMaService wxMaService(WxMaConfig maConfig) {
		WxMaService service = new WxMaServiceImpl();
		service.setWxMaConfig(maConfig);
		return service;
	}
}
