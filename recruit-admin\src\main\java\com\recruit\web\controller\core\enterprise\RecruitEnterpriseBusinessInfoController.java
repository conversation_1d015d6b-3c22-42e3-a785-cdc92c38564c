package com.recruit.web.controller.core.enterprise;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.recruit.core.domain.RecruitEnterpriseUsersRel;
import com.recruit.core.domain.response.CompanyInfoResponse;
import com.recruit.core.service.IRecruitEnterpriseUsersRelService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.recruit.common.annotation.Log;
import com.recruit.common.core.controller.BaseController;
import com.recruit.common.core.domain.AjaxResult;
import com.recruit.common.enums.BusinessType;
import com.recruit.core.domain.RecruitEnterpriseBusinessInfo;
import com.recruit.core.service.IRecruitEnterpriseBusinessInfoService;
import com.recruit.common.utils.poi.ExcelUtil;
import com.recruit.common.core.page.TableDataInfo;

/**
 * 企业工商信息Controller
 *
 * <AUTHOR>
 * @date 2023-03-21
 */
@RestController
@RequestMapping("/core/enterpriseBusinessInfo")
public class RecruitEnterpriseBusinessInfoController extends BaseController
{
    @Autowired
    private IRecruitEnterpriseBusinessInfoService recruitEnterpriseBusinessInfoService;

    @Autowired
    private IRecruitEnterpriseUsersRelService recruitEnterpriseUsersRelService;

    /**
     * 查询企业工商信息列表
     */
    @GetMapping("/list")
    public TableDataInfo list(RecruitEnterpriseBusinessInfo recruitEnterpriseBusinessInfo)
    {
        startPage();
        List<RecruitEnterpriseBusinessInfo> list = recruitEnterpriseBusinessInfoService.selectRecruitEnterpriseBusinessInfoList(recruitEnterpriseBusinessInfo);
        return getDataTable(list);
    }

    /**
     * 导出企业工商信息列表
     */
    @Log(title = "企业工商信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RecruitEnterpriseBusinessInfo recruitEnterpriseBusinessInfo)
    {
        List<RecruitEnterpriseBusinessInfo> list = recruitEnterpriseBusinessInfoService.selectRecruitEnterpriseBusinessInfoList(recruitEnterpriseBusinessInfo);
        ExcelUtil<RecruitEnterpriseBusinessInfo> util = new ExcelUtil<RecruitEnterpriseBusinessInfo>(RecruitEnterpriseBusinessInfo.class);
        util.exportExcel(response, list, "企业工商信息数据");
    }

    /**
     * 获取企业工商信息详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(recruitEnterpriseBusinessInfoService.selectRecruitEnterpriseBusinessInfoById(id));
    }

    /**
     * 新增企业工商信息
     */
    @Log(title = "企业工商信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RecruitEnterpriseBusinessInfo recruitEnterpriseBusinessInfo)
    {
        return toAjax(recruitEnterpriseBusinessInfoService.insertRecruitEnterpriseBusinessInfo(recruitEnterpriseBusinessInfo));
    }

    /**
     * 修改企业工商信息
     */
    @PreAuthorize("@ss.hasPermi('core:enterpriseBusinessInfo:edit')")
    @Log(title = "企业工商信息", businessType = BusinessType.PROCESS)
    @PutMapping
    public AjaxResult edit(@RequestBody RecruitEnterpriseBusinessInfo recruitEnterpriseBusinessInfo)
    {
        recruitEnterpriseBusinessInfo.setId(recruitEnterpriseBusinessInfo.getBusinessInfoId());

        int ss = recruitEnterpriseBusinessInfoService.updateRecruitEnterpriseBusinessInfo(recruitEnterpriseBusinessInfo);
        if(ss > 0) {
            RecruitEnterpriseUsersRel enterpriseUsersRel = new RecruitEnterpriseUsersRel();
            enterpriseUsersRel.setEnterpriseId(recruitEnterpriseBusinessInfo.getEnterpriseId());
            enterpriseUsersRel.setUserId(recruitEnterpriseBusinessInfo.getUserId());
            List<RecruitEnterpriseUsersRel> list = recruitEnterpriseUsersRelService.selectRecruitEnterpriseUsersRelList(enterpriseUsersRel);
            list.forEach(e->{
                e.setBindingStatus("2");
                recruitEnterpriseUsersRelService.updateRecruitEnterpriseUsersRel(e);
            });
        }
        return toAjax(ss);
    }

    /**
     * 删除企业工商信息
     */
    @Log(title = "企业工商信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(recruitEnterpriseBusinessInfoService.deleteRecruitEnterpriseBusinessInfoByIds(ids));
    }


    @Log(title = "获取工商认证信息", businessType = BusinessType.OTHER)
    @PutMapping("/businessUpload")
    public AjaxResult businessUpload(@RequestBody RecruitEnterpriseBusinessInfo recruitEnterpriseBusinessInfo)
    {
        //获取营业执照信息
        CompanyInfoResponse companyInfo = recruitEnterpriseBusinessInfoService.getBusinessSicenseInfo(recruitEnterpriseBusinessInfo.getBusinessUrl());
        return success(companyInfo);
    }

    /**
     * 获取企业工商信息详细信息
     */
    @GetMapping(value = "/getEnterpriseIdInfo")
    public AjaxResult getEnterpriseIdInfo(Long enterpriseId)
    {
        return success(recruitEnterpriseBusinessInfoService.selectEnterpriseBusinessInfoByEnterpriseId(enterpriseId));
    }

}
