package com.recruit.core.domain;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.recruit.common.annotation.Excel;
import com.recruit.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 企业简介对象 recruit_enterprise_info
 *
 * <AUTHOR>
 * @date 2023-03-21
 */
@Data
@ApiModel("企业简介")
public class RecruitEnterpriseInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;
    private Long[] enterpriseInfoId;

    /** 公司名称 */
    @Excel(name = "公司名称", sort = 1)
    @ApiModelProperty("公司名称")
    private String enterpriseName;

    /** 企业logo */
    @ApiModelProperty("企业logo")
    private String enterpriseLogo;

    /** 所属行业 */
    @Excel(name = "所属行业", sort = 2)
    @ApiModelProperty("所属行业")
    private String industry;

    @ApiModelProperty("所属行业")
    private List<String> industrys;

    @ApiModelProperty("区域行业")
    private List<String> regions;

    @ApiModelProperty("所属行业名称")
    private String industryName;

    @ApiModelProperty("行业类别")
    private String industryType;

    /** 企业规模 */
    //@Excel(name = "企业规模", dictType = "scale_enterprises")
    @ApiModelProperty("企业规模")
    private String scale;

    @ApiModelProperty("企业规模名称")
    private String scaleName;

    /** 企业性质 */
    //@Excel(name = "企业性质", dictType = "enterprise_nature")
    @ApiModelProperty("企业性质")
    private String enterpriseNature;

    @ApiModelProperty("企业性质名称")
    private String enterpriseNatureName;

    /** 所在地区 */
    @ApiModelProperty("所在地区")
    private String region;

    //@Excel(name = "所在地区")
    @ApiModelProperty("所在地区名称")
    private String areaName;

    /** 详细地址 */
    //@Excel(name = "详细地址")
    @ApiModelProperty("详细地址")
    private String detailAddress;

    /** 公司简称 */
    //@Excel(name = "公司简称")
    @ApiModelProperty("公司简称")
    private String abbreviation;

    /** 公司福利 */
    @Excel(name = "公司福利", dictType = "company_benefits", sort = 4)
    @ApiModelProperty("公司福利")
    private String materialBenefits;

    /** 企业邮箱 */
    //@Excel(name = "企业邮箱")
    @ApiModelProperty("企业邮箱")
    private String enterpriseMailbox;

    /** 企业网址 */
   // @Excel(name = "企业网址")
    @ApiModelProperty("企业网址")
    private String companyWebsite;

    /** 注册资金 */
    //@Excel(name = "注册资金")
    @ApiModelProperty("注册资金")
    private String registeredCapital;

    /** 融资阶段 */
    //@Excel(name = "融资阶段", dictType = "financing_stage")
    @ApiModelProperty("融资阶段")
    private String financingStage;

    @ApiModelProperty("融资阶段")
    private String financingStageName;

    @ApiModelProperty("查询收藏公司")
    private int collectionCompany;

    @ApiModelProperty("收藏id")
    private Long collectId;

    /** 公司简介 */
    @Excel(name = "公司简介", sort = 3)
    @ApiModelProperty("公司简介")
    private String profile;

    /** 公司相册 */
    //@Excel(name = "公司相册")
    @ApiModelProperty("公司相册")
    private String photoAlbum;

    /** 企业地图 */
   // @Excel(name = "企业地图经度")
    @ApiModelProperty("企业地图经度")
    private String enterpriseMapLongitude;

    //@Excel(name = "企业地图纬度")
    @ApiModelProperty("企业地图纬度")
    private String enterpriseMapLatitude;

    /** 企业id */
   // @Excel(name = "企业id")
    @ApiModelProperty("企业id")
    private Long enterpriseId;

    /** 法人名称 */
    //@Excel(name = "法人名称")
    @ApiModelProperty("法人名称")
    private String corporationName;

    /** 成立时间 */
    @ApiModelProperty("成立时间")
    private String establishedTime;

    /** 营业执照 */
    //@Excel(name = "营业执照")
    @ApiModelProperty("营业执照")
    private String businessUrl;

    /** 状态 0待审核，1审核通过，2审核不通过，3违规封号 */
    //@Excel(name = "状态", dictType = "business_status")
    @ApiModelProperty("状态 0待审核，1审核通过，2审核不通过，3违规封号")
    private String status;

    /** 经营范围 */
    //@Excel(name = "经营范围")
    @ApiModelProperty("经营范围")
    private String businessScope;

    /** 组成形式 */
    //@Excel(name = "组成形式")
    @ApiModelProperty("组成形式")
    private String compositionForm;

    /** 社会信用代码 */
    //@Excel(name = "社会信用代码")
    @ApiModelProperty("社会信用代码")
    private String socialCreditCode;

    /** 地址 */
    //@Excel(name = "地址")
    @ApiModelProperty("地址")
    private String address;

    /** 类型 */
    //@Excel(name = "类型")
    @ApiModelProperty("类型")
    private String type;
    /**
     * 认证表id
     */
    private Long businessInfoId;
    /**
     * 用户id
     */
    private Long userId;

    @ApiModelProperty("地址列表")
    private List<RecruitWorkAddress> workAddressList;


    @ApiModelProperty("蓝V状态")
    private String state;

    @Excel(name = "手机号", sort = 6)
    @ApiModelProperty("手机号")
    private String phone;

    @Excel(name = "姓名", sort = 5)
    @ApiModelProperty("姓名")
    private String userName;

    @ApiModelProperty("点击量")
    private String hitsNum;

}
