package com.recruit.core.domain.request.enterprise;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Auther: <PERSON> kong
 * @Date: 2023/4/2 18:53
 * @Description:
 */
@Data
@ApiModel("设置地图1")
public class SetUpMapRequest {

    @ApiModelProperty("企业id")
    private Long enterpriseId;

    @ApiModelProperty("企业地图经度")
    private String enterpriseMapLongitude;

    @ApiModelProperty("企业地图纬度")
    private String enterpriseMapLatitude;
}
