import java.util.*;
import java.util.regex.Pattern;

/**
 * 简化的系统用户弱口令检测测试
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-30
 */
public class SimplePasswordTest {
    
    public static void main(String[] args) {
        System.out.println(repeatString("=", 80));
        System.out.println("                    系统用户弱口令检测测试报告");
        System.out.println(repeatString("=", 80));
        System.out.println("测试时间: " + new Date());
        System.out.println(repeatString("=", 80));
        
        PasswordValidator validator = new PasswordValidator();
        
        // 测试用例
        TestCase[] testCases = {
            new TestCase("admin", "AdminPass2024!", "强密码测试"),
            new TestCase("user123", "MySecure@Pass456", "强密码测试"),
            new TestCase("test", "Pass1!", "短密码测试"),
            new TestCase("user", "password", "低复杂度密码测试"),
            new TestCase("admin", "123456", "常见弱口令测试"),
            new TestCase("testuser", "testuser", "密码与用户名相同测试"),
            new TestCase("manager", "manager123", "密码与用户名相似测试"),
            new TestCase("developer", "ComplexPass2024#", "复杂密码测试"),
            new TestCase("guest", "12345678", "纯数字密码测试"),
            new TestCase("root", "ABCDEFGH", "纯大写字母密码测试")
        };
        
        int passCount = 0;
        int totalCount = testCases.length;
        
        for (TestCase testCase : testCases) {
            ValidationResult result = validator.validate(testCase.username, testCase.password);
            printTestResult(testCase, result);
            if (result.passed) passCount++;
            System.out.println(repeatString("-", 80));
        }
        
        // 打印总结
        System.out.println("\n" + repeatString("=", 80));
        System.out.println("                           测试总结");
        System.out.println(repeatString("=", 80));
        System.out.println("总测试用例: " + totalCount);
        System.out.println("通过测试: " + passCount);
        System.out.println("失败测试: " + (totalCount - passCount));
        System.out.println("通过率: " + String.format("%.1f%%", (passCount * 100.0 / totalCount)));
        
        System.out.println("\n密码安全建议:");
        System.out.println("1. 密码长度至少8位，建议12位以上");
        System.out.println("2. 包含大写字母、小写字母、数字和特殊字符");
        System.out.println("3. 避免使用常见弱口令");
        System.out.println("4. 密码不能与用户名相同或相似");
        System.out.println("5. 定期更换密码");
        System.out.println(repeatString("=", 80));
    }
    
    static void printTestResult(TestCase testCase, ValidationResult result) {
        System.out.println("\n【" + testCase.description + "】");
        System.out.println("用户名: " + testCase.username);
        System.out.println("密码: " + maskPassword(testCase.password));
        System.out.println("结果: " + (result.passed ? "✓ 通过" : "✗ 失败"));
        System.out.println("评分: " + result.score + "/100");
        
        if (!result.passed && !result.failureReasons.isEmpty()) {
            System.out.println("失败原因:");
            for (String reason : result.failureReasons) {
                System.out.println("  • " + reason);
            }
        }
        
        if (!result.suggestions.isEmpty()) {
            System.out.println("改进建议:");
            for (String suggestion : result.suggestions) {
                System.out.println("  • " + suggestion);
            }
        }
        
        // 密码特征分析
        System.out.println("密码特征:");
        System.out.println("  长度: " + testCase.password.length() + " 字符");
        System.out.println("  大写字母: " + (hasUppercase(testCase.password) ? "✓" : "✗"));
        System.out.println("  小写字母: " + (hasLowercase(testCase.password) ? "✓" : "✗"));
        System.out.println("  数字: " + (hasDigit(testCase.password) ? "✓" : "✗"));
        System.out.println("  特殊字符: " + (hasSpecialChar(testCase.password) ? "✓" : "✗"));
    }
    
    static String maskPassword(String password) {
        if (password == null || password.isEmpty()) return "[空]";
        if (password.length() <= 2) return repeatString("*", password.length());
        return password.charAt(0) + repeatString("*", password.length() - 2) + password.charAt(password.length() - 1);
    }
    
    static boolean hasUppercase(String password) {
        return Pattern.compile("[A-Z]").matcher(password).find();
    }
    
    static boolean hasLowercase(String password) {
        return Pattern.compile("[a-z]").matcher(password).find();
    }
    
    static boolean hasDigit(String password) {
        return Pattern.compile("[0-9]").matcher(password).find();
    }
    
    static boolean hasSpecialChar(String password) {
        return Pattern.compile("[!@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>\\/?]").matcher(password).find();
    }
    
    // 测试用例类
    static class TestCase {
        String username;
        String password;
        String description;
        
        TestCase(String username, String password, String description) {
            this.username = username;
            this.password = password;
            this.description = description;
        }
    }
    
    // 验证结果类
    static class ValidationResult {
        boolean passed;
        int score;
        List<String> failureReasons = new ArrayList<>();
        List<String> suggestions = new ArrayList<>();
    }
    
    // 密码验证器
    static class PasswordValidator {
        private static final int MIN_LENGTH = 8;
        private static final Set<String> WEAK_PASSWORDS = new HashSet<>(Arrays.asList(
            "123456", "password", "admin", "123456789", "qwerty", "abc123", 
            "password123", "admin123", "root", "user", "test", "guest",
            "welcome", "login", "passw0rd", "letmein", "monkey", "dragon"
        ));
        
        ValidationResult validate(String username, String password) {
            ValidationResult result = new ValidationResult();
            List<String> failures = new ArrayList<>();
            List<String> suggestions = new ArrayList<>();
            int score = 0;
            
            // 检查密码长度
            if (password.length() < MIN_LENGTH) {
                failures.add("密码长度不足" + MIN_LENGTH + "位");
                suggestions.add("密码长度至少需要" + MIN_LENGTH + "个字符");
            } else {
                score += 20;
                if (password.length() >= 12) score += 10;
            }
            
            // 检查字符类型复杂度
            int charTypeCount = 0;
            if (hasUppercase(password)) charTypeCount++;
            if (hasLowercase(password)) charTypeCount++;
            if (hasDigit(password)) charTypeCount++;
            if (hasSpecialChar(password)) charTypeCount++;
            
            if (charTypeCount < 3) {
                failures.add("密码复杂度不够，需要包含大写字母、小写字母、数字和特殊字符中的至少3种");
                suggestions.add("建议包含大写字母、小写字母、数字和特殊字符");
            } else {
                score += charTypeCount * 15;
            }
            
            // 检查是否为常见弱口令
            if (WEAK_PASSWORDS.contains(password.toLowerCase())) {
                failures.add("密码是常见的弱口令");
                suggestions.add("避免使用常见的弱口令，如123456、password等");
            } else {
                score += 20;
            }
            
            // 检查密码与用户名的相似性
            if (password.equalsIgnoreCase(username)) {
                failures.add("密码不能与用户名相同");
                suggestions.add("密码应该与用户名完全不同");
            } else if (password.toLowerCase().contains(username.toLowerCase()) || 
                      username.toLowerCase().contains(password.toLowerCase())) {
                failures.add("密码与用户名过于相似");
                suggestions.add("密码不应包含用户名或与用户名相似");
            } else {
                score += 15;
            }
            
            // 额外检查
            if (isSequentialChars(password)) {
                failures.add("密码包含连续字符序列");
                suggestions.add("避免使用连续的字符序列，如123、abc等");
            } else {
                score += 10;
            }
            
            result.passed = failures.isEmpty();
            result.failureReasons = failures;
            result.suggestions = suggestions;
            result.score = Math.min(100, score);
            
            return result;
        }

        private boolean isSequentialChars(String password) {
            String lower = password.toLowerCase();
            return lower.contains("123") || lower.contains("abc") ||
                   lower.contains("qwe") || lower.contains("asd") ||
                   lower.contains("zxc") || lower.contains("321") ||
                   lower.contains("cba");
        }
    }

    /**
     * Java 8兼容的字符串重复方法
     */
    static String repeatString(String str, int count) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < count; i++) {
            sb.append(str);
        }
        return sb.toString();
    }
}
