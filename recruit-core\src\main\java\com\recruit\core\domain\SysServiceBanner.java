package com.recruit.core.domain;

import com.recruit.common.annotation.Excel;
import com.recruit.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 服务banner对象 sys_service_banner
 *
 * <AUTHOR>
 * @date 2023-06-01
 */
@Data
@ApiModel("服务banner")
public class SysServiceBanner extends BaseEntity
{
    private static final long serialVersionUID = 1L;


    private Long id;


    @Excel(name = "图片地址")
    @ApiModelProperty("图片地址")
    private String picUrl;


    @Excel(name = "状态")
    @ApiModelProperty("状态")
    private String status;


    @Excel(name = "类型")
    @ApiModelProperty("类型")
    private String type;


    @Excel(name = "是否入驻")
    @ApiModelProperty("是否入驻")
    private String settledType;


    @Excel(name = "介绍图")
    @ApiModelProperty("介绍图")
    private String introduceUrl;


}
