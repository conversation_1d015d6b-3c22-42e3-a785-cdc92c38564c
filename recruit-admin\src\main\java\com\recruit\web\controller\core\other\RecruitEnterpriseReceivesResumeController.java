package com.recruit.web.controller.core.other;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.recruit.common.annotation.Log;
import com.recruit.common.core.controller.BaseController;
import com.recruit.common.core.domain.AjaxResult;
import com.recruit.common.enums.BusinessType;
import com.recruit.core.domain.RecruitEnterpriseReceivesResume;
import com.recruit.core.service.IRecruitEnterpriseReceivesResumeService;
import com.recruit.common.utils.poi.ExcelUtil;
import com.recruit.common.core.page.TableDataInfo;

/**
 * 企业收到简历Controller
 *
 * <AUTHOR>
 * @date 2023-04-11
 */
@RestController
@RequestMapping("/core/receivesResume")
public class RecruitEnterpriseReceivesResumeController extends BaseController
{
    @Autowired
    private IRecruitEnterpriseReceivesResumeService recruitEnterpriseReceivesResumeService;

    /**
     * 查询企业收到简历列表
     */
    @PreAuthorize("@ss.hasPermi('core:receivesResume:list')")
    @GetMapping("/list")
    public TableDataInfo list(RecruitEnterpriseReceivesResume recruitEnterpriseReceivesResume)
    {
        startPage();
        List<RecruitEnterpriseReceivesResume> list = recruitEnterpriseReceivesResumeService.selectRecruitEnterpriseReceivesResumeList(recruitEnterpriseReceivesResume);
        return getDataTable(list);
    }

    /**
     * 导出企业收到简历列表
     */
    @PreAuthorize("@ss.hasPermi('core:receivesResume:export')")
    @Log(title = "企业收到简历", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RecruitEnterpriseReceivesResume recruitEnterpriseReceivesResume)
    {
        List<RecruitEnterpriseReceivesResume> list = recruitEnterpriseReceivesResumeService.selectRecruitEnterpriseReceivesResumeList(recruitEnterpriseReceivesResume);
        ExcelUtil<RecruitEnterpriseReceivesResume> util = new ExcelUtil<RecruitEnterpriseReceivesResume>(RecruitEnterpriseReceivesResume.class);
        util.exportExcel(response, list, "企业收到简历数据");
    }

    /**
     * 获取企业收到简历详细信息
     */
    @PreAuthorize("@ss.hasPermi('core:receivesResume:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(recruitEnterpriseReceivesResumeService.selectRecruitEnterpriseReceivesResumeById(id));
    }

    /**
     * 新增企业收到简历
     */
    @PreAuthorize("@ss.hasPermi('core:receivesResume:add')")
    @Log(title = "企业收到简历", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RecruitEnterpriseReceivesResume recruitEnterpriseReceivesResume)
    {
        return toAjax(recruitEnterpriseReceivesResumeService.insertRecruitEnterpriseReceivesResume(recruitEnterpriseReceivesResume));
    }

    /**
     * 修改企业收到简历
     */
    @PreAuthorize("@ss.hasPermi('core:receivesResume:edit')")
    @Log(title = "企业收到简历", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RecruitEnterpriseReceivesResume recruitEnterpriseReceivesResume)
    {
        return toAjax(recruitEnterpriseReceivesResumeService.updateRecruitEnterpriseReceivesResume(recruitEnterpriseReceivesResume));
    }

    /**
     * 删除企业收到简历
     */
    @PreAuthorize("@ss.hasPermi('core:receivesResume:remove')")
    @Log(title = "企业收到简历", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(recruitEnterpriseReceivesResumeService.deleteRecruitEnterpriseReceivesResumeByIds(ids));
    }
}
