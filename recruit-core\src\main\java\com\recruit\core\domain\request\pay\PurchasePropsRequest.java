package com.recruit.core.domain.request.pay;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Auther: <PERSON> kong
 * @Date: 2023/5/23 10:41
 * @Description:
 */
@Data
@ApiModel("购买道具请求实体")
public class PurchasePropsRequest {

    /*@ApiModelProperty("道具ID")
    private Long propId;*/

    @ApiModelProperty("价格表id")
    private Long sysPropPriceId;

    @ApiModelProperty("购买类型 1博汇币，2积分")
    private String purchaseType;

}
