package com.recruit.webApi;

import com.alibaba.fastjson2.JSON;
import com.google.gson.Gson;
import com.recruit.common.constant.HttpStatus;
import com.recruit.common.core.controller.BaseController;
import com.recruit.common.core.domain.AjaxResult;
import com.recruit.common.core.page.TableDataInfo;
import com.recruit.common.utils.*;
import com.recruit.common.utils.bean.BeanUtils;
import com.recruit.core.domain.*;
import com.recruit.core.domain.request.talent.FamilyRequest;
import com.recruit.core.domain.request.talent.LevelRegistrationRequest;
import com.recruit.core.domain.request.talent.QualificationBsRequest;
import com.recruit.core.domain.request.talent.VitaeRequest;
import com.recruit.core.domain.response.TalentIntroductionEnterprisesResponse;
import com.recruit.core.domain.response.TalentIntroductionResponse;
import com.recruit.core.mapper.RecruitTalentIntroductionEnrollFamilyMembersMapper;
import com.recruit.core.mapper.RecruitTalentIntroductionEnrollGraduationSchoolMapper;
import com.recruit.core.mapper.RecruitTalentIntroductionEnrollLearningAndWorkMapper;
import com.recruit.core.mapper.RecruitTalentIntroductionEnrollPhotosMapper;
import com.recruit.core.service.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;


/**
 * @Auther: Wu kong
 * @Date: 2023/5/11 0:03
 * @Description:
 */
@Api(tags= "(5-11)人才引进")
@Slf4j
@RestController
@RequestMapping("/web/api/talentIntroduction")
public class TalentIntroductionController extends BaseController {

    @Autowired
    private IRecruitUserInfoService recruitUserInfoService;

    @Autowired
    private IRecruitTalentIntroductionService recruitTalentIntroductionService;

    @Autowired
    private IRecruitTalentEducationCodeService recruitTalentEducationCodeService;

    @Autowired
    private IRecruitTalentIntroductionInfoService recruitTalentIntroductionInfoService;

    @Autowired
    private IRecruitTalentIntroductionEnrollService recruitTalentIntroductionEnrollService;


    //人才引进报名家庭成员
    @Autowired
    private RecruitTalentIntroductionEnrollFamilyMembersMapper talentIntroductionEnrollFamilyMembersMapper;

    //人才引进报名毕业学校
    @Autowired
    private RecruitTalentIntroductionEnrollGraduationSchoolMapper talentIntroductionEnrollGraduationSchoolMapper;

    //人才引进报名学习工作
    @Autowired
    private RecruitTalentIntroductionEnrollLearningAndWorkMapper talentIntroductionEnrollLearningAndWorkMapper;

    //人才引进照片
    @Autowired
    private RecruitTalentIntroductionEnrollPhotosMapper talentIntroductionEnrollPhotosMapper;



    @ApiOperation("招聘公告")
    @GetMapping("/getRecruitmentAnnouncement")
    public AjaxResult getRecruitmentAnnouncement(@NotNull(message = "公司动态id不能为空") @RequestParam Long id)
    {
        RecruitTalentIntroduction talentIntroduction = recruitTalentIntroductionService.selectRecruitTalentIntroductionById(id);
        TalentIntroductionResponse response = new TalentIntroductionResponse();
        if(talentIntroduction != null) {
            response.setTitle(talentIntroduction.getNumberOfPeriods());
            response.setContents(talentIntroduction.getContents());
            response.setRecruitmentNotice(RichTextHandle.getContent(talentIntroduction.getRecruitmentNotice()));
            response.setConsultationInfo(talentIntroduction.getConsultationInfo());
            response.setAppPicTwoUrl(talentIntroduction.getAppPicTwoUrl());
            response.setPcPicTwoUrl(talentIntroduction.getPcPicTwoUrl());
            response.setRemark(talentIntroduction.getRemark());
        }
        return success(response);
    }

    @ApiOperation("查询人才引进单位")
    @GetMapping("/getTalentIntroductionEnterprises")
    public TableDataInfo getTalentIntroductionEnterprises(String type, Long id)
    {
        List<TalentIntroductionEnterprisesResponse> list2 = new ArrayList<>();

        //查询
        List<RecruitTalentIntroductionInfo> lists = recruitTalentIntroductionInfoService.queryPrimaryEnterprises(type, id);
        for(RecruitTalentIntroductionInfo e : lists){
            TalentIntroductionEnterprisesResponse response = new TalentIntroductionEnterprisesResponse();
            response.setNameOfEmployer(e.getNameOfEmployer());
            response.setTalentIntroductionId(e.getTalentIntroductionId());
            list2.add(response);
        }
        list2 = list2.stream().filter(o -> o.getNameOfEmployer() != null).collect(
                Collectors.collectingAndThen(Collectors.toCollection(
                        () -> new TreeSet<>(Comparator.comparing(o -> o.getNameOfEmployer()))), ArrayList<TalentIntroductionEnterprisesResponse>::new));
        return getDataTable(list2);
    }

    @ApiOperation("查询人才引进岗位")
    @GetMapping("/getTalentIntroductionPositions")
    public TableDataInfo getTalentIntroductionPositions(@NotBlank(message = "id不能为空") Long talentIntroductionId, String searchValue, String openId)
    {
        RecruitTalentIntroductionInfo talentIntroductionInfo = new RecruitTalentIntroductionInfo();
        talentIntroductionInfo.setTalentIntroductionId(talentIntroductionId);
        talentIntroductionInfo.setSearchValue(searchValue);
        talentIntroductionInfo.setState("1");
        talentIntroductionInfo.setStateTwo("1");
        List<RecruitTalentIntroductionInfo> lists = recruitTalentIntroductionInfoService.selectRecruitTalentIntroductionInfoListTwo(talentIntroductionInfo);
        if(lists.isEmpty()) {
            throw new RuntimeException("未上架岗位信息！");
        }

        //查询用户报名状态
        RecruitTalentIntroductionEnroll talentIntroductionEnrolls = new RecruitTalentIntroductionEnroll();
        talentIntroductionEnrolls.setTalentIntroductionId(talentIntroductionId);
        talentIntroductionEnrolls.setOpenId(openId);
        //校验是否重复报名
        String enrollStatus = recruitTalentIntroductionEnrollService.getCheckRegistrationStatus(talentIntroductionEnrolls);

        //查询校验报名是否到期
        RecruitTalentIntroduction talentIntroduction = recruitTalentIntroductionService.selectRecruitTalentIntroductionById(talentIntroductionId);
        // 获取当前时间
        LocalDateTime currentTime = LocalDateTime.now();
        // 将字符串转换为LocalDateTime
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime specifiedTime = LocalDateTime.parse(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, talentIntroduction.getEndTime()), formatter);
        // 比较时间
        if (currentTime.isAfter(specifiedTime)) {
            enrollStatus = "1";
        }
        String finalEnrollStatus = enrollStatus;
        lists.forEach(e->{
            e.setEnrollStatus(finalEnrollStatus);
        });
        return getDataTable(lists);
    }


    @ApiOperation("报名")
    @PostMapping("/levelRegistration")
    public AjaxResult levelRegistration(@RequestBody @Validated LevelRegistrationRequest request) {
        Long userId = 0L;
        try {
            userId = getUserId();
        }catch (Exception e){
            log.error("用户未登录报名");
            RecruitUserInfo userInfo = recruitUserInfoService.getPhoneUserInfo(request.getPhone());
            if(userInfo != null){
                userId = userInfo.getId();
            }
        }
        log.info("打印报名请求参数：{}", JSON.toJSONString(request));
        //校验身份证号
        String identityCard = IdCardUtil.IdentityCardVerification(request.getIdNum());
        if(!StringUtils.equals(identityCard, "correct")){
            return AjaxResult.error(HttpStatus.UNSUPPORTED_TYPE, identityCard);
        }
        request.setUserId(userId);
        return toAjax(recruitTalentIntroductionEnrollService.insertIntroductionEnroll(request));
    }

    @ApiOperation("查询报名列表 0报名失败，1审核中，2审核通过")
    @GetMapping(value = "/getRegistrationList", produces = {"application/json"})
    @ApiResponses({
            @ApiResponse(code = 200, message = "返回实体", response = RecruitTalentIntroductionEnroll.class)
    })
    public AjaxResult getRegistrationList(String openId)
    {
        RecruitTalentIntroductionEnroll talentIntroductionEnrolls = new RecruitTalentIntroductionEnroll();
        Long userId = 0L;
        try {
            userId = getUserId();
        }catch (Exception e){
            throw new RuntimeException("用户未登录查询！");
        }
        RecruitUserInfo userInfo = recruitUserInfoService.selectRecruitUserInfoById(userId);
        if(userInfo != null){
            talentIntroductionEnrolls.setPhone(userInfo.getPhone());
        }
        talentIntroductionEnrolls.setOpenId(openId);
        List<RecruitTalentIntroductionEnroll> listsss = recruitTalentIntroductionEnrollService.getRegistrationList(talentIntroductionEnrolls);
        if(listsss.size() > 0){
            return success(listsss.get(0));
        }else {
            return success(new RecruitTalentIntroductionEnroll());
        }
    }

    @ApiOperation("查询报名列表 0报名失败，1审核中，2审核通过")
    @GetMapping(value = "/getMyRegistrationInfo", produces = {"application/json"})
    @ApiResponses({
            @ApiResponse(code = 200, message = "返回实体", response = RecruitTalentIntroductionEnroll.class)
    })
    public AjaxResult getMyRegistrationInfo(Long talentIntroductionId, String openId)
    {
        RecruitTalentIntroductionEnroll talentIntroductionEnrolls = new RecruitTalentIntroductionEnroll();
        Long userId = 0L;
        try {
            userId = getUserId();
        }catch (Exception e){
            throw new RuntimeException("用户未登录查询！");
        }
        RecruitUserInfo userInfo = recruitUserInfoService.selectRecruitUserInfoById(userId);
        if(userInfo != null){
            talentIntroductionEnrolls.setPhone(userInfo.getPhone());
        }
        talentIntroductionEnrolls.setTalentIntroductionId(talentIntroductionId);
        talentIntroductionEnrolls.setOpenId(openId);
        RecruitTalentIntroductionEnroll recruitTalentIntroductionEnroll = recruitTalentIntroductionEnrollService.getMyRegistrationInfo(talentIntroductionEnrolls);
        if(!ObjectUtils.isEmpty(recruitTalentIntroductionEnroll)){
            return success(recruitTalentIntroductionEnroll);
        }else {
            throw new RuntimeException("未找到报名信息");
        }
    }

    @ApiOperation("查询报名信息")
    @GetMapping(value = "/getRegistrationInfo", produces = {"application/json"})
    @ApiResponses({
            @ApiResponse(code = 200, message = "返回实体", response = RecruitTalentIntroductionEnroll.class)
    })
    public AjaxResult getRegistrationInfo(Long talentIntroductionId, String openId)
    {
        RecruitTalentIntroductionEnroll talentIntroductionEnrolls = new RecruitTalentIntroductionEnroll();
        Long userId = 0L;
        try {
            userId = getUserId();
        }catch (Exception e){
            throw new RuntimeException("用户未登录查询！");
        }
        RecruitUserInfo userInfo = recruitUserInfoService.selectRecruitUserInfoById(userId);
        if(userInfo != null){
            talentIntroductionEnrolls.setPhone(userInfo.getPhone());
        }
        talentIntroductionEnrolls.setTalentIntroductionId(talentIntroductionId);
        talentIntroductionEnrolls.setOpenId(openId);
        RecruitTalentIntroductionEnroll introductionEnroll = recruitTalentIntroductionEnrollService.getRegistrationInfo(talentIntroductionEnrolls);
        if(introductionEnroll != null) {
            LevelRegistrationRequest request = new LevelRegistrationRequest();
            //姓名
            request.setName(introductionEnroll.getUserName());
            //性别
            request.setSex(introductionEnroll.getSex());
            //民族
            request.setNation(introductionEnroll.getNation());
            //出生年月
            if (introductionEnroll.getDateOfBirth() != null) {
                request.setBirthday(DateUtils.dateTime(introductionEnroll.getDateOfBirth()));
            }
            //籍贯
            request.setNative_place(introductionEnroll.getHometown());
            //生源地
            request.setCollege_place(introductionEnroll.getBirthplace());
            //政治面貌
            request.setPolitice_status(introductionEnroll.getPoliticalLandscape());
            //爱好特长
            request.setHobby(introductionEnroll.getHobbiesAndSpecialties());
            //身份证号
            request.setIdNum(introductionEnroll.getIdCard());
            //学历
            request.setEducational_level(introductionEnroll.getEducation());
            //联系地址省市区
            request.setRegion(introductionEnroll.getRegion());
            //联系地址
            request.setAddress(introductionEnroll.getContactAddress());
            //移动电话
            request.setPhone(introductionEnroll.getPhone());
            //自我评价
            request.setSelf_evaluation(introductionEnroll.getSelfEvaluation());
            //论文、课题、社会实践经历
            request.setThesis_report(introductionEnroll.getEnrollOverview());

            //查询照片列表
            List<RecruitTalentIntroductionEnrollPhotos> introductionEnrollPhotosList = talentIntroductionEnrollPhotosMapper.selectRecruitTalentIntroductionEnrollPhotosList(new RecruitTalentIntroductionEnrollPhotos() {{
                setTalentIntroductionEnrollId(introductionEnroll.getId());
            }});
            //获奖证书照片
            List<String> certificate_img_list = new ArrayList<>();
            //其他材料照片
            List<String> other_img_list = new ArrayList<>();
            for (RecruitTalentIntroductionEnrollPhotos s : introductionEnrollPhotosList) {
                switch (s.getPicType()) {
                    case "4"://身份证正面
                        request.setID_img_front(s.getPicUrl());
                        break;
                    case "5"://身份证反面
                        request.setID_img_back(s.getPicUrl());
                        break;
                    case "6"://一寸照片
                        request.setNear_photo(s.getPicUrl());
                        break;
                    case "1"://获奖证书照片
                        certificate_img_list.add(s.getPicUrl());
                        break;
                    case "7"://其他材料照片
                        other_img_list.add(s.getPicUrl());
                        break;
                }
            }
            //获奖证书照片
            request.setCertificate_img_list(certificate_img_list);
            //其他材料照片
            request.setOther_img_list(other_img_list);

            //家庭成员
            List<RecruitTalentIntroductionEnrollFamilyMembers> familyMembersList = talentIntroductionEnrollFamilyMembersMapper.selectRecruitTalentIntroductionEnrollFamilyMembersList(new RecruitTalentIntroductionEnrollFamilyMembers() {{
                setTalentIntroductionEnrollId(introductionEnroll.getId());
            }});
            List<FamilyRequest> family_list = new ArrayList<>();
            for (RecruitTalentIntroductionEnrollFamilyMembers s : familyMembersList) {
                FamilyRequest r = new FamilyRequest();
                //称谓
                r.setAppellation(s.getAppellation());
                //姓名
                r.setName(s.getMoniker());
                //出生年月
                if (s.getDateOfBirth() != null) {
                    r.setBirthday(DateUtils.dateTime(s.getDateOfBirth()));
                }
                //工作单位及学校
                r.setWork_place(s.getOccupation());
                //职务
                r.setJob_name(s.getDuties());
                family_list.add(r);
            }
            //家庭成员
            request.setFamily_list(family_list);

            ////学习、工作经历
            List<RecruitTalentIntroductionEnrollLearningAndWork> learningAndWorkList = talentIntroductionEnrollLearningAndWorkMapper.selectRecruitTalentIntroductionEnrollLearningAndWorkList(new RecruitTalentIntroductionEnrollLearningAndWork() {{
                setTalentIntroductionEnrollId(introductionEnroll.getId());
            }});
            List<VitaeRequest> vitae_list = new ArrayList<>();
            for (RecruitTalentIntroductionEnrollLearningAndWork s : learningAndWorkList) {
                VitaeRequest r = new VitaeRequest();
                //开始时间
                if (s.getStartDate() != null) {
                    r.setStart_date(DateUtils.dateTime(s.getStartDate()));
                }
                //结束时间
                if (s.getEndDate() != null) {
                    r.setEnd_date(DateUtils.dateTime(s.getEndDate()));
                }
                //职务
                r.setJob_name(s.getDuties());
                //学校或工作单位
                r.setOrganization_name(s.getSchoolOrWorkplace());
                vitae_list.add(r);
            }
            request.setVitae_list(vitae_list);

            //学历信息
            List<RecruitTalentIntroductionEnrollGraduationSchool> qualificationBsList = talentIntroductionEnrollGraduationSchoolMapper.selectRecruitTalentIntroductionEnrollGraduationSchoolList(new RecruitTalentIntroductionEnrollGraduationSchool() {{
                setTalentIntroductionEnrollId(introductionEnroll.getId());
            }});
            Map<String, List<RecruitTalentIntroductionEnrollGraduationSchool>> groupedByName = qualificationBsList.stream()
                    .collect(Collectors.groupingBy(RecruitTalentIntroductionEnrollGraduationSchool::getType));

            //本科
            List<QualificationBsRequest> qualification_bs_list = new ArrayList<>();
            if (groupedByName.get("1") != null) {
                for (RecruitTalentIntroductionEnrollGraduationSchool s : groupedByName.get("1")) {
                    QualificationBsRequest r = new QualificationBsRequest();
                    //毕业学校
                    r.setSchool_name(s.getGraduationSchool());
                    //专业
                    r.setSpecialty(s.getMajor());
                    //学位时间
                    if (s.getDegreeTime() != null) {
                        r.setGraduation_time(DateUtils.dateTime(s.getDegreeTime()));
                    }
                    //主修课程
                    r.setMain_course(s.getMainCourse());
                    //毕业证照片
                    r.setGraduation_img(s.getGraduationImg());
                    //学位证照片
                    r.setDegree_img(s.getDegreeImg());
                    //学信网照片
                    r.setChsi_img(s.getChsiImg());
                    qualification_bs_list.add(r);
                }
            }
            request.setQualification_bs_list(qualification_bs_list);
            //研究生
            List<QualificationBsRequest> qualification_grad_list = new ArrayList<>();
            if (groupedByName.get("2") != null) {
                for (RecruitTalentIntroductionEnrollGraduationSchool s : groupedByName.get("2")) {
                    QualificationBsRequest rr = new QualificationBsRequest();
                    //毕业学校
                    rr.setSchool_name(s.getGraduationSchool());
                    //专业
                    rr.setSpecialty(s.getMajor());
                    //学位时间
                    if (s.getDegreeTime() != null) {
                        rr.setGraduation_time(DateUtils.dateTime(s.getDegreeTime()));
                    }
                    //主修课程
                    rr.setMain_course(s.getMainCourse());
                    //毕业证照片
                    rr.setGraduation_img(s.getGraduationImg());
                    //学位证照片
                    rr.setDegree_img(s.getDegreeImg());
                    //学信网照片
                    rr.setChsi_img(s.getChsiImg());
                    qualification_grad_list.add(rr);
                }
            }
            request.setQualification_grad_list(qualification_grad_list);
            //博士生
            List<QualificationBsRequest> qualification_scholar_list = new ArrayList<>();
            if (groupedByName.get("3") != null) {
                for (RecruitTalentIntroductionEnrollGraduationSchool s : groupedByName.get("3")) {
                    QualificationBsRequest rr = new QualificationBsRequest();
                    //毕业学校
                    rr.setSchool_name(s.getGraduationSchool());
                    //专业
                    rr.setSpecialty(s.getMajor());
                    //学位时间
                    if (s.getDegreeTime() != null) {
                        rr.setGraduation_time(DateUtils.dateTime(s.getDegreeTime()));
                    }
                    //主修课程
                    rr.setMain_course(s.getMainCourse());
                    //毕业证照片
                    rr.setGraduation_img(s.getGraduationImg());
                    //学位证照片
                    rr.setDegree_img(s.getDegreeImg());
                    //学信网照片
                    rr.setChsi_img(s.getChsiImg());
                    qualification_scholar_list.add(rr);
                }
            }
            request.setQualification_scholar_list(qualification_scholar_list);

            //高中
            List<QualificationBsRequest> qualification_high_list = new ArrayList<>();
            if (groupedByName.get("4") != null) {
                for (RecruitTalentIntroductionEnrollGraduationSchool s : groupedByName.get("4")) {
                    QualificationBsRequest rr = new QualificationBsRequest();
                    //毕业学校
                    rr.setSchool_name(s.getGraduationSchool());
                    //专业
                    rr.setSpecialty(s.getMajor());
                    //学位时间
                    if (s.getDegreeTime() != null) {
                        rr.setGraduation_time(DateUtils.dateTime(s.getDegreeTime()));
                    }
                    //主修课程
                    rr.setMain_course(s.getMainCourse());
                    //毕业证照片
                    rr.setGraduation_img(s.getGraduationImg());
                    //学位证照片
                    rr.setDegree_img(s.getDegreeImg());
                    qualification_high_list.add(rr);
                }
            }
            request.setQualification_high_list(qualification_high_list);

            //大专
            List<QualificationBsRequest> qualification_college_list = new ArrayList<>();
            if (groupedByName.get("5") != null) {
                for (RecruitTalentIntroductionEnrollGraduationSchool s : groupedByName.get("5")) {
                    QualificationBsRequest rr = new QualificationBsRequest();
                    //毕业学校
                    rr.setSchool_name(s.getGraduationSchool());
                    //专业
                    rr.setSpecialty(s.getMajor());
                    //学位时间
                    if (s.getDegreeTime() != null) {
                        rr.setGraduation_time(DateUtils.dateTime(s.getDegreeTime()));
                    }
                    //主修课程
                    rr.setMain_course(s.getMainCourse());
                    //毕业证照片
                    rr.setGraduation_img(s.getGraduationImg());
                    //学位证照片
                    rr.setDegree_img(s.getDegreeImg());
                    //学信网照片
                    rr.setChsi_img(s.getChsiImg());
                    qualification_college_list.add(rr);
                }
            }
            request.setQualification_college_list(qualification_college_list);
            return success(request);
        }else {
            LevelRegistrationRequest request = new LevelRegistrationRequest();
            request.setCertificate_img_list(new ArrayList<>());
            request.setOther_img_list(new ArrayList<>());
            //家庭成员
            List<FamilyRequest> family_list = new ArrayList<>();
            family_list.add(new FamilyRequest());
            request.setFamily_list(family_list);
            //学习、工作经历
            List<VitaeRequest> vitae_list = new ArrayList<>();
            vitae_list.add(new VitaeRequest());
            request.setVitae_list(vitae_list);
            //本科学历
            List<QualificationBsRequest> qualificationList = new ArrayList<>();
            request.setQualification_bs_list(qualificationList);
            //研究生学历
            request.setQualification_grad_list(qualificationList);
            //博士生学历
            request.setQualification_scholar_list(qualificationList);
            //高中
            request.setQualification_high_list(qualificationList);
            //大专
            request.setQualification_college_list(qualificationList);
            return success(request);
        }
    }


    @ApiOperation("查询专业代码")
    @GetMapping("/educationCodelistAll")
    public AjaxResult educationCodelistAll(RecruitTalentEducationCode recruitTalentEducationCode)
    {
        List<RecruitTalentEducationCode> list = recruitTalentEducationCodeService.selectRecruitTalentEducationCodeList(recruitTalentEducationCode);
        return success(list);
    }


    /**
     * 获取民族列表
     * @return
     */
    @GetMapping(value = "/getEthnicList")
    public AjaxResult getEthnicList()
    {
        List<NationDomain> lists = new ArrayList<>();
        Map<Integer, String> maps = ChineseNationalityCodeUtil.getNationList();
        for (Map.Entry<Integer, String> entry : maps.entrySet()) {
            NationDomain nationDomain = new NationDomain();
            nationDomain.setNationId(String.valueOf(entry.getKey()));
            nationDomain.setNationName(entry.getValue());
            lists.add(nationDomain);
        }
        return success(lists);
    }


}
