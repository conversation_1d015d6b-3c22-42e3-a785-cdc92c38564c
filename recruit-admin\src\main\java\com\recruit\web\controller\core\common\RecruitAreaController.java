package com.recruit.web.controller.core.common;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.recruit.common.annotation.Log;
import com.recruit.common.core.controller.BaseController;
import com.recruit.common.core.domain.AjaxResult;
import com.recruit.common.enums.BusinessType;
import com.recruit.core.domain.RecruitArea;
import com.recruit.core.service.IRecruitAreaService;
import com.recruit.common.utils.poi.ExcelUtil;
import com.recruit.common.core.page.TableDataInfo;

/**
 * 工作区域Controller
 *
 * <AUTHOR>
 * @date 2023-03-19
 */
@RestController
@RequestMapping("/core/area")
public class RecruitAreaController extends BaseController
{
    @Autowired
    private IRecruitAreaService recruitAreaService;

    /**
     * 查询工作区域列表
     */
    @PreAuthorize("@ss.hasPermi('core:area:list')")
    @GetMapping("/list")
    public TableDataInfo list(RecruitArea recruitArea)
    {
        startPage();
        List<RecruitArea> list = recruitAreaService.selectRecruitAreaList(recruitArea);
        return getDataTable(list);
    }

    /**
     * 导出工作区域列表
     */
    @PreAuthorize("@ss.hasPermi('core:area:export')")
    @Log(title = "工作区域", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RecruitArea recruitArea)
    {
        List<RecruitArea> list = recruitAreaService.selectRecruitAreaList(recruitArea);
        ExcelUtil<RecruitArea> util = new ExcelUtil<RecruitArea>(RecruitArea.class);
        util.exportExcel(response, list, "工作区域数据");
    }

    /**
     * 获取工作区域详细信息
     */
    @PreAuthorize("@ss.hasPermi('core:area:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(recruitAreaService.selectRecruitAreaById(id));
    }

    /**
     * 新增工作区域
     */
    @PreAuthorize("@ss.hasPermi('core:area:add')")
    @Log(title = "工作区域", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RecruitArea recruitArea)
    {
        return toAjax(recruitAreaService.insertRecruitArea(recruitArea));
    }

    /**
     * 修改工作区域
     */
    @PreAuthorize("@ss.hasPermi('core:area:edit')")
    @Log(title = "工作区域", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RecruitArea recruitArea)
    {
        return toAjax(recruitAreaService.updateRecruitArea(recruitArea));
    }

    /**
     * 删除工作区域
     */
    @PreAuthorize("@ss.hasPermi('core:area:remove')")
    @Log(title = "工作区域", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(recruitAreaService.deleteRecruitAreaByIds(ids));
    }

    @GetMapping("/getAreaList")
    public TableDataInfo getAreaList()
    {
        List<RecruitArea> list = recruitAreaService.selectRecruitAreaList(new RecruitArea());
        return getDataTable(list);
    }
}
