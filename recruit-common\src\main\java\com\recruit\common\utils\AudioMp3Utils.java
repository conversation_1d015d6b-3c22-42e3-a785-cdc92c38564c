package com.recruit.common.utils;

import javazoom.jl.decoder.BitstreamException;
import javazoom.jl.player.Player;
import lombok.extern.slf4j.Slf4j;
import java.io.BufferedInputStream;
import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLConnection;

import javazoom.jl.decoder.Bitstream;
import javazoom.jl.decoder.Header;

@Slf4j
public class AudioMp3Utils {

    public static int getObtainVoiceDuration(String mpUrl){
        int time = 0;
        try {
            URL urlfile = new URL(mpUrl);
            URLConnection con = null;
            try {
                con = urlfile.openConnection();
            } catch (IOException e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
            assert con != null;
            int b = con.getContentLength();//
            BufferedInputStream bis = new BufferedInputStream(con.getInputStream());
            Bitstream bt = new Bitstream(bis);
            Header h = bt.readFrame();
            if(h != null){
                time = (int) h.total_ms(b);
                time = (time / 1000);
            }
        }catch (Exception e){
            e.printStackTrace();
        }
        return time;
    }

}
