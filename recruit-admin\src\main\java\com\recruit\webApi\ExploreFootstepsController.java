package com.recruit.webApi;

import com.recruit.common.core.controller.BaseController;
import com.recruit.common.core.domain.AjaxResult;
import com.recruit.common.core.page.TableDataInfo;
import com.recruit.common.utils.DictUtils;
import com.recruit.common.utils.StringUtils;
import com.recruit.core.domain.RecruitEnterpriseUsersRel;
import com.recruit.core.domain.RecruitExploreFootsteps;
import com.recruit.core.domain.RecruitPositionInfo;
import com.recruit.core.domain.request.other.BrowsePositionRequest;
import com.recruit.core.domain.request.other.EnterpriseBrowsingUsersRequest;
import com.recruit.core.domain.response.PositionBrowsingInfoResponse;
import com.recruit.core.domain.response.UserBrowsingInfoResponse;
import com.recruit.core.service.IRecruitEnterpriseUsersRelService;
import com.recruit.core.service.IRecruitExploreFootstepsService;
import com.recruit.core.service.IRecruitPositionInfoService;
import com.recruit.core.service.IRecruitPositionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Auther: Wu kong
 * @Date: 2023/4/12 9:23
 * @Description:
 */
@Api(tags= "(04-12)浏览足迹接口")
@RestController
@RequestMapping("/web/api/footsteps")
public class ExploreFootstepsController  extends BaseController {

    @Autowired
    private IRecruitPositionService recruitPositionService;

    @Autowired
    private IRecruitPositionInfoService recruitPositionInfoService;

    @Autowired
    private IRecruitExploreFootstepsService recruitExploreFootstepsService;

    @Autowired
    private IRecruitEnterpriseUsersRelService recruitEnterpriseUsersRelService;


    @ApiOperation(value = "求职者，添加浏览职位",notes="求职者浏览职位")
    @PostMapping("/addBrowsePosition")
    public AjaxResult addBrowsePosition(@RequestBody BrowsePositionRequest request)
    {
        RecruitExploreFootsteps footsteps = new RecruitExploreFootsteps();
        footsteps.setEnterpriseId(request.getEnterpriseId());
        footsteps.setUserId(getUserId());
        footsteps.setPositionInfoId(request.getPositionInfoId());
        footsteps.setPublisherId(request.getPublisherId());
        footsteps.setType("1");

        List<RecruitExploreFootsteps> list = recruitExploreFootstepsService.selectRecruitExploreFootstepsList(footsteps);
        if(list.size() > 0){
            return toAjax(1);
        }else {
            return toAjax(recruitExploreFootstepsService.insertRecruitExploreFootsteps(footsteps));
        }
    }


    @ApiOperation("求职者, 我看过的")
    @GetMapping("/getPositionBrowsingInfo")
    public TableDataInfo getPositionBrowsingInfo(@NotNull(message = "页码不能为空") @RequestParam Integer page,
                                            @NotNull(message = "size不能为空") @RequestParam Integer size)
    {
        startPageTwo(page, size);
        RecruitExploreFootsteps footsteps = new RecruitExploreFootsteps();
        footsteps.setUserId(getUserId());
        footsteps.setType("1");
        List<PositionBrowsingInfoResponse> list = recruitExploreFootstepsService.getPositionBrowsingInfo(footsteps);
        list.forEach(e->{
            if(e.getPositionCode() != null && !StringUtils.equals(e.getPositionCode(), "")) {
                e.setPositionName(recruitPositionService.getMap(e.getPositionCode()));
            }
            if(e.getWorkExperience() != null && !StringUtils.equals(e.getWorkExperience(), "")){
                e.setWorkExperienceName(DictUtils.getDictLabel("work_experience", e.getWorkExperience()));
                if(e.getWorkExperienceName().equals("不限")){
                    e.setWorkExperienceName("经验不限");
                }
            }else {
                e.setWorkExperienceName("经验不限");
            }
            if(e.getMinimumEducation() != null && !StringUtils.equals(e.getMinimumEducation(), "")) {
                e.setMinimumEducationName(DictUtils.getDictLabel("background_type", e.getMinimumEducation()));
                if (e.getMinimumEducationName().equals("不限")) {
                    e.setMinimumEducationName("学历不限");
                }
            }else {
                e.setMinimumEducationName("学历不限");
            }
        });
        return getDataTable(list);
    }


    @ApiOperation("求职者, 看过我的")
    @GetMapping("/getBrowsingWhoHasSeenMyData")
    public TableDataInfo getBrowsingWhoHasSeenMyData(@NotNull(message = "页码不能为空") @RequestParam Integer page,
                                            @NotNull(message = "size不能为空") @RequestParam Integer size)
    {
        startPageTwo(page, size);
        RecruitExploreFootsteps footsteps = new RecruitExploreFootsteps();
        footsteps.setUserId(getUserId());
        footsteps.setType("2");
        List<PositionBrowsingInfoResponse> list = recruitExploreFootstepsService.getPositionBrowsingInfo(footsteps);
        list.forEach(e->{
            if(e.getPositionCode() != null && !StringUtils.equals(e.getPositionCode(), "")) {
                e.setPositionName(recruitPositionService.getMap(e.getPositionCode()));
            }
            if(e.getWorkExperience() != null && !StringUtils.equals(e.getWorkExperience(), "")){
                e.setWorkExperienceName(DictUtils.getDictLabel("work_experience", e.getWorkExperience()));
                if(e.getWorkExperienceName().equals("不限")){
                    e.setWorkExperienceName("经验不限");
                }
            }else {
                e.setWorkExperienceName("经验不限");
            }
            if(e.getMinimumEducation() != null && !StringUtils.equals(e.getMinimumEducation(), "")) {
                e.setMinimumEducationName(DictUtils.getDictLabel("background_type", e.getMinimumEducation()));
                if (e.getMinimumEducationName().equals("不限")) {
                    e.setMinimumEducationName("学历不限");
                }
            }else {
                e.setMinimumEducationName("学历不限");
            }
        });
        return getDataTable(list);
    }


    @ApiOperation("求职者（查看职位浏览信息数）, 查看求职者看过的职位记录数")
    @GetMapping("/getPositionBrowsingInfoNum")
    public AjaxResult getPositionBrowsingInfoNum()
    {
        RecruitExploreFootsteps footsteps = new RecruitExploreFootsteps();
        footsteps.setUserId(getUserId());
        footsteps.setType("1");
        return success(recruitExploreFootstepsService.getPositionBrowsingInfoNum(footsteps));
    }








    @ApiOperation(value = "企业添加浏览用户",notes="企业浏览用户")
    @PostMapping("/addEnterpriseBrowsingUsers")
    public AjaxResult addEnterpriseBrowsingUsers(@RequestBody EnterpriseBrowsingUsersRequest request)
    {
        RecruitExploreFootsteps footsteps = new RecruitExploreFootsteps();
        RecruitEnterpriseUsersRel usersRel = recruitEnterpriseUsersRelService.selectEnterpriseUsersRelUserId(getUserId());
        if(usersRel == null){
            throw new RuntimeException("该企业用户未绑定企业！");
        }
        RecruitPositionInfo positionInfo = new RecruitPositionInfo();
        positionInfo.setUserId(getUserId());
        positionInfo.setEnterpriseId(usersRel.getEnterpriseId());
        List<RecruitPositionInfo> lists = recruitPositionInfoService.selectRecruitPositionInfoList(positionInfo);
        if(lists.size() > 0){
            footsteps.setPositionInfoId(lists.get(0).getId());
            footsteps.setUserId(request.getUserId());
            footsteps.setPublisherId(getUserId());
            footsteps.setEnterpriseId(usersRel.getEnterpriseId());
            footsteps.setType("2");
            List<RecruitExploreFootsteps> list = recruitExploreFootstepsService.selectRecruitExploreFootstepsList(footsteps);
            if(list.size() > 0){
                return toAjax(1);
            }else {
                return toAjax(recruitExploreFootstepsService.insertRecruitExploreFootsteps(footsteps));
            }
        }else {
            return toAjax(1);
        }

    }

    @ApiOperation("招聘者 我看过的")
    @GetMapping("/getUserBrowsingInfo")
    public TableDataInfo getUserBrowsingInfo(@NotNull(message = "页码不能为空") @RequestParam Integer page,
                                             @NotNull(message = "size不能为空") @RequestParam Integer size)
    {
        startPageTwo(page, size);
        RecruitExploreFootsteps footsteps = new RecruitExploreFootsteps();
        footsteps.setPublisherId(getUserId());
        footsteps.setType("2");
        List<UserBrowsingInfoResponse> list = recruitExploreFootstepsService.getUserBrowsingInfo(footsteps);
        list.forEach(e->{
            //工作经验
            if(e.getWorkExperience() != null && !StringUtils.equals(e.getWorkExperience(), "")) {
                e.setWorkExperienceName(DictUtils.getDictLabel("work_experience_two", e.getWorkExperience()));
                if(e.getWorkExperienceName().equals("不限")){
                    e.setWorkExperienceName("经验不限");
                }
            }else {
                e.setWorkExperienceName("经验不限");
            }
            //最高学历
            if(e.getEducation() != null && !StringUtils.equals(e.getEducation(), "")) {
                e.setEducationName(DictUtils.getDictLabel("background_type", e.getEducation()));
                if (e.getEducationName().equals("不限")) {
                    e.setEducationName("学历不限");
                }
            }else {
                e.setEducationName("学历不限");
            }
        });
        return getDataTable(list);
    }


    @ApiOperation("招聘者 谁看过我的数据")
    @GetMapping("/getWhoHasSeenMyData")
    public TableDataInfo getWhoHasSeenMyData(@NotNull(message = "页码不能为空") @RequestParam Integer page,
                                             @NotNull(message = "size不能为空") @RequestParam Integer size)
    {
        startPageTwo(page, size);
        RecruitExploreFootsteps footsteps = new RecruitExploreFootsteps();
        footsteps.setPublisherId(getUserId());
        footsteps.setType("1");
        List<UserBrowsingInfoResponse> list = recruitExploreFootstepsService.getUserBrowsingInfo(footsteps);
        list.forEach(e->{
            //工作经验
            if(e.getWorkExperience() != null && !StringUtils.equals(e.getWorkExperience(), "")) {
                e.setWorkExperienceName(DictUtils.getDictLabel("work_experience_two", e.getWorkExperience()));
                if(e.getWorkExperienceName().equals("不限")){
                    e.setWorkExperienceName("经验不限");
                }
            }else {
                e.setWorkExperienceName("经验不限");
            }
            //最高学历
            if(e.getEducation() != null && !StringUtils.equals(e.getEducation(), "")) {
                e.setEducationName(DictUtils.getDictLabel("background_type", e.getEducation()));
                if (e.getEducationName().equals("不限")) {
                    e.setEducationName("学历不限");
                }
            }else {
                e.setEducationName("学历不限");
            }
        });
        return getDataTable(list);
    }

    @ApiOperation("招聘者（查看用户浏览信息数）, 企业看过的个人信息记录数")
    @GetMapping("/getUserBrowsingInfoNum")
    public AjaxResult getUserBrowsingInfoNum()
    {
        RecruitExploreFootsteps footsteps = new RecruitExploreFootsteps();
        footsteps.setPublisherId(getUserId());
        footsteps.setType("2");
        return success(recruitExploreFootstepsService.getUserBrowsingInfoNum(footsteps));
    }


}
