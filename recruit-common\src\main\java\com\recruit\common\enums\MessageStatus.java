package com.recruit.common.enums;


public enum MessageStatus {

    UNREAD(0,"未读"),
    ALREADY_READ(1,"已读"),
    RECALL(2,"已撤回");

    private Integer code;

    private String desc;

    MessageStatus(Integer index, String desc) {
        this.code =index;
        this.desc=desc;
    }

    public static MessageStatus fromCode(Integer code){
        for (MessageStatus typeEnum:values()) {
            if (typeEnum.code.equals(code)) {
                return typeEnum;
            }
        }
        return null;
    }


    public String description() {
        return desc;
    }

    public Integer code(){
        return this.code;
    }
}
