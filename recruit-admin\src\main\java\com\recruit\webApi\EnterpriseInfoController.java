package com.recruit.webApi;

import com.github.pagehelper.PageInfo;
import com.recruit.common.core.controller.BaseController;
import com.recruit.common.core.domain.AjaxResult;
import com.recruit.common.core.page.TableDataInfo;
import com.recruit.common.utils.DateUtils;
import com.recruit.common.utils.DictUtils;
import com.recruit.common.utils.StringUtils;
import com.recruit.common.utils.bean.BeanUtils;
import com.recruit.core.domain.RecruitOnlineJobFairs;
import com.recruit.core.domain.RecruitPopularEnterprises;
import com.recruit.core.domain.RecruitPositionInfo;
import com.recruit.core.domain.request.other.JobFairsRequest;
import com.recruit.core.domain.response.OnlineJobFairsResponse;
import com.recruit.core.domain.response.PositionInfoResponse;
import com.recruit.core.service.IRecruitOnlineJobFairsService;
import com.recruit.core.service.IRecruitPopularEnterprisesService;
import com.recruit.core.service.IRecruitPositionInfoService;
import com.recruit.core.service.IRecruitPositionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * @Auther: Wu kong
 * @Date: 2023/4/21 20:06
 * @Description:
 */
@Api(tags= "(04-21)企业信息")
@Slf4j
@RestController
@RequestMapping("/web/api/enterpriseInfo")
public class EnterpriseInfoController extends BaseController {

    @Autowired
    private IRecruitPositionInfoService recruitPositionInfoService;

    @Autowired
    private IRecruitPopularEnterprisesService recruitOnlineJobFairsService;

    @Autowired
    private IRecruitOnlineJobFairsService onlineJobFairsService;

    @Autowired
    private IRecruitPositionService recruitPositionService;


    @ApiOperation("查询热门企业")
    @GetMapping(value = "/getPopularEnterprises", produces = {"application/json"})
    @ApiResponses({
            @ApiResponse(code = 200, message = "返回实体", response = OnlineJobFairsResponse.class)
    })
    public TableDataInfo getPopularEnterprises(JobFairsRequest request)
    {
        startPage();
        RecruitPopularEnterprises onlineJobFairs = new RecruitPopularEnterprises();

        BeanUtils.copyBeanProp(onlineJobFairs, request);
        onlineJobFairs.setType("2");
        onlineJobFairs.setState("1");
        //判断职位等级
        if(request.getPosition() != null && !StringUtils.equals(request.getPosition(), "")) {
            Integer grade = recruitPositionService.getMap1(request.getPosition());
            if (grade != null) {
                onlineJobFairs.setGrade(grade);
            }
        }
        onlineJobFairs.setStartTime(DateUtils.dateTime(DateUtils.YYYY_MM_DD, DateUtils.getDate()));
        onlineJobFairs.setEndTime(DateUtils.dateTime(DateUtils.YYYY_MM_DD, DateUtils.getDate()));
        List<OnlineJobFairsResponse> list = recruitOnlineJobFairsService.getPopularEnterprises(onlineJobFairs);
        list.forEach(e->{
            if(e.getScale() != null && !StringUtils.equals(e.getScale(), "")) {
                e.setScaleName(DictUtils.getDictLabel("scale_enterprises", e.getScale()));
                if(e.getScaleName().equals("不限")){
                    e.setScaleName("规模不限");
                }
            }else {
                e.setScaleName("规模不限");
            }
            if(e.getEnterpriseNature() != null && !StringUtils.equals(e.getEnterpriseNature(), "")) {
                e.setEnterpriseNatureName(DictUtils.getDictLabel("enterprise_nature", e.getEnterpriseNature()));
            }

            RecruitPositionInfo positionInfo = new RecruitPositionInfo();
            positionInfo.setPositionStatus("1");
            positionInfo.setEnterpriseId(e.getEnterpriseId());
            startPageTwo(1, 3);
            List<RecruitPositionInfo> lists = recruitPositionInfoService.getEnterprisePositionList(positionInfo);
            List<PositionInfoResponse> positionInfoList = new ArrayList<>();
            lists.forEach(s->{
                PositionInfoResponse response = new PositionInfoResponse();
                BeanUtils.copyBeanProp(response, s);
                if(response.getMinimumEducation() != null && !StringUtils.equals(response.getMinimumEducation(), "")) {
                    response.setMinimumEducationName(DictUtils.getDictLabel("background_type", response.getMinimumEducation()));
                    if (response.getMinimumEducationName().equals("不限")) {
                        response.setMinimumEducationName("学历不限");
                    }
                }else {
                    response.setMinimumEducationName("学历不限");
                }
                if(response.getWorkExperience() != null && !StringUtils.equals(response.getWorkExperience(), "")) {
                    response.setWorkExperienceName(DictUtils.getDictLabel("work_experience", response.getWorkExperience()));
                    if(response.getWorkExperienceName().equals("不限")){
                        response.setWorkExperienceName("经验不限");
                    }
                }else {
                    response.setWorkExperienceName("经验不限");
                }
                if(response.getPositionCode() != null && !StringUtils.equals(response.getPositionCode(), "")) {
                    response.setPositionName(recruitPositionService.getMap(response.getPositionCode()));
                }

                try {
                    //最高薪资
                    if(response.getMaximumSalary() != null && !StringUtils.equals(response.getMaximumSalary(), "")) {
                        BigDecimal maximumSalary = new BigDecimal(response.getMaximumSalary());
                        response.setMaximumSalary(maximumSalary.divide(new BigDecimal(1000))+"K");
                    }
                }catch (Exception ss){
                    log.error("最高薪资转换问题，无需关注");
                }
                try {
                    //最低薪资
                    if(response.getMinimumWage() != null && !StringUtils.equals(response.getMinimumWage(), "")) {
                        BigDecimal minimumWage = new BigDecimal(response.getMinimumWage());
                        response.setMinimumWage(minimumWage.divide(new BigDecimal(1000))+"K");
                    }
                }catch (Exception ss){
                    log.error("最低薪资转换问题，无需关注");
                }
                //如果最低最高金额相同，则最高金额赋值为空
                if(response.getMinimumWage() != null && response.getMaximumSalary() != null) {
                    if (response.getMinimumWage().equals(response.getMaximumSalary())) {
                        response.setMaximumSalary(null);
                    }
                }

                positionInfoList.add(response);
            });
            e.setPositionInfoList(positionInfoList);
        });
        return getDataTable(list);
    }

    @ApiOperation("查询网络招聘会")
    @GetMapping(value = "/getOnlineJobFairs", produces = {"application/json"})
    @ApiResponses({
            @ApiResponse(code = 200, message = "返回实体", response = OnlineJobFairsResponse.class)
    })
    public AjaxResult getOnlineJobFairs(JobFairsRequest request)
    {
        RecruitOnlineJobFairs onlineJobFairss = onlineJobFairsService.selectRecruitOnlineJobFairs();

        startPage();
        RecruitPopularEnterprises onlineJobFairs = new RecruitPopularEnterprises();
        BeanUtils.copyBeanProp(onlineJobFairs, request);
        onlineJobFairs.setOnlineJobFairsId(onlineJobFairss.getId());
        onlineJobFairs.setType("1");
        List<OnlineJobFairsResponse> list = recruitOnlineJobFairsService.getPopularEnterprises(onlineJobFairs);
        onlineJobFairss.setTotal(new PageInfo(list).getTotal());
        list.forEach(e->{
            if(e.getScale() != null && !StringUtils.equals(e.getScale(), "")) {
                e.setScaleName(DictUtils.getDictLabel("scale_enterprises", e.getScale()));
                if(e.getScaleName().equals("不限")){
                    e.setScaleName("规模不限");
                }
            }else {
                e.setScaleName("规模不限");
            }
            if(e.getEnterpriseNature() != null && !StringUtils.equals(e.getEnterpriseNature(), "")) {
                e.setEnterpriseNatureName(DictUtils.getDictLabel("enterprise_nature", e.getEnterpriseNature()));
            }

            RecruitPositionInfo positionInfo = new RecruitPositionInfo();
            positionInfo.setPositionStatus("1");
            positionInfo.setEnterpriseId(e.getEnterpriseId());
            startPageTwo(1, 3);
            List<RecruitPositionInfo> lists = recruitPositionInfoService.getEnterprisePositionList(positionInfo);
            List<PositionInfoResponse> positionInfoList = new ArrayList<>();
            lists.forEach(s->{
                PositionInfoResponse response = new PositionInfoResponse();
                BeanUtils.copyBeanProp(response, s);
                if(response.getMinimumEducation() != null && !StringUtils.equals(response.getMinimumEducation(), "")) {
                    response.setMinimumEducationName(DictUtils.getDictLabel("background_type", response.getMinimumEducation()));
                    if (response.getMinimumEducationName().equals("不限")) {
                        response.setMinimumEducationName("学历不限");
                    }
                }else {
                    response.setMinimumEducationName("学历不限");
                }
                if(response.getPositionCode() != null && !StringUtils.equals(response.getPositionCode(), "")) {
                    response.setPositionName(recruitPositionService.getMap(response.getPositionCode()));
                }
                if(response.getWorkExperience() != null && !StringUtils.equals(response.getWorkExperience(), "")) {
                    response.setWorkExperienceName(DictUtils.getDictLabel("work_experience", response.getWorkExperience()));
                    if(response.getWorkExperienceName().equals("不限")){
                        response.setWorkExperienceName("经验不限");
                    }
                }else {
                    response.setWorkExperienceName("经验不限");
                }

                try {
                    //最高薪资
                    if(response.getMaximumSalary() != null && !StringUtils.equals(response.getMaximumSalary(), "")) {
                        BigDecimal maximumSalary = new BigDecimal(response.getMaximumSalary());
                        response.setMaximumSalary(maximumSalary.divide(new BigDecimal(1000))+"K");
                    }
                }catch (Exception ss){
                    log.error("最高薪资转换问题，无需关注");
                }
                try {
                    //最低薪资
                    if(response.getMinimumWage() != null && !StringUtils.equals(response.getMinimumWage(), "")) {
                        BigDecimal minimumWage = new BigDecimal(response.getMinimumWage());
                        response.setMinimumWage(minimumWage.divide(new BigDecimal(1000))+"K");
                    }
                }catch (Exception ss){
                    log.error("最低薪资转换问题，无需关注");
                }
                //如果最低最高金额相同，则最高金额赋值为空
                if(response.getMinimumWage() != null && response.getMaximumSalary() != null) {
                    if (response.getMinimumWage().equals(response.getMaximumSalary())) {
                        response.setMaximumSalary(null);
                    }
                }

                positionInfoList.add(response);
            });
            e.setPositionInfoList(positionInfoList);
        });

        onlineJobFairss.setOnlineJobFairsList(list);
        return success(onlineJobFairss);
    }


}
