package com.recruit.core.domain;

import java.math.BigDecimal;
import lombok.Data;

/**
 * 服务商品价格对象 temporary_service_goods_price
 *
 * <AUTHOR>
 * @date 2023-06-01
 */
@Data
public class TemporaryServiceGoodsPrice
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 服务商品表id */
    private Long serviceGoodsId;

    /** 商品名称 */
    private String goodsName;

    /** 商品价格 */
    private BigDecimal goodsPrice;

    /** 商品描述 */
    private String goodsDescribe;

    /** 数量 */
    private String goodsNum;

    /** 实际价格 */
    private BigDecimal realityPrice;

    /** 单位类型 */
    private String unitType;


}
