package com.recruit.web.controller.core.user;

import com.recruit.common.annotation.Log;
import com.recruit.common.constant.HttpStatus;
import com.recruit.common.core.controller.BaseController;
import com.recruit.common.core.domain.AjaxResult;
import com.recruit.common.core.page.TableDataInfo;
import com.recruit.common.enums.BusinessType;
import com.recruit.common.utils.*;
import com.recruit.common.utils.poi.ExcelUtil;
import com.recruit.core.domain.*;
import com.recruit.core.domain.response.ResumeInfoResponse;
import com.recruit.core.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 用户信息Controller
 *
 * <AUTHOR>
 * @date 2023-03-15
 */
@RestController
@RequestMapping("/core/customerSentiment")
public class CustomerSentimentController extends BaseController
{
    @Autowired
    private IRecruitUserInfoService recruitUserInfoService;

    @Autowired
    private IRecruitPositionService recruitPositionService;

    @Autowired
    private IRecruitResumeEducationService recruitResumeEducationService;

    @Autowired
    private IRecruitResumeWorkHistoryService recruitResumeWorkHistoryService;

    @Autowired
    private IRecruitResumeProjectExperienceService recruitResumeProjectExperienceService;

    @Autowired
    private IRecruitResumeProfessionalSkillsService recruitResumeProfessionalSkillsService;

    /**
     * 查询用户信息列表
     */
    @PreAuthorize("@ss.hasPermi('core:customerSentiment:list')")
    @GetMapping("/list")
    public TableDataInfo list(RecruitUserInfo recruitUserInfo)
    {
        startPage();
        List<RecruitUserInfo> list = recruitUserInfoService.selectCustomerSentimentList(recruitUserInfo);
        return getDataTable(list);
    }

    /**
     * 获取用户信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('core:customerSentiment:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        ResumeInfoResponse recruitUserInfo = recruitUserInfoService.getResumeInfo(id);
        if(recruitUserInfo != null){
            recruitUserInfo.getRecruitUserInfo().setPassword("");
            if(recruitUserInfo.getResumeJobDesire() != null){
                if(recruitUserInfo.getResumeJobDesire().getEngagedInIndustry() != null) {
                    recruitUserInfo.getResumeJobDesire().setExpectedPositionName(recruitPositionService.getMap((recruitUserInfo.getResumeJobDesire().getExpectedPosition())));
                }
                if(recruitUserInfo.getResumeJobDesire().getWorkFunction() != null){
                    String[] ss = recruitUserInfo.getResumeJobDesire().getWorkFunction().split(",");
                    StringBuilder workFunctionName = new StringBuilder();
                    for(String s : ss){
                        if(StringUtils.equals(workFunctionName, "")){
                            workFunctionName.append(recruitPositionService.getMap(s));
                        }else {
                            workFunctionName.append(",").append(recruitPositionService.getMap(s));
                        }
                    }
                    recruitUserInfo.getResumeJobDesire().setWorkFunctionName(String.valueOf(workFunctionName));
                }
                //期望城市
                if(recruitUserInfo.getResumeJobDesire().getExpectedCity() != null && !StringUtils.equals(recruitUserInfo.getResumeJobDesire().getExpectedCity(), "")){
                    List<String> expectedCity = new ArrayList<>(Arrays.asList(recruitUserInfo.getResumeJobDesire().getExpectedCity().split(",")));
                    recruitUserInfo.getResumeJobDesire().setExpectedCitys(expectedCity);
                }
            }
            //基本资料
            if(recruitUserInfo.getResumeBasicInfo() != null){
                if(recruitUserInfo.getResumeBasicInfo().getCity() != null && !StringUtils.equals(recruitUserInfo.getResumeBasicInfo().getCity(), "")){
                    List<String> citys = new ArrayList<>(Arrays.asList(recruitUserInfo.getResumeBasicInfo().getCity().split(",")));
                    recruitUserInfo.getResumeBasicInfo().setCitys(citys);
                }
            }

            //职业技能
            RecruitResumeProfessionalSkills resumeProfessionalSkills = new RecruitResumeProfessionalSkills();
            resumeProfessionalSkills.setUserId(recruitUserInfo.getRecruitUserInfo().getId());
            List<RecruitResumeProfessionalSkills> list = recruitResumeProfessionalSkillsService.selectRecruitResumeProfessionalSkillsList(resumeProfessionalSkills);
            list.forEach(e->{
                e.setProficiencyName(DictUtils.getDictLabel("proficiency_type", e.getProficiency()));
            });
            recruitUserInfo.setResumeProfessionalSkillsList(list);
            //项目经历
            RecruitResumeProjectExperience resumeProjectExperience = new RecruitResumeProjectExperience();
            resumeProjectExperience.setUserId(recruitUserInfo.getRecruitUserInfo().getId());
            recruitUserInfo.setResumeProjectExperienceList(recruitResumeProjectExperienceService.selectRecruitResumeProjectExperienceList(resumeProjectExperience));
            //工作经历
            RecruitResumeWorkHistory resumeWorkHistory = new RecruitResumeWorkHistory();
            resumeWorkHistory.setUserId(recruitUserInfo.getRecruitUserInfo().getId());
            List<RecruitResumeWorkHistory> listss = recruitResumeWorkHistoryService.selectRecruitResumeWorkHistoryList(resumeWorkHistory);
            listss.forEach(e->{
                if(e.getPosition() != null && !StringUtils.equals(e.getPosition(), "")) {
                    e.setPositionName(recruitPositionService.getMap(e.getPosition()));
                }
                if(e.getUpToNow() != null && !StringUtils.equals(e.getUpToNow(), "")){
                    if(StringUtils.equals(e.getUpToNow(), "")){
                        e.setUpToNowName("是");
                    }else {
                        e.setUpToNowName("否");
                    }
                }
            });
            recruitUserInfo.setResumeWorkHistoryList(listss);
            //教育经历
            RecruitResumeEducation resumeEducation = new RecruitResumeEducation();
            resumeEducation.setUserId(recruitUserInfo.getRecruitUserInfo().getId());
            List<RecruitResumeEducation> lists = recruitResumeEducationService.selectRecruitResumeEducationList(resumeEducation);
            lists.forEach(s->{
                if(s.getHighestEducation() != null && !StringUtils.equals(s.getHighestEducation(), "")) {
                    s.setHighestEducationName(DictUtils.getDictLabel("background_type", s.getHighestEducation()));
                    if (s.getHighestEducationName().equals("不限")) {
                        s.setHighestEducationName("学历不限");
                    }
                }else {
                    s.setHighestEducationName("学历不限");
                }
            });
            recruitUserInfo.setResumeEducationList(lists);
        }
        return success(recruitUserInfo);
    }

    /**
     * 新增用户信息
     */
    @PreAuthorize("@ss.hasPermi('core:customerSentiment:add')")
    @Log(title = "用户信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RecruitUserInfo recruitUserInfo)
    {
        //校验身份证号
        if(recruitUserInfo.getIdCard() != null && !StringUtils.equals(recruitUserInfo.getIdCard(), "")) {
            String identityCard = IdCardUtil.IdentityCardVerification(recruitUserInfo.getIdCard());
            if (!StringUtils.equals(identityCard, "correct")) {
                return AjaxResult.error(HttpStatus.UNSUPPORTED_TYPE, identityCard);
            }
            //性别
            recruitUserInfo.setSex(IdCardUtil.getSex(recruitUserInfo.getIdCard()));
            //出生年月
            recruitUserInfo.setDateOfBirth(DateUtils.dateTime(DateUtils.YYYY_MM_DD, IdCardUtil.getBirthday(recruitUserInfo.getIdCard())));
        }
        //校验手机号
        String verifyPhone = PhoneUtil.verifyPhone(recruitUserInfo.getPhone());
        if(!StringUtils.equals(verifyPhone, "success")){
            return AjaxResult.error(HttpStatus.UNSUPPORTED_TYPE, verifyPhone);
        }
        //校验邮箱
        if(recruitUserInfo.getMailbox() != null && !StringUtils.equals(recruitUserInfo.getMailbox(), "")) {
            if (!EmailUtil.emailFormat(recruitUserInfo.getMailbox())) {
                return AjaxResult.error(HttpStatus.UNSUPPORTED_TYPE, "邮箱格式不正确");
            }
        }

        if (!recruitUserInfoService.checkIdCardUnique2(recruitUserInfo))
        {
            return AjaxResult.error("新增用户'" + recruitUserInfo.getIdCard() + "'失败，身份证号已存在");
        }
        else if (StringUtils.isNotEmpty(recruitUserInfo.getPhone()) && !recruitUserInfoService.checkPhoneUnique2(recruitUserInfo))
        {
            return error("新增用户'" + recruitUserInfo.getUserName() + "'失败，手机号码已存在");
        }
        recruitUserInfo.setPassword(SecurityUtils.encryptPassword(recruitUserInfo.getPassword()));
        recruitUserInfo.setNewUser(0);
        return toAjax(recruitUserInfoService.insertRecruitUserInfo(recruitUserInfo));
    }

    /**
     * 修改用户信息
     */
    @PreAuthorize("@ss.hasPermi('core:customerSentiment:edit')")
    @Log(title = "用户信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RecruitUserInfo recruitUserInfo)
    {

        //校验身份证号
        if(recruitUserInfo.getIdCard() != null && !StringUtils.equals(recruitUserInfo.getIdCard(), "")) {
            String identityCard = IdCardUtil.IdentityCardVerification(recruitUserInfo.getIdCard());
            if (!StringUtils.equals(identityCard, "correct")) {
                return AjaxResult.error(HttpStatus.UNSUPPORTED_TYPE, identityCard);
            }
        }
        //校验手机号
        String verifyPhone = PhoneUtil.verifyPhone(recruitUserInfo.getPhone());
        if(!StringUtils.equals(verifyPhone, "success")){
            return AjaxResult.error(HttpStatus.UNSUPPORTED_TYPE, verifyPhone);
        }
        //校验邮箱
        if(recruitUserInfo.getMailbox() != null && !StringUtils.equals(recruitUserInfo.getMailbox(), "")) {
            if (!EmailUtil.emailFormat(recruitUserInfo.getMailbox())) {
                return AjaxResult.error(HttpStatus.UNSUPPORTED_TYPE, "邮箱格式不正确");
            }
        }

        if (!recruitUserInfoService.checkIdCardUnique(recruitUserInfo))
        {
            return AjaxResult.error("修改用户'" + recruitUserInfo.getIdCard() + "'失败，身份证号已存在");
        }
        else if (StringUtils.isNotEmpty(recruitUserInfo.getPhone()) && !recruitUserInfoService.checkPhoneUnique(recruitUserInfo))
        {
            return error("修改用户'" + recruitUserInfo.getUserName() + "'失败，手机号码已存在");
        }else if (StringUtils.isNotEmpty(recruitUserInfo.getMailbox()) && !recruitUserInfoService.checkEmailUnique(recruitUserInfo))
        {
            return error("修改用户'" + recruitUserInfo.getMailbox() + "'失败，邮箱已存在");
        }

        recruitUserInfo.setPassword(SecurityUtils.encryptPassword(recruitUserInfo.getPassword()));
        return toAjax(recruitUserInfoService.updateRecruitUserInfo(recruitUserInfo));
    }

    /**
     * 删除用户信息
     */
    @PreAuthorize("@ss.hasPermi('core:customerSentiment:remove')")
    @Log(title = "用户信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(recruitUserInfoService.deleteRecruitUserInfoByIds(ids));
    }

}
