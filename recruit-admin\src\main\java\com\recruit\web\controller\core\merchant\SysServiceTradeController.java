package com.recruit.web.controller.core.merchant;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.recruit.common.core.domain.TreeSelectTwo;
import com.recruit.common.utils.StringUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.recruit.common.annotation.Log;
import com.recruit.common.core.controller.BaseController;
import com.recruit.common.core.domain.AjaxResult;
import com.recruit.common.enums.BusinessType;
import org.springframework.web.multipart.MultipartFile;
import com.recruit.common.core.domain.entity.SysServiceTrade;
import com.recruit.core.service.ISysServiceTradeService;
import com.recruit.common.utils.poi.ExcelUtil;

/**
 * 服务行业Controller
 *
 * <AUTHOR>
 * @date 2023-05-27
 */
@RestController
@RequestMapping("/core/serviceTrade")
public class SysServiceTradeController extends BaseController
{
    @Autowired
    private ISysServiceTradeService sysServiceTradeService;

    /**
     * 查询服务行业列表
     */
    @PreAuthorize("@ss.hasPermi('core:serviceTrade:list')")
    @GetMapping("/list")
    public AjaxResult list(SysServiceTrade sysServiceTrade)
    {
        List<SysServiceTrade> list = sysServiceTradeService.selectSysServiceTradeList(sysServiceTrade);
        return success(list);
    }

    /**
     * 导出服务行业列表
     */
    @PreAuthorize("@ss.hasPermi('core:serviceTrade:export')")
    @Log(title = "服务行业", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysServiceTrade sysServiceTrade)
    {
        List<SysServiceTrade> list = sysServiceTradeService.selectSysServiceTradeList(sysServiceTrade);
        ExcelUtil<SysServiceTrade> util = new ExcelUtil<SysServiceTrade>(SysServiceTrade.class);
        util.exportExcel(response, list, "服务行业数据");
    }

    /**
     * 获取服务行业详细信息
     */
    @PreAuthorize("@ss.hasPermi('core:serviceTrade:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(sysServiceTradeService.selectSysServiceTradeById(id));
    }

    /**
     * 新增服务行业
     */
    @PreAuthorize("@ss.hasPermi('core:serviceTrade:add')")
    @Log(title = "服务行业", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SysServiceTrade sysServiceTrade)
    {
        return toAjax(sysServiceTradeService.insertSysServiceTrade(sysServiceTrade));
    }

    /**
     * 修改服务行业
     */
    @PreAuthorize("@ss.hasPermi('core:serviceTrade:edit')")
    @Log(title = "服务行业", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SysServiceTrade sysServiceTrade)
    {
        return toAjax(sysServiceTradeService.updateSysServiceTrade(sysServiceTrade));
    }

    /**
     * 删除服务行业
     */
    @PreAuthorize("@ss.hasPermi('core:serviceTrade:remove')")
    @Log(title = "服务行业", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(sysServiceTradeService.deleteSysServiceTradeByIds(ids));
    }


    /**
     * 导入服务行业
     * @param file
     * @param updateSupport
     * @return
     * @throws Exception
     */
    @PreAuthorize("@ss.hasPermi('core:serviceTrade:import')")
    @Log(title = "服务行业", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<SysServiceTrade> util = new ExcelUtil<>(SysServiceTrade.class);
        List<SysServiceTrade> lists = util.importExcel(file.getInputStream());
        String message = sysServiceTradeService.importSysServiceTrade(lists, updateSupport);
        return AjaxResult.success(message);
    }


    @GetMapping("/getServiceTradeList")
    public AjaxResult getServiceTradeList()
    {
        List<SysServiceTrade> lists = sysServiceTradeService.selectSysServiceTradeList(new SysServiceTrade());
        List<TreeSelectTwo>  list = sysServiceTradeService.buildtServiceTradeTreeSelectTwo(lists);
        return success(list);
    }

    @GetMapping("/list/exclude/{id}")
    public AjaxResult excludeChild(@PathVariable(value = "id", required = false) Long id)
    {
        List<SysServiceTrade> depts = sysServiceTradeService.selectSysServiceTradeList(new SysServiceTrade());
        depts.removeIf(d -> d.getId().intValue() == id || ArrayUtils.contains(StringUtils.split(d.getAncestors(), ","), id + ""));
        return success(depts);
    }


    @PreAuthorize("@ss.hasPermi('core:serviceTrade:list')")
    @GetMapping("/tradeTree")
    public AjaxResult tradeTree(SysServiceTrade sysServiceTrade)
    {
        sysServiceTrade.setFCode("0");
        List<SysServiceTrade> lists = sysServiceTradeService.selectSysServiceTradeList(sysServiceTrade);
        List<TreeSelectTwo>  list = sysServiceTradeService.buildtServiceTradeTreeSelectTwo(lists);
        return success(list);
    }

}
