package com.recruit.core.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.recruit.common.annotation.Excel;
import com.recruit.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 商户处罚信息对象 temporary_merchant_punish
 *
 * <AUTHOR>
 * @date 2023-05-30
 */
@Data
@ApiModel("商户处罚信息")
public class TemporaryMerchantPunish extends BaseEntity
{
    private static final long serialVersionUID = 1L;


    private Long id;


    @Excel(name = "商户信息id")
    @ApiModelProperty("商户信息id")
    private Long merchantInfoId;


    @Excel(name = "周期类型")
    @ApiModelProperty("周期类型")
    private String cycleType;


    @Excel(name = "数量")
    @ApiModelProperty("数量")
    private String number;


    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "处罚时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("处罚时间")
    private Date startTime;


    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "截止时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("截止时间")
    private Date entTime;


    @Excel(name = "处罚内容")
    @ApiModelProperty("处罚内容")
    private String punishContent;


    @Excel(name = "处罚人")
    @ApiModelProperty("处罚人")
    private String punisher;


    @ApiModelProperty("商户名称")
    private String serviceName;

}
