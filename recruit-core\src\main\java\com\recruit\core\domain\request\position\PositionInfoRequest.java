package com.recruit.core.domain.request.position;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Auther: <PERSON> kong
 * @Date: 2023/3/27 10:28
 * @Description:
 */
@Data
@ApiModel("查询企业在招职位2")
public class PositionInfoRequest {

    @ApiModelProperty("企业id")
    private Long enterpriseId;

    @ApiModelProperty("职位名称")
    private String positionName;

    @ApiModelProperty("工作地址id")
    private String workAddressId;

    @ApiModelProperty("工作经验")
    private String workExperience;

    @ApiModelProperty("最低学历")
    private String minimumEducation;

    @ApiModelProperty("最高薪资")
    private String maximumSalary;

    @ApiModelProperty("最低薪资")
    private String minimumWage;

    @ApiModelProperty("福利")
    private String materialBenefits;

}
