package com.recruit.web.core.redisMSMQ;

import com.recruit.common.constant.RedisMessageType;
import com.recruit.core.service.IRecruitUserSysNoticeService;
import com.recruit.system.domain.SysNotice;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * @Auther: <PERSON> kong
 * @Date: 2023/5/17 9:20
 * @Description: redis消息队列消费者
 */
@Slf4j
@Component
@EnableScheduling
public class MessageConsumer {

    @Autowired
    private IRecruitUserSysNoticeService recruitUserSysNoticeService;

    @Autowired
    private RedisTemplate<String,Object> redisTemplate;


    @Transactional
    @Scheduled(initialDelay = 5 * 1000, fixedRate = 2 * 1000)
    public void rPop() {
        //消费消息
        Object noticeMessage = redisTemplate.opsForList().rightPop(RedisMessageType.SYS_NOTICE);
        if(noticeMessage != null) {
            recruitUserSysNoticeService.sendMessage((SysNotice) noticeMessage);
        }
    }
}

