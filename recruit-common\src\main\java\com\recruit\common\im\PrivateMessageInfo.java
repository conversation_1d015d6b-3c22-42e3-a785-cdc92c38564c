package com.recruit.common.im;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.recruit.common.serializer.DateToLongSerializer;
import lombok.Data;

import java.util.Date;

@Data
public class PrivateMessageInfo {

    /*
     * 消息id
     */
    private long id;

    /*
     * 发送者id
     */
    private Long sendId;
    /*
     * 发送者id
     */
    private String sendName;

    /*
     * 接收者id
     */
    private Long recvId;

    /*
     * 接收者id
     */
    private String recvName;

    /*
     * 发送内容
     */
    private String content;

    /*
     * 消息内容类型 具体枚举值由应用层定义
     */
    private Integer type;

    /**
     * 发送时间
     */
    @JsonSerialize(using = DateToLongSerializer.class)
    private Date sendTime;

    /**
     * 发送用户头像
     */
    private String sendPic;

    /**
     * 接收用户头像
     */
    private String recvPic;

    /**
     * 简历名称
     */
    private String resumeName;

    /**
     * 时长
     */
    private int duration;

    /**
     * 交换状态
     */
    private String exchangeStatus;

}
