package com.recruit.core.domain;

import com.recruit.common.annotation.Excel;
import com.recruit.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 企业与用户关联对象 recruit_enterprise_users_rel
 *
 * <AUTHOR>
 * @date 2023-03-21
 */
@Data
@ApiModel("企业与用户关联")
public class RecruitEnterpriseUsersRel extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 企业id */
    @Excel(name = "企业id")
    @ApiModelProperty("企业id")
    private Long enterpriseId;

    /** 用户id */
    @Excel(name = "用户id")
    @ApiModelProperty("用户id")
    private Long userId;

    @ApiModelProperty("用户名称")
    private String userName;

    @ApiModelProperty("手机号")
    private String phone;

    /** 在职证明 */
    @Excel(name = "营业执照")
    @ApiModelProperty("营业执照")
    private String businessUrl;

    /** 职位 */
    @Excel(name = "职位")
    @ApiModelProperty("职位")
    private String position;

    /** 绑定状态 0认证中 1绑定成功 */
    @Excel(name = "绑定状态 0认证中 1绑定成功")
    @ApiModelProperty("绑定状态 0未绑定 1认证中 2绑定成功 3认证失败")
    private String bindingStatus;


    @ApiModelProperty("公司名称")
    private String enterpriseName;

    @ApiModelProperty("法人名称")
    private String corporationName;

    @ApiModelProperty("注册资本")
    private String registeredCapital;

    @ApiModelProperty("成立时间")
    private String establishedTime;

    @ApiModelProperty("营业执照")
    private String businessUrls;

    @Excel(name = "状态", dictType = "business_status")
    @ApiModelProperty("状态 0待审核，1审核通过，2审核不通过，3违规封号")
    private String status;

    @ApiModelProperty("经营范围")
    private String businessScope;

    @ApiModelProperty("组成形式")
    private String compositionForm;

    @ApiModelProperty("社会信用代码")
    private String socialCreditCode;

    @ApiModelProperty("地址")
    private String address;

    @ApiModelProperty("类型")
    private String type;

    @ApiModelProperty("证件号")
    private String idCard;

    @ApiModelProperty("邮箱")
    private String mailbox;

}
