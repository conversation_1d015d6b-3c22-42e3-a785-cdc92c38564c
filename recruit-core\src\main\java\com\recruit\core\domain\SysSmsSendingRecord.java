package com.recruit.core.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.recruit.common.annotation.Excel;
import com.recruit.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 短信发送记录对象 sys_sms_sending_record
 *
 * <AUTHOR>
 * @date 2023-05-31
 */
@Data
@ApiModel("短信发送记录")
public class SysSmsSendingRecord extends BaseEntity
{
    private static final long serialVersionUID = 1L;


    private Long id;



    @ApiModelProperty("用户id")
    private Long userId;

    @Excel(name = "用户名称")
    @ApiModelProperty("用户名称")
    private String userName;


    @Excel(name = "手机号")
    @ApiModelProperty("手机号")
    private String phone;


    @Excel(name = "公告标题")
    @ApiModelProperty("公告标题")
    private String noticeTitle;


    @Excel(name = "公告类型", dictType = "sys_notice_type")
    @ApiModelProperty("公告类型")
    private String noticeType;


    @Excel(name = "公告内容")
    @ApiModelProperty("公告内容")
    private String noticeContent;

    @Excel(name = "发送类型", dictType = "sent_type")
    @ApiModelProperty("发送类型")
    private String sentType;



    @ApiModelProperty("短信模板id")
    private Long smsTemplateId;


    @Excel(name = "短信模板名称")
    @ApiModelProperty("短信模板名称")
    private String templateName;


    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "发送时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("发送时间")
    private Date sendTime;


}
