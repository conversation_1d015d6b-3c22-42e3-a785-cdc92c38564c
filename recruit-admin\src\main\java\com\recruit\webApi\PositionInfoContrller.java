package com.recruit.webApi;

import com.recruit.common.core.controller.BaseController;
import com.recruit.common.core.domain.AjaxResult;
import com.recruit.common.core.page.TableDataInfo;
import com.recruit.common.utils.DateUtils;
import com.recruit.common.utils.DesensitizeUtil;
import com.recruit.common.utils.DictUtils;
import com.recruit.common.utils.StringUtils;
import com.recruit.common.utils.bean.BeanUtils;
import com.recruit.core.domain.RecruitCollect;
import com.recruit.core.domain.RecruitEnterpriseBusinessInfo;
import com.recruit.core.domain.RecruitEnterpriseInfo;
import com.recruit.core.domain.RecruitEnterpriseUsersRel;
import com.recruit.core.domain.RecruitPositionInfo;
import com.recruit.core.domain.request.position.JobApplicantPositionRequest;
import com.recruit.core.domain.request.position.MapJobSearchRequest;
import com.recruit.core.domain.request.position.PositionInfoRequest;
import com.recruit.core.domain.request.position.PositionStatusRequest;
import com.recruit.core.domain.request.position.PostPositionInfoRequest;
import com.recruit.core.service.IRecruitCollectService;
import com.recruit.core.service.IRecruitEnterpriseBusinessInfoService;
import com.recruit.core.service.IRecruitEnterpriseInfoService;
import com.recruit.core.service.IRecruitEnterpriseUsersRelService;
import com.recruit.core.service.IRecruitPositionInfoService;
import com.recruit.core.service.IRecruitPositionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Auther: Wu kong
 * @Date: 2023/3/26 10:18
 * @Description:
 */
@Slf4j
@Api(tags= "职位信息接口")
@RestController
@RequestMapping("/web/api/position")
public class PositionInfoContrller extends BaseController {

    @Autowired
    private IRecruitCollectService recruitCollectService;

    @Autowired
    private IRecruitPositionService recruitPositionService;

    @Autowired
    private IRecruitPositionInfoService recruitPositionInfoService;

    @Autowired
    private IRecruitEnterpriseInfoService recruitEnterpriseInfoService;

    @Autowired
    private IRecruitEnterpriseUsersRelService recruitEnterpriseUsersRelService;

    @Autowired
    private IRecruitEnterpriseBusinessInfoService recruitEnterpriseBusinessInfoService;


    @ApiOperation("发布和修改职位信息，职位发布后 状态 positionStatus 为审核中，审核通过后为待开放，审核失败后为审核失败，开放后为开放中，关闭后为已关闭")
    @PostMapping("/postPositionInfo")
    public AjaxResult postPositionInfo(@RequestBody @Validated PostPositionInfoRequest request) {

        RecruitEnterpriseUsersRel enterpriseUsersRel = recruitEnterpriseUsersRelService.selectEnterpriseUsersRelUserId(getUserId());
        if(StringUtils.isNotNull(enterpriseUsersRel)){
            if(enterpriseUsersRel.getIdCard() != null && !StringUtils.equals(enterpriseUsersRel.getIdCard(), "")) {
                if (StringUtils.equals(enterpriseUsersRel.getStatus(), "0")) {
                    return error("企业审核中");
                } else if (StringUtils.equals(enterpriseUsersRel.getStatus(), "1")) {
                    if(StringUtils.equals(request.getMinimumWage(), "面议")){
                        request.setMaximumSalary(null);
                    }
                    return toAjax(recruitPositionInfoService.postPositionInfo(request));
                } else if (StringUtils.equals(enterpriseUsersRel.getStatus(), "2")) {
                    return error("企业未通过审核");
                } else if (StringUtils.equals(enterpriseUsersRel.getStatus(), "3")) {
                    return error("企业信息违规封号");
                } else {
                    return error("用户数据不存在");
                }
            }else {
                return error("未实名认证，请先实名认证");
            }
        }else {
            return error("用户数据不存在");
        }
    }

    @ApiOperation("设置职位状态 positionStatus 关闭职位传4，开放职位传1")
    @PostMapping("/setPositionStatus")
    public AjaxResult setPositionStatus(@RequestBody @Validated PositionStatusRequest request) {
        return toAjax(recruitPositionInfoService.setPositionStatus(request));
    }


    @ApiOperation("企业用户查看发布职位列表, 全部传 ALL，开放中传1，待开放传0，审核失败传2，已关闭传4，审核中不需要在tab选中，但在全部里面可以显示")
    @GetMapping(value = "/getEnterprisePositionList", produces = {"application/json"})
    @ApiResponses({
            @ApiResponse(code = 200, message = "返回实体", response = RecruitPositionInfo.class)
    })
    public TableDataInfo getEnterprisePositionList(@NotBlank(message = "职位状态不能为空") String positionStatus) {
        RecruitEnterpriseUsersRel enterpriseUsersRel = recruitEnterpriseUsersRelService.selectEnterpriseUsersRelUserIdAndStatus(getUserId());
        if(StringUtils.isNotNull(enterpriseUsersRel)){
            RecruitPositionInfo positionInfo = new RecruitPositionInfo();
            positionInfo.setPublisherId(getUserId());
            positionInfo.setEnterpriseId(enterpriseUsersRel.getEnterpriseId());
            if(!StringUtils.equals(positionStatus, "ALL")){
                positionInfo.setPositionStatus(positionStatus);
            }
            List<RecruitPositionInfo> list = recruitPositionInfoService.getEnterprisePositionList(positionInfo);
            list.forEach(e->{
                //急聘字段
                if(e.getHelpWanted() == null || StringUtils.equals(e.getHelpWanted(), "")){
                    e.setHelpWanted("0");
                }

                if(e.getPositionCode() != null && !StringUtils.equals(e.getPositionCode(), "")) {
                    e.setPositionName(recruitPositionService.getMap(e.getPositionCode()));
                }
                if(e.getWorkExperience() != null && !StringUtils.equals(e.getWorkExperience(), "")){
                    e.setWorkExperienceName(DictUtils.getDictLabel("work_experience", e.getWorkExperience()));
                    if(e.getWorkExperienceName().equals("不限")){
                        e.setWorkExperienceName("经验不限");
                    }
                }else {
                    e.setWorkExperienceName("经验不限");
                }
                if(e.getMinimumEducation() != null && !StringUtils.equals(e.getMinimumEducation(), "")) {
                    e.setMinimumEducationName(DictUtils.getDictLabel("background_type", e.getMinimumEducation()));
                    if (e.getMinimumEducationName().equals("不限")) {
                        e.setMinimumEducationName("学历不限");
                    }
                }else {
                    e.setMinimumEducationName("学历不限");
                }
                if(e.getGenderRequirements() != null && !StringUtils.equals(e.getGenderRequirements(), "")) {
                    e.setGenderRequirementsName(DictUtils.getDictLabel("sys_user_sex", e.getGenderRequirements()));
                    if (e.getGenderRequirementsName().equals("不限")) {
                        e.setGenderRequirementsName("性别不限");
                    }
                }else {
                    e.setGenderRequirementsName("性别不限");
                }
                if(e.getTecruitmentType() != null && !StringUtils.equals(e.getTecruitmentType(), "")) {
                    e.setTecruitmentTypeName(DictUtils.getDictLabel("tecruitment_type", e.getTecruitmentType()));
                }
                if(e.getScale() != null && !StringUtils.equals(e.getScale(), "")) {
                    e.setScaleName(DictUtils.getDictLabel("scale_enterprises", e.getScale()));
                    if(e.getScaleName().equals("不限")){
                        e.setScaleName("规模不限");
                    }
                }else {
                    e.setScaleName("规模不限");
                }
                if(e.getEnterpriseNature() != null && !StringUtils.equals(e.getEnterpriseNature(), "")) {
                    e.setEnterpriseNatureName(DictUtils.getDictLabel("enterprise_nature", e.getEnterpriseNature()));
                }

                try {
                    //最高薪资
                    if(e.getMaximumSalary() != null && !StringUtils.equals(e.getMaximumSalary(), "")) {
                        BigDecimal maximumSalary = new BigDecimal(e.getMaximumSalary());
                        e.setMaximumSalary(maximumSalary.divide(new BigDecimal(1000))+"K");
                    }
                }catch (Exception s){
                    log.error("最高薪资转换问题，无需关注");
                }
                try {
                    //最低薪资
                    if(e.getMinimumWage() != null && !StringUtils.equals(e.getMinimumWage(), "")) {
                        BigDecimal minimumWage = new BigDecimal(e.getMinimumWage());
                        e.setMinimumWage(minimumWage.divide(new BigDecimal(1000))+"K");
                    }
                }catch (Exception s){
                    log.error("最低薪资转换问题，无需关注");
                }
                //如果最低最高金额相同，则最高金额赋值为空
                if(e.getMinimumWage() != null && e.getMaximumSalary() != null) {
                    if (e.getMinimumWage().equals(e.getMaximumSalary())) {
                        e.setMaximumSalary(null);
                    }
                }

            });
            return getDataTable(list);
        }else {
            return getDataTable(new ArrayList<>());
        }
    }

    @ApiOperation("查看企业发布职位列表")
    @GetMapping(value = "/getJobSeekersViewJobListings", produces = {"application/json"})
    @ApiResponses({
            @ApiResponse(code = 200, message = "返回实体", response = RecruitPositionInfo.class)
    })
    public TableDataInfo getJobSeekersViewJobListings(PositionInfoRequest request) {
        RecruitPositionInfo positionInfo = new RecruitPositionInfo();
        BeanUtils.copyBeanProp(positionInfo, request);
        positionInfo.setPositionStatus("1");
        List<RecruitPositionInfo> list = recruitPositionInfoService.getEnterprisePositionList(positionInfo);
        list.forEach(e->{

            //急聘字段
            if(e.getHelpWanted() == null || StringUtils.equals(e.getHelpWanted(), "")){
                e.setHelpWanted("0");
            }
            if(e.getWorkExperience() != null && !StringUtils.equals(e.getWorkExperience(), "")) {
                e.setWorkExperienceName(DictUtils.getDictLabel("work_experience", e.getWorkExperience()));
                if(e.getWorkExperienceName().equals("不限")){
                    e.setWorkExperienceName("经验不限");
                }
            }else {
                e.setWorkExperienceName("经验不限");
            }
            if(e.getPositionCode() != null && !StringUtils.equals(e.getPositionCode(), "")) {
                e.setPositionName(recruitPositionService.getMap(e.getPositionCode()));
            }
            if(e.getMinimumEducation() != null && !StringUtils.equals(e.getMinimumEducation(), "")) {
                e.setMinimumEducationName(DictUtils.getDictLabel("background_type", e.getMinimumEducation()));
                if (e.getMinimumEducationName().equals("不限")) {
                    e.setMinimumEducationName("学历不限");
                }
            }else {
                e.setMinimumEducationName("学历不限");
            }
            if(e.getGenderRequirements() != null && !StringUtils.equals(e.getGenderRequirements(), "")) {
                e.setGenderRequirementsName(DictUtils.getDictLabel("sys_user_sex", e.getGenderRequirements()));
                if (e.getGenderRequirementsName().equals("不限")) {
                    e.setGenderRequirementsName("性别不限");
                }
            }else {
                e.setGenderRequirementsName("性别不限");
            }
            if(e.getTecruitmentType() != null && !StringUtils.equals(e.getTecruitmentType(), "")) {
                e.setTecruitmentTypeName(DictUtils.getDictLabel("tecruitment_type", e.getTecruitmentType()));
            }
            if(e.getScale() != null && !StringUtils.equals(e.getScale(), "")) {
                e.setScaleName(DictUtils.getDictLabel("scale_enterprises", e.getScale()));
                if(e.getScaleName().equals("不限")){
                    e.setScaleName("规模不限");
                }
            }else {
                e.setScaleName("规模不限");
            }
            if(e.getEnterpriseNature() != null && !StringUtils.equals(e.getEnterpriseNature(), "")) {
                e.setEnterpriseNatureName(DictUtils.getDictLabel("enterprise_nature", e.getEnterpriseNature()));
            }

            try {
                //最高薪资
                if(e.getMaximumSalary() != null && !StringUtils.equals(e.getMaximumSalary(), "")) {
                    BigDecimal maximumSalary = new BigDecimal(e.getMaximumSalary());
                    e.setMaximumSalary(maximumSalary.divide(new BigDecimal(1000))+"K");
                }
            }catch (Exception s){
                log.error("最高薪资转换问题，无需关注");
            }
            try {
                //最低薪资
                if(e.getMinimumWage() != null && !StringUtils.equals(e.getMinimumWage(), "")) {
                    BigDecimal minimumWage = new BigDecimal(e.getMinimumWage());
                    e.setMinimumWage(minimumWage.divide(new BigDecimal(1000))+"K");
                }
            }catch (Exception s){
                log.error("最低薪资转换问题，无需关注");
            }
            //如果最低最高金额相同，则最高金额赋值为空
            if(e.getMinimumWage() != null && e.getMaximumSalary() != null) {
                if (e.getMinimumWage().equals(e.getMaximumSalary())) {
                    e.setMaximumSalary(null);
                }
            }
        });
        return getDataTable(list);
    }

    @ApiOperation("求职者职位列表, 查询类别 queryCategory，查询类别，1推荐，2最新，3急聘")
    @GetMapping(value = "/getJobApplicantPositionList", produces = {"application/json"})
    @ApiResponses({
            @ApiResponse(code = 200, message = "返回实体", response = RecruitPositionInfo.class)
    })
    public TableDataInfo getJobApplicantPositionList(JobApplicantPositionRequest request) {
        RecruitPositionInfo positionInfo = new RecruitPositionInfo();
        BeanUtils.copyBeanProp(positionInfo, request);
        if(positionInfo.getRegion() != null && !StringUtils.equals(positionInfo.getRegion(), "")){
            if(StringUtils.equals(positionInfo.getRegion(), "0")){
                positionInfo.setRegion(null);
            }
        }
        positionInfo.setPositionStatus("1");
        try {
            positionInfo.setUserId(getUserId());
        }catch (Exception e){
            log.error("获取用户id失败，该用户未登录，可忽略该信息");
        }
        //判断职位等级
        if(request.getPosition() != null && !StringUtils.equals(request.getPosition(), "")) {
            Integer grade = recruitPositionService.getMap1(request.getPosition());
            if (grade != null) {
                positionInfo.setGrade(grade);
            }
        }

        //公司福利
        List<String> materialBenefitsList = new ArrayList<>();
        if(request.getMaterialBenefits() != null && !StringUtils.equals(request.getMaterialBenefits(), "")){
            String[] materialBenefits = request.getMaterialBenefits().split(",");
            materialBenefitsList.addAll(Arrays.asList(materialBenefits));
        }
        if(materialBenefitsList.size() > 0) {
            positionInfo.setMaterialBenefits(materialBenefitsList.get(0));
            positionInfo.setMaterialBenefitsList(materialBenefitsList);
        }
        startPage();
        List<RecruitPositionInfo> list = recruitPositionInfoService.getJobApplicantPositionList(positionInfo);
        list.forEach(e->{
            //急聘字段
            if(e.getHelpWanted() == null || StringUtils.equals(e.getHelpWanted(), "")){
                e.setHelpWanted("0");
            }
            //期望岗位名称
            if(e.getPositionCode() != null && !StringUtils.equals(e.getPositionCode(), "")) {
                e.setPositionName(recruitPositionService.getMap(e.getPositionCode()));
            }
            if(e.getWorkExperience() != null && !StringUtils.equals(e.getWorkExperience(), "")) {
                e.setWorkExperienceName(DictUtils.getDictLabel("work_experience", e.getWorkExperience()));
                if(e.getWorkExperienceName().equals("不限")){
                    e.setWorkExperienceName("经验不限");
                }
            }else {
                e.setWorkExperienceName("经验不限");
            }
            if(e.getMinimumEducation() != null && !StringUtils.equals(e.getMinimumEducation(), "")) {
                e.setMinimumEducationName(DictUtils.getDictLabel("background_type", e.getMinimumEducation()));
                if (e.getMinimumEducationName().equals("不限")) {
                    e.setMinimumEducationName("学历不限");
                }
            }else {
                e.setMinimumEducationName("学历不限");
            }
            if(e.getGenderRequirements() != null && !StringUtils.equals(e.getGenderRequirements(), "")) {
                e.setGenderRequirementsName(DictUtils.getDictLabel("sys_user_sex", e.getGenderRequirements()));
                if (e.getGenderRequirementsName().equals("不限")) {
                    e.setGenderRequirementsName("性别不限");
                }
            }else {
                e.setGenderRequirementsName("性别不限");
            }
            if(e.getTecruitmentType() != null && !StringUtils.equals(e.getTecruitmentType(), "")) {
                e.setTecruitmentTypeName(DictUtils.getDictLabel("tecruitment_type", e.getTecruitmentType()));
            }
            if(e.getScale() != null && !StringUtils.equals(e.getScale(), "")) {
                e.setScaleName(DictUtils.getDictLabel("scale_enterprises", e.getScale()));
                if(e.getScaleName().equals("不限")){
                    e.setScaleName("规模不限");
                }
            }else {
                e.setScaleName("规模不限");
            }
            if(e.getEnterpriseNature() != null && !StringUtils.equals(e.getEnterpriseNature(), "")) {
                e.setEnterpriseNatureName(DictUtils.getDictLabel("enterprise_nature", e.getEnterpriseNature()));
            }
            if(positionInfo.getUserId() == null){
                //姓名脱敏
                e.setPublisherName(DesensitizeUtil.left(e.getPublisherName(), 1));
            }
            //公司福利
            if(e.getMaterialBenefits() != null && !StringUtils.equals(e.getMaterialBenefits(), "")){
                String[] sss = e.getMaterialBenefits().split(",");
                StringBuilder materialBenefits = new StringBuilder();
                for(String s : sss){
                    if(StringUtils.equals(materialBenefits, "")){
                        materialBenefits.append(DictUtils.getDictLabel("company_benefits",s));
                    }else {
                        materialBenefits.append(",").append(DictUtils.getDictLabel("company_benefits",s));
                    }
                }
            }
            try {
                //最高薪资
                if(e.getMaximumSalary() != null && !StringUtils.equals(e.getMaximumSalary(), "")) {
                    BigDecimal maximumSalary = new BigDecimal(e.getMaximumSalary());
                    e.setMaximumSalary(maximumSalary.divide(new BigDecimal(1000))+"K");
                }
            }catch (Exception s){
                log.error("最高薪资转换问题，无需关注");
            }
            try {
                //最低薪资
                if(e.getMinimumWage() != null && !StringUtils.equals(e.getMinimumWage(), "")) {
                    BigDecimal minimumWage = new BigDecimal(e.getMinimumWage());
                    e.setMinimumWage(minimumWage.divide(new BigDecimal(1000))+"K");
                }
            }catch (Exception s){
                log.error("最低薪资转换问题，无需关注");
            }
            //如果最低最高金额相同，则最高金额赋值为空
            if(e.getMinimumWage() != null && e.getMaximumSalary() != null) {
                if (e.getMinimumWage().equals(e.getMaximumSalary())) {
                    e.setMaximumSalary(null);
                }
            }
            //发布时间
            e.setReleaseTime(DateUtils.timeDistanceTwo(DateUtils.getNowDate(), e.getUpdateTime()));
            if(StringUtils.equals(e.getHeadSculpture(), "")){
                e.setHeadSculpture("https://minio.hmbhrc.com:9002/box-im/defaultAvatar/defaultAvatar.png");
            }
        });
        return getDataTable(list);
    }

    @ApiOperation("求职者 企业详情")
    @GetMapping(value = "/getJobApplicantBusinessDetails", produces = {"application/json"})
    @ApiResponses({
            @ApiResponse(code = 200, message = "返回实体", response = RecruitEnterpriseInfo.class)
    })
    public AjaxResult getJobApplicantBusinessDetails(@NotBlank(message = "企业ID不能为空") Long enterpriseId) {
        RecruitEnterpriseInfo enterpriseInfo = recruitEnterpriseInfoService.selectRecruitEnterpriseInfoById(enterpriseId);

        //企业规模
        if(enterpriseInfo.getScale() != null && !StringUtils.equals(enterpriseInfo.getScale(), "")) {
            enterpriseInfo.setScaleName(DictUtils.getDictLabel("scale_enterprises", enterpriseInfo.getScale()));
            if(enterpriseInfo.getScaleName().equals("不限")){
                enterpriseInfo.setScaleName("规模不限");
            }
        }else {
            enterpriseInfo.setScaleName("规模不限");
        }
        //企业性质
        if(enterpriseInfo.getEnterpriseNature() != null && !StringUtils.equals(enterpriseInfo.getEnterpriseNature(), "")) {
            enterpriseInfo.setEnterpriseNatureName(DictUtils.getDictLabel("enterprise_nature", enterpriseInfo.getEnterpriseNature()));
        }
        if(enterpriseInfo.getFinancingStage() != null && !StringUtils.equals(enterpriseInfo.getFinancingStage(), "")) {
            enterpriseInfo.setFinancingStageName(DictUtils.getDictLabel("financing_stage", enterpriseInfo.getFinancingStage()));
            if (enterpriseInfo.getFinancingStageName().equals("不限")) {
                enterpriseInfo.setFinancingStageName("融资不限");
            }
        }else {
            enterpriseInfo.setFinancingStageName("融资不限");
        }

        //查询收藏公司数
        try {
            RecruitCollect collect = new RecruitCollect();
            collect.setUserId(getUserId());
            collect.setCollectType("3");
            collect.setEnterpriseId(enterpriseInfo.getId());
            RecruitCollect collects = recruitCollectService.getRecruitCollect(collect);
            if (collects == null) {
                enterpriseInfo.setCollectionCompany(0);
            } else {
                enterpriseInfo.setCollectId(collects.getId());
                enterpriseInfo.setCollectionCompany(1);
            }
        }catch (Exception e){
            log.error("获取用户id失败，该用户未登录，可忽略该信息");
        }

        try {
            enterpriseInfo.setUserId(getUserId());
        }catch (Exception e){
            log.error("获取用户id失败，该用户未登录，可忽略该信息");
        }
        /*if(enterpriseInfo.getUserId() == null){
            enterpriseInfo.setEnterpriseName(DesensitizeUtil.left(enterpriseInfo.getEnterpriseName(), 2));
        }*/
        return success(enterpriseInfo);
    }


    @ApiOperation("获取企业工商信息")
    @GetMapping(value = "/getQualification", produces = {"application/json"})
    @ApiResponses({
            @ApiResponse(code = 200, message = "返回实体", response = RecruitEnterpriseBusinessInfo.class)
    })
    public AjaxResult getQualification(@NotBlank(message = "企业ID不能为空") Long enterpriseId) {
        return success(recruitEnterpriseBusinessInfoService.selectEnterpriseBusinessInfoByEnterpriseId(enterpriseId));
    }

    @ApiOperation("查看企业环境")
    @GetMapping("/getViewEnterpriseEnvironment")
    public AjaxResult getViewEnterpriseEnvironment(@NotBlank(message = "企业ID不能为空") Long enterpriseId) {
        Map<String, String> map = new HashMap<>();
        map.put("photoAlbum", recruitEnterpriseInfoService.getViewEnterpriseEnvironment(enterpriseId));
        return success(map);
    }

    @ApiOperation("查看职位详情")
    @GetMapping("/getJobDetails")
    public AjaxResult getJobDetails(@NotBlank(message = "职位ID不能为空") Long positionInfoId) {
        RecruitPositionInfo positionInfo = recruitPositionInfoService.selectRecruitPositionInfoById(positionInfoId);
        if(positionInfo != null) {

            //急聘字段
            if(positionInfo.getHelpWanted() == null || StringUtils.equals(positionInfo.getHelpWanted(), "")){
                positionInfo.setHelpWanted("0");
            }

            //在招职位数
            positionInfo.setPositionNum(recruitPositionInfoService.getPositionNum(positionInfo.getEnterpriseId()));

            //期望岗位名称
            if(positionInfo.getPositionCode() != null && !StringUtils.equals(positionInfo.getPositionCode(), "")) {
                positionInfo.setPositionName(recruitPositionService.getMap(positionInfo.getPositionCode()));
            }

            try {
                //查询收藏职位
                RecruitCollect collects = new RecruitCollect();
                collects.setUserId(getUserId());
                collects.setCollectType("1");
                collects.setPositionInfoId(positionInfo.getId());
                RecruitCollect collect = recruitCollectService.getRecruitCollect(collects);
                if (collect == null) {
                    positionInfo.setCollectionPosition(0);
                } else {
                    positionInfo.setCollectId(collect.getId());
                    positionInfo.setCollectionPosition(1);
                }
            }catch (Exception s){
                log.error("获取用户id失败，该用户未登录，可忽略该信息");
            }

            //工作经验
            if(positionInfo.getWorkExperience() != null && !StringUtils.equals(positionInfo.getWorkExperience(), "")) {
                positionInfo.setWorkExperienceName(DictUtils.getDictLabel("work_experience", positionInfo.getWorkExperience()));
                if(positionInfo.getWorkExperienceName().equals("不限")){
                    positionInfo.setWorkExperienceName("经验不限");
                }
            }else {
                positionInfo.setWorkExperienceName("经验不限");
            }
            //最低学历
            if(positionInfo.getMinimumEducation() != null && !StringUtils.equals(positionInfo.getMinimumEducation(), "")) {
                positionInfo.setMinimumEducationName(DictUtils.getDictLabel("background_type", positionInfo.getMinimumEducation()));
                if (positionInfo.getMinimumEducationName().equals("不限")) {
                    positionInfo.setMinimumEducationName("学历不限");
                }
            }else {
                positionInfo.setMinimumEducationName("学历不限");
            }
            //性别要求
            if(positionInfo.getGenderRequirements() != null && !StringUtils.equals(positionInfo.getGenderRequirements(), "")) {
                positionInfo.setGenderRequirementsName(DictUtils.getDictLabel("sys_user_sex", positionInfo.getGenderRequirements()));
                if (positionInfo.getGenderRequirementsName().equals("不限")) {
                    positionInfo.setGenderRequirementsName("性别不限");
                }
            }else {
                positionInfo.setGenderRequirementsName("性别不限");
            }
            //招聘类型
            if(positionInfo.getTecruitmentType() != null && !StringUtils.equals(positionInfo.getTecruitmentType(), "")) {
                positionInfo.setTecruitmentTypeName(DictUtils.getDictLabel("tecruitment_type", positionInfo.getTecruitmentType()));
            }
            //企业规模
            if(positionInfo.getScale() != null && !StringUtils.equals(positionInfo.getScale(), "")) {
                positionInfo.setScaleName(DictUtils.getDictLabel("scale_enterprises", positionInfo.getScale()));
                if(positionInfo.getScaleName().equals("不限")){
                    positionInfo.setScaleName("规模不限");
                }
            }else {
                positionInfo.setScaleName("规模不限");
            }
            //企业性质
            if(positionInfo.getEnterpriseNature() != null && !StringUtils.equals(positionInfo.getEnterpriseNature(), "")) {
                positionInfo.setEnterpriseNatureName(DictUtils.getDictLabel("enterprise_nature", positionInfo.getEnterpriseNature()));
            }
            //认证姓名
            if(positionInfo.getAuthenticationName() != null && !StringUtils.equals(positionInfo.getAuthenticationName(), "")){
                positionInfo.setPublisherName(positionInfo.getAuthenticationName());
            }
            try {
                positionInfo.setUserId(getUserId());
            }catch (Exception e){
                log.error("获取用户id失败，该用户未登录，可忽略该信息");
            }
            if(positionInfo.getUserId() == null){
                //公司名称脱敏
                //positionInfo.setEnterpriseName(DesensitizeUtil.left(positionInfo.getEnterpriseName(), 2));
                //姓名脱敏
                positionInfo.setPublisherName(DesensitizeUtil.left(positionInfo.getPublisherName(), 1));
            }

            try {
                //最高薪资
                if(positionInfo.getMaximumSalary() != null && !StringUtils.equals(positionInfo.getMaximumSalary(), "")) {
                    BigDecimal maximumSalary = new BigDecimal(positionInfo.getMaximumSalary());
                    positionInfo.setMaximumSalary(maximumSalary.divide(new BigDecimal(1000))+"K");
                }
            }catch (Exception s){
                log.error("最高薪资转换问题，无需关注");
            }
            try {
                //最低薪资
                if(positionInfo.getMinimumWage() != null && !StringUtils.equals(positionInfo.getMinimumWage(), "")) {
                    BigDecimal minimumWage = new BigDecimal(positionInfo.getMinimumWage());
                    positionInfo.setMinimumWage(minimumWage.divide(new BigDecimal(1000))+"K");
                }
            }catch (Exception s){
                log.error("最低薪资转换问题，无需关注");
            }
            //如果最低最高金额相同，则最高金额赋值为空
            if(positionInfo.getMinimumWage() != null && positionInfo.getMaximumSalary() != null) {
                if (positionInfo.getMinimumWage().equals(positionInfo.getMaximumSalary())) {
                    positionInfo.setMaximumSalary(null);
                }
            }
            if(StringUtils.equals(positionInfo.getHeadSculpture(), "")){
                positionInfo.setHeadSculpture("https://minio.hmbhrc.com:9002/box-im/defaultAvatar/defaultAvatar.png");
            }


        }
        return success(positionInfo);
    }

    @ApiOperation("查询在招职位数")
    @GetMapping("/getRecruitmentPositionNum")
    public AjaxResult getRecruitmentPositionNum(@NotBlank(message = "企业ID不能为空") Long enterpriseId) {
        Map<String, Integer> map = new HashMap<>();
        map.put("positionNum", recruitPositionInfoService.getPositionNum(enterpriseId));
        return success(map);
    }


    @ApiOperation("地图找工作，直线距离，不限《0》，1公里《1》，3公里《2》，5公里《3》，7公里《4》，10公里《5》")
    @GetMapping("/getMapJobSearch")
    public TableDataInfo getMapJobSearch(MapJobSearchRequest request) {
        switch (request.getLinearDistance()){
            case "0":
                request.setLinearDistance(null);
                break;
            case "1":
                request.setLinearDistance("1000");
                break;
            case "2":
                request.setLinearDistance("3000");
                break;
            case "3":
                request.setLinearDistance("5000");
                break;
            case "4":
                request.setLinearDistance("6000");
                break;
            case "5":
                request.setLinearDistance("10000");
                break;
        }
        RecruitPositionInfo positionInfo = new RecruitPositionInfo();
        BeanUtils.copyBeanProp(positionInfo, request);
        if(positionInfo.getRegion() != null && !StringUtils.equals(positionInfo.getRegion(), "")){
            if(StringUtils.equals(positionInfo.getRegion(), "0")){
                positionInfo.setRegion(null);
            }
        }
        positionInfo.setPositionStatus("1");
        try {
            //positionInfo.setUserId(91L);
            positionInfo.setUserId(getUserId());
        }catch (Exception e){
            log.error("获取用户id失败，该用户未登录，可忽略该信息");
        }
        //判断职位等级
        if(request.getPosition() != null && !StringUtils.equals(request.getPosition(), "")) {
            Integer grade = recruitPositionService.getMap1(request.getPosition());
            if (grade != null) {
                positionInfo.setGrade(grade);
            }
        }

        List<RecruitPositionInfo> lists = recruitPositionInfoService.getMapJobSearch(positionInfo);
        lists.forEach(e->{
            //期望岗位名称
            if(e.getPositionCode() != null && !StringUtils.equals(e.getPositionCode(), "")) {
                e.setPositionName(recruitPositionService.getMap(e.getPositionCode()));
            }
            if(e.getWorkExperience() != null && !StringUtils.equals(e.getWorkExperience(), "")) {
                e.setWorkExperienceName(DictUtils.getDictLabel("work_experience", e.getWorkExperience()));
                if(e.getWorkExperienceName().equals("不限")){
                    e.setWorkExperienceName("经验不限");
                }
            }else {
                e.setWorkExperienceName("经验不限");
            }
            if(e.getMinimumEducation() != null && !StringUtils.equals(e.getMinimumEducation(), "")) {
                e.setMinimumEducationName(DictUtils.getDictLabel("background_type", e.getMinimumEducation()));
                if (e.getMinimumEducationName().equals("不限")) {
                    e.setMinimumEducationName("学历不限");
                }
            }else {
                e.setMinimumEducationName("学历不限");
            }
            if(e.getGenderRequirements() != null && !StringUtils.equals(e.getGenderRequirements(), "")) {
                e.setGenderRequirementsName(DictUtils.getDictLabel("sys_user_sex", e.getGenderRequirements()));
                if (e.getGenderRequirementsName().equals("不限")) {
                    e.setGenderRequirementsName("性别不限");
                }
            }else {
                e.setGenderRequirementsName("性别不限");
            }
            if(e.getTecruitmentType() != null && !StringUtils.equals(e.getTecruitmentType(), "")) {
                e.setTecruitmentTypeName(DictUtils.getDictLabel("tecruitment_type", e.getTecruitmentType()));
            }
            if(e.getScale() != null && !StringUtils.equals(e.getScale(), "")) {
                e.setScaleName(DictUtils.getDictLabel("scale_enterprises", e.getScale()));
                if(e.getScaleName().equals("不限")){
                    e.setScaleName("规模不限");
                }
            }else {
                e.setScaleName("规模不限");
            }
            if(e.getEnterpriseNature() != null && !StringUtils.equals(e.getEnterpriseNature(), "")) {
                e.setEnterpriseNatureName(DictUtils.getDictLabel("enterprise_nature", e.getEnterpriseNature()));
            }

            try {
                //最高薪资
                if(e.getMaximumSalary() != null && !StringUtils.equals(e.getMaximumSalary(), "")) {
                    BigDecimal maximumSalary = new BigDecimal(e.getMaximumSalary());
                    e.setMaximumSalary(maximumSalary.divide(new BigDecimal(1000))+"K");
                }
            }catch (Exception s){
                log.error("最高薪资转换问题，无需关注");
            }
            try {
                //最低薪资
                if(e.getMinimumWage() != null && !StringUtils.equals(e.getMinimumWage(), "")) {
                    BigDecimal minimumWage = new BigDecimal(e.getMinimumWage());
                    e.setMinimumWage(minimumWage.divide(new BigDecimal(1000))+"K");
                }
            }catch (Exception s){
                log.error("最低薪资转换问题，无需关注");
            }
            //如果最低最高金额相同，则最高金额赋值为空
            if(e.getMinimumWage() != null && e.getMaximumSalary() != null) {
                if (e.getMinimumWage().equals(e.getMaximumSalary())) {
                    e.setMaximumSalary(null);
                }
            }
        });
        return getDataTable(lists);
    }

}
