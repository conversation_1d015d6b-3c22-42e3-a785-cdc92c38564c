package com.recruit.core.domain;

import lombok.Data;
import java.util.Date;

/**
 * <p>
 *  用户
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-01
 */
@Data
public class User {

    private static final long serialVersionUID=1L;

    /**
     * id
     */
    private Long id;

    /**
     * 用户名
     */
    private String userName;

    /**
     * 用户名
     */
    private String nickName;

    /**
     * 性别
     */
    private Integer sex;

    /**
     * 头像
     */
    private String headImage;

    /**
     * 头像缩略图
     */
    private String headImageThumb;


    /**
     * 个性签名
     */
    private String signature;
    /**
     * 密码(明文)
     */
    private String password;

    /**
     * 最后登录时间
     */
    private Date lastLoginTime;

    /**
     * 创建时间
     */
    private Date createdTime;


}
