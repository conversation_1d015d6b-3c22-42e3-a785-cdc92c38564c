package com.recruit.core.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.recruit.common.annotation.Excel;
import com.recruit.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 用户订单子单对象 temporary_service_user_sub_order
 *
 * <AUTHOR>
 * @date 2023-06-03
 */
@Data
@ApiModel("用户订单子单")
public class TemporaryServiceUserSubOrder extends BaseEntity
{
    private static final long serialVersionUID = 1L;


    private Long id;


    @Excel(name = "用户订单表id")
    @ApiModelProperty("用户订单表id")
    private Long serviceUserOrderId;

    @ApiModelProperty("下单人")
    private Long placeOrderUserId;

    @Excel(name = "下单人", sort = 1)
    @ApiModelProperty("下单人")
    private String placeOrderUserName;


    @Excel(name = "联系人", sort = 2)
    @ApiModelProperty("联系人")
    private String liaisonNan;


    @Excel(name = "下单人手机号", sort = 3)
    @ApiModelProperty("下单人手机号")
    private String placeOrderPhone;


    @ApiModelProperty("接单人id")
    private Long receiverUserId;

    @Excel(name = "接单人姓名", sort = 4)
    @ApiModelProperty("接单人姓名")
    private String receiverUserName;


    @ApiModelProperty("商户id")
    private Long merchantInfoId;

    @Excel(name = "服务类别", sort = 5)
    @ApiModelProperty("服务类别")
    private String serviceGoodsName;

    @Excel(name = "服务名称", sort = 6)
    @ApiModelProperty("服务名称")
    private String serviceGoodsPriceName;


    @Excel(name = "商户名称", sort = 7)
    @ApiModelProperty("商户名称")
    private String merchantInfoName;


    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "预约日期", width = 30, dateFormat = "yyyy-MM-dd", sort = 8)
    @ApiModelProperty("预约日期")
    private Date arrivalTime;


    @Excel(name = "到门时间", dictType = "appointment_time", sort = 9)
    @ApiModelProperty("到门时间")
    private String toDoorTime;


    @Excel(name = "订单价格", sort = 10)
    @ApiModelProperty("订单价格")
    private BigDecimal orderPrice;


    @Excel(name = "订单状态", dictType = "user_sub_order_status", sort = 11)
    @ApiModelProperty("订单状态 0预约订单，1进行中，2已完成，3订单取消")
    private String orderStatus;


    @Excel(name = "用户地址", sort = 12)
    @ApiModelProperty("用户地址")
    private String address;


    @Excel(name = "门牌号", sort = 13)
    @ApiModelProperty("门牌号")
    private String houseNumber;


    @ApiModelProperty("经度")
    private String longitude;


    @ApiModelProperty("纬度")
    private String latitude;


}
