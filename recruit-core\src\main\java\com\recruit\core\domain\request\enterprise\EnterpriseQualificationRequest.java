package com.recruit.core.domain.request.enterprise;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @Auther: <PERSON> kong
 * @Date: 2023/3/21 21:46
 * @Description:
 */
@Data
@ApiModel("企业资质1")
public class EnterpriseQualificationRequest {

    @NotBlank(message = "营业执照不能为空")
    @ApiModelProperty("营业执照")
    private String businessUrl;

}
