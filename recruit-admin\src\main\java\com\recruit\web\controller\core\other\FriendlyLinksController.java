package com.recruit.web.controller.core.other;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.recruit.common.annotation.Log;
import com.recruit.common.core.controller.BaseController;
import com.recruit.common.core.domain.AjaxResult;
import com.recruit.common.enums.BusinessType;
import org.springframework.web.multipart.MultipartFile;
import com.recruit.core.domain.FriendlyLinks;
import com.recruit.core.service.IFriendlyLinksService;
import com.recruit.common.utils.poi.ExcelUtil;
import com.recruit.common.core.page.TableDataInfo;

/**
 * 友情链接Controller
 *
 * <AUTHOR>
 * @date 2023-06-18
 */
@RestController
@RequestMapping("/core/friendlyLinks")
public class FriendlyLinksController extends BaseController
{
    @Autowired
    private IFriendlyLinksService friendlyLinksService;

    /**
     * 查询友情链接列表
     */
    @PreAuthorize("@ss.hasPermi('core:friendlyLinks:list')")
    @GetMapping("/list")
    public TableDataInfo list(FriendlyLinks friendlyLinks)
    {
        startPage();
        List<FriendlyLinks> list = friendlyLinksService.selectFriendlyLinksList(friendlyLinks);
        return getDataTable(list);
    }

    /**
     * 导出友情链接列表
     */
    @PreAuthorize("@ss.hasPermi('core:friendlyLinks:export')")
    @Log(title = "友情链接", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, FriendlyLinks friendlyLinks)
    {
        List<FriendlyLinks> list = friendlyLinksService.selectFriendlyLinksList(friendlyLinks);
        ExcelUtil<FriendlyLinks> util = new ExcelUtil<FriendlyLinks>(FriendlyLinks.class);
        util.exportExcel(response, list, "友情链接数据");
    }

    /**
     * 获取友情链接详细信息
     */
    @PreAuthorize("@ss.hasPermi('core:friendlyLinks:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(friendlyLinksService.selectFriendlyLinksById(id));
    }

    /**
     * 新增友情链接
     */
    @PreAuthorize("@ss.hasPermi('core:friendlyLinks:add')")
    @Log(title = "友情链接", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody FriendlyLinks friendlyLinks)
    {
        return toAjax(friendlyLinksService.insertFriendlyLinks(friendlyLinks));
    }

    /**
     * 修改友情链接
     */
    @PreAuthorize("@ss.hasPermi('core:friendlyLinks:edit')")
    @Log(title = "友情链接", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody FriendlyLinks friendlyLinks)
    {
        return toAjax(friendlyLinksService.updateFriendlyLinks(friendlyLinks));
    }

    /**
     * 删除友情链接
     */
    @PreAuthorize("@ss.hasPermi('core:friendlyLinks:remove')")
    @Log(title = "友情链接", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(friendlyLinksService.deleteFriendlyLinksByIds(ids));
    }


    /**
     * 导入友情链接
     * @param file
     * @param updateSupport
     * @return
     * @throws Exception
     */
    @PreAuthorize("@ss.hasPermi('core:friendlyLinks:import')")
    @Log(title = "友情链接", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<FriendlyLinks> util = new ExcelUtil<>(FriendlyLinks.class);
        List<FriendlyLinks> lists = util.importExcel(file.getInputStream());
        String message = friendlyLinksService.importFriendlyLinks(lists, updateSupport);
        return AjaxResult.success(message);
    }


}
