package com.recruit.webApi;

import com.recruit.common.core.controller.BaseController;
import com.recruit.common.core.domain.AjaxResult;
import com.recruit.common.core.domain.vo.PrivateMessageVO;
import com.recruit.common.core.page.TableDataInfo;
import com.recruit.common.utils.bean.BeanUtils;
import com.recruit.core.domain.RecruitEnterpriseUsersRel;
import com.recruit.core.domain.RecruitInterviewInfo;
import com.recruit.core.domain.request.other.InterviewInvitationRequest;
import com.recruit.core.service.IRecruitEnterpriseUsersRelService;
import com.recruit.core.service.IRecruitInterviewInfoService;
import com.recruit.core.service.IPrivateMessageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Auther: Wu kong
 * @Date: 2023/4/23 18:02
 * @Description:
 */
@Api(tags= "（4-23）面试邀请")
@Slf4j
@RestController
@RequestMapping("/web/api/InterviewInfo")
public class InterviewInfoController extends BaseController {

    @Autowired
    private IPrivateMessageService privateMessageService;

    @Autowired
    private IRecruitInterviewInfoService recruitInterviewInfoService;

    @Autowired
    private IRecruitEnterpriseUsersRelService recruitEnterpriseUsersRelService;


    @ApiOperation("企业查看面试列表")
    @GetMapping(value = "/getInterviewInfoList", produces = {"application/json"})
    @ApiResponses({
            @ApiResponse(code = 200, message = "返回实体", response = RecruitInterviewInfo.class)
    })
    public TableDataInfo getInterviewInfoList(@NotNull(message = "页码不能为空") @RequestParam Integer page,
                                              @NotNull(message = "size不能为空") @RequestParam Integer size)
    {

        RecruitEnterpriseUsersRel enterpriseUsersRel = recruitEnterpriseUsersRelService.selectEnterpriseUsersRelUserId(getUserId());
        startPageTwo(page, size);
        RecruitInterviewInfo recruitInterviewInfo = new RecruitInterviewInfo();
        recruitInterviewInfo.setInviterId(getUserId());
        if(enterpriseUsersRel != null) {
            recruitInterviewInfo.setEnterpriseId(enterpriseUsersRel.getEnterpriseId());
        }
        List<RecruitInterviewInfo> list = recruitInterviewInfoService.selectRecruitInterviewInfoList(recruitInterviewInfo);
        return getDataTable(list);
    }


    @ApiOperation("个人查看面试列表")
    @GetMapping(value = "/getPersonalInterviewInfoList", produces = {"application/json"})
    @ApiResponses({
            @ApiResponse(code = 200, message = "返回实体", response = RecruitInterviewInfo.class)
    })
    public TableDataInfo getPersonalInterviewInfoList(@NotNull(message = "页码不能为空") @RequestParam Integer page,
                                              @NotNull(message = "size不能为空") @RequestParam Integer size)
    {
        startPageTwo(page, size);
        RecruitInterviewInfo recruitInterviewInfo = new RecruitInterviewInfo();
        recruitInterviewInfo.setInviteeId(getUserId());
        List<RecruitInterviewInfo> list = recruitInterviewInfoService.selectRecruitInterviewInfoList(recruitInterviewInfo);
        return getDataTable(list);
    }


    @ApiOperation("查看面试邀请列表详情")
    @GetMapping("/getInterviewInfoInfo")
    public AjaxResult getInterviewInfoInfo(@RequestParam Long id)
    {
        return success(recruitInterviewInfoService.selectRecruitInterviewInfoById(id));
    }


    @ApiOperation("查看聊天记录面试邀请详情")
    @GetMapping("/getChatRecordResumeDetails")
    public AjaxResult getChatRecordResumeDetails(@NotBlank(message = "消息不能为空") @RequestParam Long privateMessageId)
    {
        return success(recruitInterviewInfoService.selectInterviewInfoByPrivateMessageId(privateMessageId));
    }


    @ApiOperation("添加面试邀请")
    @PostMapping("/addInterviewInvitation")
    public AjaxResult addInterviewInvitation(@RequestBody @Validated InterviewInvitationRequest request)
    {
        PrivateMessageVO vo = new PrivateMessageVO();
        vo.setType(6);
        vo.setContent("面试邀请");
        vo.setRecvId(request.getRecvId());
        Long privateMessageId = privateMessageService.sendMessage(vo);
        RecruitInterviewInfo recruitInterviewInfo = new RecruitInterviewInfo();
        BeanUtils.copyBeanProp(recruitInterviewInfo, request);
        recruitInterviewInfo.setPrivateMessageId(privateMessageId);
        recruitInterviewInfo.setInviteeId(request.getRecvId());
        recruitInterviewInfo.setInviterId(getUserId());
        recruitInterviewInfo.setExchangeStatus("0");
        return success(recruitInterviewInfoService.insertRecruitInterviewInfo(recruitInterviewInfo));
    }


}
