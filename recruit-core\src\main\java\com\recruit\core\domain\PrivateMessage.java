package com.recruit.core.domain;


import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-01
 */
@Data
public class PrivateMessage {

    private static final long serialVersionUID=1L;

    /**
     * id
     */
    private Long id;

    /**
     * 发送用户id
     */
    private Long sendId;

    /**
     * 发送用户id
     */
    private String sendName;

    /**
     * 接收用户id
     */
    private Long recvId;

    /**
     * 接收用户id
     */
    private String recvName;

    /**
     * 发送内容
     */
    private String content;

    /**
     * 消息类型 0:文字 1:图片 2:文件 3:语音，4：交换手机号，5：交换微信 10:系统提示
     */
    private Integer type;

    /**
     * 状态
     */
    private Integer status;


    /**
     * 发送时间
     */
    private Date sendTime;

    /**
     * 发送用户头像
     */
    private String sendPic;

    /**
     * 接收用户头像
     */
    private String recvPic;
    /**
     * 发送用户id
     */
    private Long flag;
    /**
     * 发送用户id
     */
    private Long friendSendId;
    /**
     * 发送用户id
     */
    private Long friendRecvId;

    /**
     * 简历名称
     */
    private String resumeName;

    /**
     * 时长
     */
    private int duration;

    /**
     * 交换状态
     */
    private String exchangeStatus;

    /**
     * 校验发送id
     */
    private Long checkSendId;


    private String checks;



}
