package com.recruit.core.domain;

import com.recruit.common.annotation.Excel;
import com.recruit.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.List;

/**
 * 搜索职位热词对象 sys_popular_search_terms
 *
 * <AUTHOR>
 * @date 2023-04-15
 */
@Data
@ApiModel("搜索职位热词")
public class SysPopularSearchTerms extends BaseEntity
{
    private static final long serialVersionUID = 1L;


    private Long id;


    @Excel(name = "职位编码")
    @ApiModelProperty("职位编码")
    private String positionCode;

    @ApiModelProperty("职位编码名称")
    private String positionCodeName;


    @ApiModelProperty("职位编码")
    private List<String> positionCodes;


    @Excel(name = "显示顺序")
    @ApiModelProperty("显示顺序")
    private Integer orderNum;


}
