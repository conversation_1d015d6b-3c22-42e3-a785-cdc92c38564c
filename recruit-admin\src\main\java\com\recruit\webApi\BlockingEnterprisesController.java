package com.recruit.webApi;

import com.recruit.common.core.controller.BaseController;
import com.recruit.common.core.domain.AjaxResult;
import com.recruit.common.core.page.TableDataInfo;
import com.recruit.common.utils.DictUtils;
import com.recruit.common.utils.StringUtils;
import com.recruit.core.domain.RecruitBlockingEnterprises;
import com.recruit.core.domain.request.other.BlockingEnterprisesRequest;
import com.recruit.core.service.IRecruitBlockingEnterprisesService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Auther: Wu kong
 * @Date: 2023/4/12 9:22
 * @Description:
 */
@Api(tags= "(04-12)屏蔽公司接口")
@RestController
@RequestMapping("/web/api/blocking")
public class BlockingEnterprisesController  extends BaseController {

    @Autowired
    private IRecruitBlockingEnterprisesService recruitBlockingEnterprisesService;


    @ApiOperation("查看屏蔽公司列表, 传企业名称和分页字段 pageNum pageSize")
    @GetMapping("/getEnterprisesList")
    public TableDataInfo getEnterprisesList(String enterpriseName,
                                            @NotNull(message = "页码不能为空") @RequestParam Integer page,
                                            @NotNull(message = "size不能为空") @RequestParam Integer size)
    {
        startPageTwo(page, size);
        RecruitBlockingEnterprises blockingEnterprises = new RecruitBlockingEnterprises();
        blockingEnterprises.setEnterpriseName(enterpriseName);
        List<RecruitBlockingEnterprises> list = recruitBlockingEnterprisesService.selectRecruitBlockingEnterprisesList(blockingEnterprises);
        list.forEach(e->{
            if(e.getScale() != null && !StringUtils.equals(e.getScale(), "")) {
                e.setScaleName(DictUtils.getDictLabel("scale_enterprises", e.getScale()));
                if(e.getScaleName().equals("不限")){
                    e.setScaleName("规模不限");
                }
            }else {
                e.setScaleName("规模不限");
            }
            if(e.getEnterpriseNature() != null && !StringUtils.equals(e.getEnterpriseNature(), "")) {
                e.setEnterpriseNatureName(DictUtils.getDictLabel("enterprise_nature", e.getEnterpriseNature()));
            }
        });
        return getDataTable(list);
    }


    @ApiOperation("查看屏蔽公司数量")
    @GetMapping("/getEnterprisesNum")
    public AjaxResult getEnterprisesNum()
    {
        RecruitBlockingEnterprises blockingEnterprises = new RecruitBlockingEnterprises();
        blockingEnterprises.setUserId(getUserId());
        return success(recruitBlockingEnterprisesService.getEnterprisesNum(blockingEnterprises));
    }


    @DeleteMapping("/delEnterprises/{id}")
    @ApiOperation(value = "解除屏蔽公司",notes="解除屏蔽公司")
    public AjaxResult recallMessage(@NotNull(message = "消息id不能为空") @PathVariable Long id){
        return success(recruitBlockingEnterprisesService.deleteRecruitBlockingEnterprisesById(id));
    }

    @ApiOperation(value = "添加屏蔽公司",notes="添加屏蔽公司")
    @PostMapping("/addEnterprises")
    public AjaxResult addEnterprises(@RequestBody BlockingEnterprisesRequest request)
    {
        RecruitBlockingEnterprises enterprises = new RecruitBlockingEnterprises();
        enterprises.setEnterpriseId(request.getEnterpriseId());
        enterprises.setUserId(getUserId());
        return toAjax(recruitBlockingEnterprisesService.insertRecruitBlockingEnterprises(enterprises));
    }

}
