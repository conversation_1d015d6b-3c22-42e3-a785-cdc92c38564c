package com.recruit.common.utils;

import java.util.HashMap;
import java.util.Map;

public class ChineseNationalityCodeUtil {
    /**
     * 中国民族-相关码表 chinese nationality code
     */
    private static final Map<Integer, String> CHINESE_NATIONALITY_CODE = new HashMap<>();

    static {
        //民族代码表 56个民族
        CHINESE_NATIONALITY_CODE.put(1, "汉族");
        CHINESE_NATIONALITY_CODE.put(2, "蒙族");
        CHINESE_NATIONALITY_CODE.put(3, "回族");
        CHINESE_NATIONALITY_CODE.put(4, "藏族");
        CHINESE_NATIONALITY_CODE.put(5, "维吾尔族");
        CHINESE_NATIONALITY_CODE.put(6, "苗族");
        CHINESE_NATIONALITY_CODE.put(7, "彝族");
        CHINESE_NATIONALITY_CODE.put(8, "壮族");
        CHINESE_NATIONALITY_CODE.put(9, "布依族");
        CHINESE_NATIONALITY_CODE.put(10, "朝鲜族");
        CHINESE_NATIONALITY_CODE.put(11, "满族");
        CHINESE_NATIONALITY_CODE.put(12, "侗族");
        CHINESE_NATIONALITY_CODE.put(13, "瑶族");
        CHINESE_NATIONALITY_CODE.put(14, "白族");
        CHINESE_NATIONALITY_CODE.put(15, "土家族");
        CHINESE_NATIONALITY_CODE.put(16, "哈尼族");
        CHINESE_NATIONALITY_CODE.put(17, "哈萨克族");
        CHINESE_NATIONALITY_CODE.put(18, "傣族");
        CHINESE_NATIONALITY_CODE.put(19, "黎族");
        CHINESE_NATIONALITY_CODE.put(20, "傈僳族");
        CHINESE_NATIONALITY_CODE.put(21, "佤族");
        CHINESE_NATIONALITY_CODE.put(22, "畲族");
        CHINESE_NATIONALITY_CODE.put(23, "高山族");
        CHINESE_NATIONALITY_CODE.put(24, "拉祜族");
        CHINESE_NATIONALITY_CODE.put(25, "水族");
        CHINESE_NATIONALITY_CODE.put(26, "东乡族");
        CHINESE_NATIONALITY_CODE.put(27, "纳西族");
        CHINESE_NATIONALITY_CODE.put(28, "景颇族");
        CHINESE_NATIONALITY_CODE.put(29, "柯尔克孜族");
        CHINESE_NATIONALITY_CODE.put(30, "撒拉族");
        CHINESE_NATIONALITY_CODE.put(31, "达斡尔族");
        CHINESE_NATIONALITY_CODE.put(32, "仫佬族");
        CHINESE_NATIONALITY_CODE.put(33, "羌族");
        CHINESE_NATIONALITY_CODE.put(34, "布朗族");
        CHINESE_NATIONALITY_CODE.put(35, "撒拉族");
        CHINESE_NATIONALITY_CODE.put(36, "毛难族");
        CHINESE_NATIONALITY_CODE.put(37, "仡佬族");
        CHINESE_NATIONALITY_CODE.put(38, "锡伯族");
        CHINESE_NATIONALITY_CODE.put(39, "阿昌族");
        CHINESE_NATIONALITY_CODE.put(40, "普米族");
        CHINESE_NATIONALITY_CODE.put(41, "塔吉克族");
        CHINESE_NATIONALITY_CODE.put(42, "怒族");
        CHINESE_NATIONALITY_CODE.put(43, "乌孜别克族");
        CHINESE_NATIONALITY_CODE.put(44, "俄罗斯族");
        CHINESE_NATIONALITY_CODE.put(45, "鄂温克族");
        CHINESE_NATIONALITY_CODE.put(46, "崩龙族");
        CHINESE_NATIONALITY_CODE.put(47, "保安族");
        CHINESE_NATIONALITY_CODE.put(48, "裕固族");
        CHINESE_NATIONALITY_CODE.put(49, "京族");
        CHINESE_NATIONALITY_CODE.put(50, "塔塔尔族");
        CHINESE_NATIONALITY_CODE.put(51, "独龙族");
        CHINESE_NATIONALITY_CODE.put(52, "鄂伦春族");
        CHINESE_NATIONALITY_CODE.put(53, "德昂族");
        CHINESE_NATIONALITY_CODE.put(54, "门巴族");
        CHINESE_NATIONALITY_CODE.put(55, "珞巴族");
        CHINESE_NATIONALITY_CODE.put(56, "基诺族");
        // 其他  <====> 未定族称人口
        CHINESE_NATIONALITY_CODE.put(97, "其他");
        // 外国血统 <====> 外国血统中国籍人士 <===> 入籍
        CHINESE_NATIONALITY_CODE.put(98, "外国血统");
    }

    public static Map<Integer, String> getNationList(){
        return CHINESE_NATIONALITY_CODE;
    }

    public static String getNationInfo(int nationCode){
        return CHINESE_NATIONALITY_CODE.get(nationCode);
    }
}
