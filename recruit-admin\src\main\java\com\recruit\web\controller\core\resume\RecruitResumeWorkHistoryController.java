package com.recruit.web.controller.core.resume;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.recruit.common.annotation.Log;
import com.recruit.common.core.controller.BaseController;
import com.recruit.common.core.domain.AjaxResult;
import com.recruit.common.enums.BusinessType;
import com.recruit.core.domain.RecruitResumeWorkHistory;
import com.recruit.core.service.IRecruitResumeWorkHistoryService;
import com.recruit.common.utils.poi.ExcelUtil;
import com.recruit.common.core.page.TableDataInfo;

/**
 * 简历工作经历Controller
 *
 * <AUTHOR>
 * @date 2023-03-19
 */
@RestController
@RequestMapping("/core/resumeWorkHistory")
public class RecruitResumeWorkHistoryController extends BaseController
{
    @Autowired
    private IRecruitResumeWorkHistoryService recruitResumeWorkHistoryService;

    /**
     * 查询简历工作经历列表
     */
    @PreAuthorize("@ss.hasPermi('core:resumeWorkHistory:list')")
    @GetMapping("/list")
    public TableDataInfo list(RecruitResumeWorkHistory recruitResumeWorkHistory)
    {
        startPage();
        List<RecruitResumeWorkHistory> list = recruitResumeWorkHistoryService.selectRecruitResumeWorkHistoryList(recruitResumeWorkHistory);
        return getDataTable(list);
    }

    /**
     * 导出简历工作经历列表
     */
    @PreAuthorize("@ss.hasPermi('core:resumeWorkHistory:export')")
    @Log(title = "简历工作经历", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RecruitResumeWorkHistory recruitResumeWorkHistory)
    {
        List<RecruitResumeWorkHistory> list = recruitResumeWorkHistoryService.selectRecruitResumeWorkHistoryList(recruitResumeWorkHistory);
        ExcelUtil<RecruitResumeWorkHistory> util = new ExcelUtil<RecruitResumeWorkHistory>(RecruitResumeWorkHistory.class);
        util.exportExcel(response, list, "简历工作经历数据");
    }

    /**
     * 获取简历工作经历详细信息
     */
    @PreAuthorize("@ss.hasPermi('core:resumeWorkHistory:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(recruitResumeWorkHistoryService.selectRecruitResumeWorkHistoryById(id));
    }

    /**
     * 新增简历工作经历
     */
    @PreAuthorize("@ss.hasPermi('core:resumeWorkHistory:add')")
    @Log(title = "简历工作经历", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RecruitResumeWorkHistory recruitResumeWorkHistory)
    {
        return toAjax(recruitResumeWorkHistoryService.insertRecruitResumeWorkHistory(recruitResumeWorkHistory));
    }

    /**
     * 修改简历工作经历
     */
    @PreAuthorize("@ss.hasPermi('core:resumeWorkHistory:edit')")
    @Log(title = "简历工作经历", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RecruitResumeWorkHistory recruitResumeWorkHistory)
    {
        return toAjax(recruitResumeWorkHistoryService.updateRecruitResumeWorkHistory(recruitResumeWorkHistory));
    }

    /**
     * 删除简历工作经历
     */
    @PreAuthorize("@ss.hasPermi('core:resumeWorkHistory:remove')")
    @Log(title = "简历工作经历", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(recruitResumeWorkHistoryService.deleteRecruitResumeWorkHistoryByIds(ids));
    }
}
