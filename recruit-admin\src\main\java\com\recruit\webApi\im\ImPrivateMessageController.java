package com.recruit.webApi.im;

import com.recruit.common.core.controller.BaseController;
import com.recruit.common.core.domain.AjaxResult;
import com.recruit.common.core.domain.vo.FriendVO;
import com.recruit.common.core.domain.vo.PrivateMessageVO;
import com.recruit.common.core.page.TableDataInfo;
import com.recruit.common.utils.StringUtils;
import com.recruit.core.domain.RecruitEnterpriseReceivesResume;
import com.recruit.core.domain.RecruitPositionInfo;
import com.recruit.core.domain.RecruitUserInfo;
import com.recruit.core.domain.response.MobileWeChatResumeResponse;
import com.recruit.core.service.IRecruitEnterpriseReceivesResumeService;
import com.recruit.core.service.IRecruitPositionInfoService;
import com.recruit.core.service.IRecruitUserInfoService;
import com.recruit.core.service.ISysSensitiveService;
import com.recruit.core.service.IFriendService;
import com.recruit.core.service.IPrivateMessageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.Map;


/**
 * @Auther: Wu kong
 * @Date: 2023/4/7 14:53
 * @Description:
 */
@Api(tags= "IM私聊消息")
@RestController
@RequestMapping("/web/api/ImPrivate")
public class ImPrivateMessageController extends BaseController {

    @Autowired
    private IFriendService friendService;

    @Autowired
    private ISysSensitiveService sysSensitiveService;

    @Autowired
    private IPrivateMessageService privateMessageService;

    @Autowired
    private IRecruitUserInfoService recruitUserInfoService;

    @Autowired
    private IRecruitPositionInfoService recruitPositionInfoService;

    @Autowired
    private IRecruitEnterpriseReceivesResumeService recruitEnterpriseReceivesResumeService;


    @PostMapping("/send")
    @ApiOperation(value = "发送消息",notes="发送私聊消息")
    public AjaxResult sendMessage(@Valid @RequestBody PrivateMessageVO vo){

        Long userId = getUserId();

        RecruitUserInfo userInfo = recruitUserInfoService.selectRecruitUserInfoById(userId);
        if(userInfo.getType().equals("2")){
            RecruitPositionInfo positionInfo = recruitPositionInfoService.selectPositionInfoByUserId(userId);
            if(positionInfo == null){
                throw new RuntimeException("未发布职位，不允许与求职者沟通！");
            }
        }

        if(vo.getType() == 2){
            FriendVO friendVO = friendService.checkIfTheUserExists(userId, vo.getRecvId(), "2");
            if(friendVO != null) {
                RecruitEnterpriseReceivesResume receivesResume = new RecruitEnterpriseReceivesResume();
                receivesResume.setDselivererId(userId);
                receivesResume.setPositionInfoId(friendVO.getPositionInfoId());
                receivesResume.setEnterpriseId(friendVO.getEnterpriseId());
                receivesResume.setAttachmentResumeUrl(vo.getContent());
                recruitEnterpriseReceivesResumeService.insertRecruitEnterpriseReceivesResume(receivesResume);
            }
        }else if(vo.getType() == 0){
            //校验敏感字
            String content = sysSensitiveService.verifySensitiveWords(userInfo.getId(), userInfo.getUserName(), vo.getContent());
            vo.setContent(content);
        }
        return success(privateMessageService.sendMessage(vo));
    }

    @DeleteMapping("/recall/{id}")
    @ApiOperation(value = "撤回消息",notes="撤回私聊消息")
    public AjaxResult recallMessage(@NotNull(message = "消息id不能为空") @PathVariable Long id){
        privateMessageService.recallMessage(id);
        return success();
    }


    @GetMapping("/setReadMessage")
    @ApiOperation(value = "设置已读消息",notes="设置已读消息")
    public AjaxResult setReadMessage(@NotNull(message = "id不能为空") @RequestParam Long sendId){
        privateMessageService.setReadMessage(getUserId(), sendId);
        return success();
    }


    @GetMapping("/history")
    @ApiOperation(value = "查询聊天记录",notes="查询聊天记录")
    public TableDataInfo recallMessage(@NotNull(message = "好友id不能为空") @RequestParam Long friendId,
                                       @NotNull(message = "页码不能为空") @RequestParam Integer page,
                                       @NotNull(message = "size不能为空") @RequestParam Integer size){

        startPageTwo(page, size);
        return getDataTable(privateMessageService.findHistoryMessage(friendId));
    }


    @GetMapping("/verifyMobileWeChatResume")
    @ApiOperation(value = "校验是否发送手机号微信简历等信息，如果sendOrNot 为false时不能发送简历，手机号，微信等信息, 为true时则可以发送，如果未配置则提示去配置")
    public AjaxResult verifyMobileWeChatResume(@NotNull(message = "好友id不能为空") @RequestParam Long friendId, String types){
        MobileWeChatResumeResponse response = new MobileWeChatResumeResponse();
        RecruitUserInfo userInfo = recruitUserInfoService.selectRecruitUserInfoById(getUserId());
        String type = "0";
        if(StringUtils.equals(userInfo.getType(), "1")){
            type = "2";
        }else {
            type = "1";
        }
        FriendVO friendVO = friendService.checkIfTheUserExists(getUserId(), friendId, type);
        if(friendVO != null){
            if(StringUtils.equals(friendVO.getEstablishingLink(), "1")){
                response.setSendOrNot(true);
                response.setPhone(userInfo.getPhone());
                response.setWechatNum(userInfo.getWechatNum());
                Map<String, String> map = recruitUserInfoService.getAttachmentResume();
                response.setResumeName(map.get("resumeName"));
                response.setAttachmentResumeUrl(map.get("attachmentResumeUrl"));
            }else {
                if(!StringUtils.equals(type, "2")) {
                    response.setSendOrNot(false);
                }else{
                    Map<String, String> map = recruitUserInfoService.getAttachmentResume();
                    response.setResumeName(map.get("resumeName"));
                    response.setAttachmentResumeUrl(map.get("attachmentResumeUrl"));
                    response.setSendOrNot(true);
                }
            }
        }
        return success(response);
    }


    @GetMapping("/setExchange")
    @ApiOperation(value = "同意或拒绝接口",notes="同意或拒绝接口")
    public AjaxResult setExchange(@NotNull(message = "id不能为空") @RequestParam Long id, String exchangeStatus){
        privateMessageService.setExchange(id, exchangeStatus);
        return success();
    }

}
