package com.recruit.core.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.recruit.common.annotation.Excel;
import com.recruit.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 人才引进照片对象 recruit_talent_introduction_enroll_photos
 *
 * <AUTHOR>
 * @date 2024-03-15
 */
@Data
@ApiModel("人才引进照片")
public class RecruitTalentIntroductionEnrollPhotos extends BaseEntity
{
    private static final long serialVersionUID = 1L;


    private Long id;


    @ApiModelProperty("人才引进报名id")
    private Long talentIntroductionEnrollId;

    @ApiModelProperty("序号")
    private Integer index;

    @Excel(name = "序号", cellType = Excel.ColumnType.NUMERIC)
    @ApiModelProperty("序号")
    private String sequence;


    @Excel(name = "照片", cellType = Excel.ColumnType.IMAGE)
    @ApiModelProperty("照片地址")
    private String picUrl;


    @Excel(name = "照片类型", dictType = "pic_type")
    @ApiModelProperty("照片类型")
    private String picType;


    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "上传时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("上传时间")
    private Date uploadTime;


}
