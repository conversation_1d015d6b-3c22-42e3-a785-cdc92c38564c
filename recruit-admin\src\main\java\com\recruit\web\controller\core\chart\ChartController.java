package com.recruit.web.controller.core.chart;

import com.recruit.common.core.controller.BaseController;
import com.recruit.common.core.domain.AjaxResult;
import com.recruit.common.utils.DateUtils;
import com.recruit.common.utils.StringUtils;
import com.recruit.core.domain.Friend;
import com.recruit.core.domain.RecruitEnterpriseInfo;
import com.recruit.core.domain.RecruitUserInfo;
import com.recruit.core.domain.response.AreaNameResponse;
import com.recruit.core.domain.response.ChatCommunicationResponse;
import com.recruit.core.domain.response.IndustryCategoryResponse;
import com.recruit.core.domain.response.JobApplicantAreaResponse;
import com.recruit.core.domain.response.PageViewResponse;
import com.recruit.core.domain.response.StatisticalQuantityResponse;
import com.recruit.core.service.IFriendService;
import com.recruit.core.service.IRecruitEnterpriseInfoService;
import com.recruit.core.service.IRecruitPositionInfoService;
import com.recruit.core.service.IRecruitUserInfoService;
import com.recruit.core.service.ISysRegionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Auther: Wu kong
 * @Date: 2023/6/19 8:27
 * @Description:
 */
@RestController
@RequestMapping("/core/chart")
public class ChartController extends BaseController {

    @Autowired
    private IFriendService friendService;

    @Autowired
    private ISysRegionService sysRegionService;

    @Autowired
    private IRecruitUserInfoService recruitUserInfoService;

    @Autowired
    private IRecruitPositionInfoService recruitPositionInfoService;

    @Autowired
    private IRecruitEnterpriseInfoService recruitEnterpriseInfoService;


    /**
     * 获取统计数
     * @return
     */
    @GetMapping(value = "/getStatisticalQuantity")
    public AjaxResult getStatisticalQuantity(){
        StatisticalQuantityResponse response = new StatisticalQuantityResponse();
        //企业数
        response.setEnterprisesNum(recruitEnterpriseInfoService.getEnterprisesNum());
        //职位数
        response.setPositionNum(recruitPositionInfoService.getPositionNumTwo());
        //人才数
        response.setTalentNum(recruitUserInfoService.getTalentNum());
        //今日访问数
        response.setVisitsTodayNum(recruitUserInfoService.getVisitsTodayNum(DateUtils.getDate()));
        return success(response);
    }


    /**
     * 查询行业类别
     * @return
     */
    @GetMapping(value = "/getIndustryCategory")
    public AjaxResult getIndustryCategory(){
        List<RecruitEnterpriseInfo> lists = recruitEnterpriseInfoService.selectRecruitEnterpriseInfoList(new RecruitEnterpriseInfo());
        //判空分组
        Map<String, List<RecruitEnterpriseInfo>> map = lists.stream().filter(e->e.getIndustryType()!=null).collect(Collectors.groupingBy(RecruitEnterpriseInfo::getIndustryType));

        //行业类别响应实体
        IndustryCategoryResponse response = new IndustryCategoryResponse();
        //一产
        BigDecimal yichanNum = new BigDecimal(0);
        if(map.get("1") != null) {
            yichanNum = new BigDecimal(map.get("1").size());
        }
        //二产
        BigDecimal erchanNum = new BigDecimal(0);
        if(map.get("2") != null) {
            erchanNum = new BigDecimal(map.get("2").size());
        }
        //三产
        BigDecimal shanchanNum = new BigDecimal(0);
        if(map.get("3") != null) {
            shanchanNum = new BigDecimal(map.get("3").size());
        }
        BigDecimal otherNum = new BigDecimal(lists.size()).subtract(yichanNum).subtract(erchanNum).subtract(shanchanNum);
        //其它
        response.setOtherNum(otherNum);
        //一产
        response.setYichanNum(yichanNum);
        //二产
        response.setErchanNum(erchanNum);
        //三产
        response.setShanchanNum(shanchanNum);
        return success(response);
    }


    /**
     * 查询求职者区域
     * @return
     */
    @GetMapping(value = "/getJobApplicantArea")
    public AjaxResult getJobApplicantArea(){
        //查询求职者信息
        List<RecruitUserInfo> lists = recruitUserInfoService.getJobApplicantArea();
        lists.forEach(e->{
            if(e.getCity() != null && !StringUtils.equals(e.getCity(), "")){
                String[] ss = e.getCity().split(",");
                if(ss.length > 1){
                    e.setCity(sysRegionService.getMap(ss[1]));
                }else {
                    e.setCity("其它");
                }
            }else {
                e.setCity("其它");
            }
        });

        //判空分组
        Map<String, List<RecruitUserInfo>> map = lists.stream().filter(e->e.getCity()!=null).collect(Collectors.groupingBy(RecruitUserInfo::getCity));

        JobApplicantAreaResponse response = new JobApplicantAreaResponse();

        //区域名称
        List<String> areaNameList  = new ArrayList<>();
        //区域名称及数量
        List<AreaNameResponse> areaNameMap = new ArrayList<>();
        for(String key : map.keySet()){
            areaNameList.add(key);
            AreaNameResponse responses = new AreaNameResponse();
            responses.setName(key);
            responses.setValue(map.get(key).size());
            areaNameMap.add(responses);
        }
        //区域名称
        response.setAreaNameList(areaNameList);
        //区域名称及数量
        response.setAreaNameMap(areaNameMap);
        return success(response);
    }

    /**
     * 查询近七日聊天沟通
     * @return
     */
    @GetMapping(value = "/getChatCommunication")
    public AjaxResult getChatCommunication(){

        Friend friend = new Friend();
        //14
        Date date = DateUtils.getCurrentTimePlusDay(new Date(), -6);
        friend.setCreatedTime(date);
        List<Friend> lists = friendService.getChatCommunication(friend);
        lists.forEach(e->{
            e.setCreatedTimeTwo(DateUtils.getFormatDate(e.getCreatedTime(), DateUtils.MM_DD));
        });
        //判空分组
        Map<String, List<Friend>> map = lists.stream().filter(e->e.getChatType()!=null).collect(Collectors.groupingBy(Friend::getChatType));

        //类型 1：求职者 2：招聘者
        Map<String, List<Friend>> jobSeekersMap = new HashMap<>();
        Map<String, List<Friend>> recruiterMap = new HashMap<>();
        if(map.get("1") != null) {
            jobSeekersMap = map.get("1").stream().filter(e -> e.getCreatedTimeTwo() != null).collect(Collectors.groupingBy(Friend::getCreatedTimeTwo));
        }
        if(map.get("1") != null) {
            recruiterMap = map.get("2").stream().filter(e -> e.getCreatedTimeTwo() != null).collect(Collectors.groupingBy(Friend::getCreatedTimeTwo));
        }

        ChatCommunicationResponse response = new ChatCommunicationResponse();
        //求职数量
        List<Integer> jobSeekersList = new ArrayList<>();
        //招聘者数
        List<Integer> recruiterList = new ArrayList<>();
        //获取近一周时间
        List<String> sevenDateList = DateUtils.getSevenDate(7);
        Collections.reverse(sevenDateList);
        Map<String, List<Friend>> finalJobSeekersMap = jobSeekersMap;
        Map<String, List<Friend>> finalRecruiterMap = recruiterMap;
        sevenDateList.forEach(e->{
            if(finalJobSeekersMap.get(e) != null) {
                jobSeekersList.add(finalJobSeekersMap.get(e).size());
            }else {
                jobSeekersList.add(0);
            }
            if(finalRecruiterMap.get(e) != null) {
                recruiterList.add(finalRecruiterMap.get(e).size());
            }else {
                recruiterList.add(0);
            }
        });

        response.setDateLists(sevenDateList);
        response.setJobSeekersList(jobSeekersList);
        response.setRecruiterList(recruiterList);
        return success(response);
    }

    /**
     * 访问量
     * @return
     */
    @GetMapping(value = "/getPageView/{type}")
    public AjaxResult getPageView(@PathVariable("type")String type){


        RecruitUserInfo userInfo = new RecruitUserInfo();
        if(type.equals("2")){
            Date date = DateUtils.getCurrentTimePlusDay(new Date(), -6);
            userInfo.setLoginDate(date);
        }else if(type.equals("3")){
            Date date = DateUtils.dateTime(DateUtils.YYYY_MM_DD, DateUtils.getAroundTime(-1, new Date()));
            userInfo.setLoginDate(date);
        }else if(type.equals("1")){
            userInfo.setLoginDate(new Date());
        }
        //查询访问量
        List<RecruitUserInfo> lists = recruitUserInfoService.selectRecruitUserInfoListTwo(userInfo);
        if(!type.equals("1")) {
            lists.forEach(e -> {
                e.setLoginDateTwo(DateUtils.getFormatDate(e.getLoginDate(), DateUtils.MM_DD));
            });
        }else {
            lists.forEach(e -> {
                e.setLoginDateTwo(DateUtils.getFormatDate(e.getLoginDate(), DateUtils.HH));
            });
        }

        //根据时间分类
        Map<String, List<RecruitUserInfo>> map = lists.stream().filter(e->e.getLoginDateTwo()!=null).collect(Collectors.groupingBy(RecruitUserInfo::getLoginDateTwo));
        PageViewResponse response = new PageViewResponse();

        //日期数
        List<String> dateLists = new ArrayList<>();
        //访问数
        List<Integer> accessLists = new ArrayList<>();
        //获取近一周时间
        List<String> sevenDateList = new ArrayList<>();
        if(type.equals("2")) {
            sevenDateList = DateUtils.getSevenDate(7);
        }else if(type.equals("3")){
            //计算相差天数
            int differentDays = DateUtils.differentDaysByMillisecond(userInfo.getLoginDate(), new Date());
            sevenDateList = DateUtils.getSevenDate(differentDays);
        }else if(type.equals("1")){
            sevenDateList = DateUtils.getStatisticalHours();
        }
        Collections.reverse(sevenDateList);
        sevenDateList.forEach(e->{
            if(map.get(e) != null){
                accessLists.add(map.get(e).size());
            }else {
                accessLists.add(0);
            }
            if(type.equals("1")){
                dateLists.add(e+":00");
            }else {
                dateLists.add(e);
            }
        });
        response.setDateLists(dateLists);
        response.setAccessLists(accessLists);
        return success(response);
    }

}
