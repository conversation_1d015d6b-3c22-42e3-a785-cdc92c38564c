package com.recruit.webApi;

import com.recruit.common.core.controller.BaseController;
import com.recruit.common.core.domain.AjaxResult;
import com.recruit.common.core.page.TableDataInfo;
import com.recruit.common.utils.DictUtils;
import com.recruit.common.utils.StringUtils;
import com.recruit.common.utils.bean.BeanUtils;
import com.recruit.core.domain.RecruitEnterpriseReceivesResume;
import com.recruit.core.domain.RecruitPositionInfo;
import com.recruit.core.domain.request.other.DeliveryRecordRequest;
import com.recruit.core.service.IRecruitEnterpriseReceivesResumeService;
import com.recruit.core.service.IRecruitPositionInfoService;
import com.recruit.core.service.IRecruitPositionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Auther: Wu kong
 * @Date: 2023/4/11 22:25
 * @Description:
 */
@Api(tags= "(04-11)投递简历管理")
@RestController
@RequestMapping("/web/api/receivesResume")
public class ReceivesResumeController extends BaseController {

    @Autowired
    private IRecruitPositionService recruitPositionService;

    @Autowired
    private IRecruitPositionInfoService recruitPositionInfoService;

    @Autowired
    private IRecruitEnterpriseReceivesResumeService recruitEnterpriseReceivesResumeService;


    @ApiOperation("查看个人投递记录")
    @GetMapping("/getPersonalDeliveryRecords")
    public TableDataInfo getPersonalDeliveryRecords(@NotNull(message = "页码不能为空") @RequestParam Integer page,
                                                    @NotNull(message = "size不能为空") @RequestParam Integer size)
    {
        startPageTwo(page, size);
        RecruitPositionInfo positionInfo = new RecruitPositionInfo();
        positionInfo.setUserId(getUserId());
        List<RecruitPositionInfo> list = recruitPositionInfoService.getPersonalDeliveryRecords(positionInfo);
        list.forEach(e->{
            if(e.getPositionCode() != null && !StringUtils.equals(e.getPositionCode(), "")) {
                e.setPositionName(recruitPositionService.getMap(e.getPositionCode()));
            }
            if(e.getWorkExperience() != null && !StringUtils.equals(e.getWorkExperience(), "")){
                e.setWorkExperienceName(DictUtils.getDictLabel("work_experience", e.getWorkExperience()));
                if(e.getWorkExperienceName().equals("不限")){
                    e.setWorkExperienceName("经验不限");
                }
            }else {
                e.setWorkExperienceName("经验不限");
            }
            if(e.getMinimumEducation() != null && !StringUtils.equals(e.getMinimumEducation(), "")) {
                e.setMinimumEducationName(DictUtils.getDictLabel("background_type", e.getMinimumEducation()));
                if(e.getMinimumEducationName().equals("不限")){
                    e.setMinimumEducationName("学历不限");
                }
            }else {
                e.setMinimumEducationName("学历不限");
            }
        });
        return getDataTable(list);
    }



    @ApiOperation("查看个人投递数")
    @GetMapping("/getPersonalDeliveryNum")
    public AjaxResult getPersonalDeliveryNum()
    {
        RecruitPositionInfo positionInfo = new RecruitPositionInfo();
        positionInfo.setUserId(getUserId());
        return success(recruitPositionInfoService.getPersonalDeliveryNum(positionInfo));
    }


    @ApiOperation("查看企业收到简历记录")
    @GetMapping("/getEnterpriseReceivingRecords")
    public TableDataInfo getEnterpriseReceivingRecords(
                                                    @NotNull(message = "页码不能为空") @RequestParam Integer page,
                                                    @NotNull(message = "size不能为空") @RequestParam Integer size)
    {
        startPageTwo(page, size);
        List<RecruitEnterpriseReceivesResume> list = recruitEnterpriseReceivesResumeService.selectEnterpriseReceivesResumeListTwo(new RecruitEnterpriseReceivesResume(){{
            setEnterpriseId(getUserId());
        }});
        list.forEach(e->{
            //期望岗位名称
            if(e.getExpectedPosition() != null && !StringUtils.equals(e.getExpectedPosition(), "")) {
                e.setExpectedPositionName(recruitPositionService.getMap(e.getExpectedPosition()));
            }
            //工作经验名称
            if(e.getWorkExperience() != null && !StringUtils.equals(e.getWorkExperience(), "")){
                e.setWorkExperienceName(DictUtils.getDictLabel("work_experience_two", e.getWorkExperience()));
                if(e.getWorkExperienceName().equals("不限")){
                    e.setWorkExperienceName("经验不限");
                }
            }else {
                e.setWorkExperienceName("经验不限");
            }
            //最高学历
            if(e.getEducation() != null && !StringUtils.equals(e.getEducation(), "")) {
                e.setEducationName(DictUtils.getDictLabel("background_type", e.getEducation()));
                if(e.getEducationName().equals("不限")){
                    e.setEducationName("学历不限");
                }
            }else {
                e.setEducationName("学历不限");
            }
        });
        return getDataTable(list);
    }


    @ApiOperation("查看企业收到简历数")
    @GetMapping("/getEnterpriseReceivingNum")
    public AjaxResult getEnterpriseReceivingNum()
    {
        RecruitEnterpriseReceivesResume receivesResume = new RecruitEnterpriseReceivesResume();
        receivesResume.setEnterpriseId(getUserId());
        int sss = recruitEnterpriseReceivesResumeService.getEnterpriseReceivingNum(receivesResume);
        return success(sss);
    }

    @ApiOperation("添加投递记录")
    @PostMapping("/addDeliveryRecord")
    public AjaxResult addDeliveryRecord(@RequestBody DeliveryRecordRequest request)
    {
        RecruitEnterpriseReceivesResume receivesResume = new RecruitEnterpriseReceivesResume();
        BeanUtils.copyBeanProp(receivesResume, request);
        receivesResume.setDselivererId(getUserId());
        return toAjax(recruitEnterpriseReceivesResumeService.insertRecruitEnterpriseReceivesResume(receivesResume));
    }




}
