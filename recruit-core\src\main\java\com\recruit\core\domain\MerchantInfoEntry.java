package com.recruit.core.domain;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.recruit.common.annotation.Excel;
import com.recruit.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 服务申请入驻对象 service_apply_for_entry
 *
 * <AUTHOR>
 * @date 2023-05-27
 */
@Data
@ApiModel("服务申请入驻")
public class MerchantInfoEntry extends BaseEntity
{
    private static final long serialVersionUID = 1L;


    private Long id;


    @ApiModelProperty("服务行业编码")
    private String serviceTrade;


    @Excel(name = "服务行业名称", sort = 1)
    @ApiModelProperty("服务行业名称")
    private String serviceTradeName;


    @ApiModelProperty("服务行业编码")
    private List<String> serviceTrades;

    @Excel(name = "申请人姓名", sort = 3)
    @ApiModelProperty("申请人姓名")
    private String userName;

    @Excel(name = "商户名称", sort = 2)
    @ApiModelProperty("商户名称")
    private String serviceName;

    @ApiModelProperty("申请人姓名")
    private Long userId;


    @Excel(name = "联系手机号", sort = 4)
    @ApiModelProperty("联系手机号")
    private String phone;


    @ApiModelProperty("申请理由")
    private String applicationReason;


    @ApiModelProperty("申请状态")
    private String applicationStatus;


    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("申请时间")
    private Date applicationTime;


    @ApiModelProperty("审核人")
    private String auditBy;


    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("审核时间")
    private Date auditTime;

    @Excel(name = "身份证号", sort = 5)
    @ApiModelProperty("身份证号")
    private String idCard;


    @ApiModelProperty("申请资料")
    private String applicationMaterialsUrl;

    @ApiModelProperty("营业执照")
    private String businessLicenseUrl;

    @Excel(name = "详细地址", sort = 7)
    @ApiModelProperty("详细地址")
    private String address;


    @ApiModelProperty("经度")
    private String longitude;


    @ApiModelProperty("纬度")
    private String latitude;


    @ApiModelProperty("审核反馈")
    private String auditFeedback;


    @ApiModelProperty("所在地区")
    private String region;

    @ApiModelProperty("所在地区")
    private List<String> regions;


    @Excel(name = "所在地区", sort = 6, combo = {
            "东河区街道",
            "西河区街道",
            "新市区街道",
            "丽园区街道",
            "石油新城街道",
            "雅满苏镇",
            "七角井镇",
            "星星峡镇",
            "二堡镇",
            "五堡镇",
            "陶家宫镇",
            "沁城乡",
            "乌拉台哈萨克族乡",
            "双井子乡",
            "大泉湾乡",
            "回城乡",
            "花园乡",
            "南湖乡",
            "德外里都如克哈萨克族乡",
            "西山乡",
            "天山乡",
            "白石头乡",
            "柳树沟乡",
            "现代农业园区管理委员会",
            "兵团红星二场",
            "兵团火箭农场",
            "巴里坤镇",
            "博尔羌吉镇",
            "大河镇",
            "奎苏镇",
            "三塘湖镇",
            "萨尔乔克乡",
            "海子沿乡",
            "下涝坝乡",
            "石人子乡",
            "花园乡",
            "大红柳峡乡",
            "八墙子乡",
            "良种繁育场",
            "兵团红山农场",
            "伊吾镇",
            "淖毛湖镇",
            "盐池镇",
            "苇子峡乡",
            "下马崖乡",
            "吐葫芦乡",
            "前山哈萨克族乡"
    })
    @ApiModelProperty("所在地区名称")
    private String areaName;


    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("处罚截止时间")
    private Date penaltyTime;

    @ApiModelProperty("账户金额")
    private BigDecimal accountAmount;

    @ApiModelProperty("保证金")
    private BigDecimal earnestMoney;

    @ApiModelProperty("保证金状态")
    private String marginStatus;

    @ApiModelProperty("直线距离")
    private String linearDistance;

    @ApiModelProperty("身份证正面")
    private String idCardZ;

    @ApiModelProperty("身份证反面")
    private String idCardF;


}
