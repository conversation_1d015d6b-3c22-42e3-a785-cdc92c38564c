package com.recruit.core.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.recruit.common.annotation.Excel;
import com.recruit.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 简历工作经历对象 recruit_resume_work_history
 *
 * <AUTHOR>
 * @date 2023-03-21
 */
@Data
@ApiModel("简历工作经历")
public class RecruitResumeWorkHistory extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 用户id */
    @Excel(name = "用户id")
    @ApiModelProperty("用户id")
    private Long userId;

    /** 单位名称 */
    @Excel(name = "单位名称")
    @ApiModelProperty("单位名称")
    private String unitName;

    /** 担任职位 */
    @Excel(name = "担任职位")
    @ApiModelProperty("担任职位")
    private String position;

    @ApiModelProperty("担任职位名称")
    private String positionName;

    /** 入职时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "入职时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("入职时间")
    private Date entryTime;

    /** 离职时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "离职时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("离职时间")
    private Date leavedate;

    /** 至今 */
    @Excel(name = "至今")
    @ApiModelProperty("至今")
    private String upToNow;

    @ApiModelProperty("至今名称")
    private String upToNowName;

    /** 工作描述 */
    @Excel(name = "工作描述")
    @ApiModelProperty("工作描述")
    private String jobDescription;


}
