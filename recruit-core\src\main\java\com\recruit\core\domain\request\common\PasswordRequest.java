package com.recruit.core.domain.request.common;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Auther: <PERSON> kong
 * @Date: 2023/3/19 20:46
 * @Description:
 */
@Data
@ApiModel("设置修改密码1")
public class PasswordRequest {

    @ApiModelProperty("手机号")
    private String phone;

    @ApiModelProperty("旧密码")
    private String oldPassword;

    @ApiModelProperty("新密码")
    private String newPassword;

    @ApiModelProperty("确认密码")
    private String confirmPassword;
}
