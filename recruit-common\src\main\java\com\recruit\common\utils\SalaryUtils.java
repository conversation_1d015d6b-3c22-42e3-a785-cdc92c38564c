package com.recruit.common.utils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Auther: <PERSON> kong
 * @Date: 2023/3/20 15:09
 * @Description:
 */
public class SalaryUtils {

    /**
     * 获取薪资要求
     * @return
     */
    public static List<Map<String, List<String>>> getSalaryRequirements(){

        List<Map<String, List<String>>> listMap = new ArrayList<>();
        Map<String, List<String>> map1 = new HashMap<>();
        List<String> list1 = new ArrayList<>();
        list1.add("面议");
        map1.put("面议", list1);
        listMap.add(map1);

        Map<String, List<String>> map2 = new HashMap<>();
        List<String> list2 = new ArrayList<>();
        list2.add("2000");
        list2.add("3000");
        list2.add("4000");
        list2.add("5000");
        list2.add("6000");
        map2.put("1000", list2);
        listMap.add(map2);

        Map<String, List<String>> map3 = new HashMap<>();
        List<String> list3 = new ArrayList<>();
        list3.add("3000");
        list3.add("4000");
        list3.add("5000");
        list3.add("6000");
        list3.add("7000");
        map3.put("2000", list3);
        listMap.add(map3);

        Map<String, List<String>> map4 = new HashMap<>();
        List<String> list4 = new ArrayList<>();
        list4.add("4000");
        list4.add("5000");
        list4.add("6000");
        list4.add("7000");
        list4.add("8000");
        map4.put("3000", list4);
        listMap.add(map4);

        Map<String, List<String>> map5 = new HashMap<>();
        List<String> list5 = new ArrayList<>();
        list5.add("5000");
        list5.add("6000");
        list5.add("7000");
        list5.add("8000");
        list5.add("9000");
        map5.put("4000", list5);
        listMap.add(map5);

        Map<String, List<String>> map6 = new HashMap<>();
        List<String> list6 = new ArrayList<>();
        list6.add("6000");
        list6.add("7000");
        list6.add("8000");
        list6.add("9000");
        list6.add("10000");
        map6.put("5000", list6);
        listMap.add(map6);

        Map<String, List<String>> map7 = new HashMap<>();
        List<String> list7 = new ArrayList<>();
        list7.add("7000");
        list7.add("8000");
        list7.add("9000");
        list7.add("10000");
        list7.add("11000");
        map7.put("6000", list7);
        listMap.add(map7);

        Map<String, List<String>> map8 = new HashMap<>();
        List<String> list8 = new ArrayList<>();
        list8.add("8000");
        list8.add("9000");
        list8.add("10000");
        list8.add("11000");
        list8.add("12000");
        map8.put("7000", list8);
        listMap.add(map8);

        Map<String, List<String>> map9 = new HashMap<>();
        List<String> list9 = new ArrayList<>();
        list9.add("9000");
        list9.add("10000");
        list9.add("11000");
        list9.add("12000");
        list9.add("13000");
        map9.put("8000", list9);
        listMap.add(map9);

        Map<String, List<String>> map10 = new HashMap<>();
        List<String> list10 = new ArrayList<>();
        list10.add("10000");
        list10.add("11000");
        list10.add("12000");
        list10.add("13000");
        list10.add("14000");
        map10.put("9000", list10);
        listMap.add(map10);

        Map<String, List<String>> map11 = new HashMap<>();
        List<String> list11 = new ArrayList<>();
        list11.add("11000");
        list11.add("12000");
        list11.add("13000");
        list11.add("14000");
        list11.add("15000");
        map11.put("10000", list11);
        listMap.add(map11);

        Map<String, List<String>> map12 = new HashMap<>();
        List<String> list12 = new ArrayList<>();
        list12.add("12000");
        list12.add("13000");
        list12.add("14000");
        list12.add("15000");
        list12.add("16000");
        list12.add("17000");
        list12.add("18000");
        list12.add("19000");
        list12.add("20000");
        list12.add("21000");
        list12.add("22000");
        map12.put("11000", list12);
        listMap.add(map12);

        Map<String, List<String>> map13 = new HashMap<>();
        List<String> list13 = new ArrayList<>();
        list13.add("13000");
        list13.add("14000");
        list13.add("15000");
        list13.add("16000");
        list13.add("17000");
        list13.add("18000");
        list13.add("19000");
        list13.add("20000");
        list13.add("21000");
        list13.add("22000");
        list13.add("23000");
        list13.add("24000");
        map13.put("12000", list13);
        listMap.add(map13);

        Map<String, List<String>> map14 = new HashMap<>();
        List<String> list14 = new ArrayList<>();
        list14.add("14000");
        list14.add("15000");
        list14.add("16000");
        list14.add("17000");
        list14.add("18000");
        list14.add("19000");
        list14.add("20000");
        list14.add("21000");
        list14.add("22000");
        list14.add("23000");
        list14.add("24000");
        list14.add("25000");
        list14.add("26000");
        map14.put("13000", list14);
        listMap.add(map14);

        Map<String, List<String>> map15 = new HashMap<>();
        List<String> list15 = new ArrayList<>();
        list15.add("15000");
        list15.add("16000");
        list15.add("17000");
        list15.add("18000");
        list15.add("19000");
        list15.add("20000");
        list15.add("21000");
        list15.add("22000");
        list15.add("23000");
        list15.add("24000");
        list15.add("25000");
        list15.add("26000");
        list15.add("27000");
        list15.add("28000");
        map15.put("14000", list15);
        listMap.add(map15);

        Map<String, List<String>> map16 = new HashMap<>();
        List<String> list16 = new ArrayList<>();
        list16.add("16000");
        list16.add("17000");
        list16.add("18000");
        list16.add("19000");
        list16.add("20000");
        list16.add("21000");
        list16.add("22000");
        list16.add("23000");
        list16.add("24000");
        list16.add("25000");
        list16.add("26000");
        list16.add("27000");
        list16.add("28000");
        list16.add("29000");
        list16.add("30000");
        map16.put("15000", list16);
        listMap.add(map16);

        Map<String, List<String>> map17 = new HashMap<>();
        List<String> list17 = new ArrayList<>();
        list17.add("17000");
        list17.add("18000");
        list17.add("19000");
        list17.add("20000");
        list17.add("21000");
        list17.add("22000");
        list17.add("23000");
        list17.add("24000");
        list17.add("25000");
        list17.add("26000");
        list17.add("27000");
        list17.add("28000");
        list17.add("29000");
        list17.add("30000");
        map17.put("16000", list17);
        listMap.add(map17);

        Map<String, List<String>> map18 = new HashMap<>();
        List<String> list18 = new ArrayList<>();
        list18.add("18000");
        list18.add("19000");
        list18.add("20000");
        list18.add("21000");
        list18.add("22000");
        list18.add("23000");
        list18.add("24000");
        list18.add("25000");
        list18.add("26000");
        list18.add("27000");
        list18.add("28000");
        list18.add("29000");
        list18.add("30000");
        map18.put("17000", list18);
        listMap.add(map18);

        Map<String, List<String>> map19 = new HashMap<>();
        List<String> list19 = new ArrayList<>();
        list19.add("19000");
        list19.add("20000");
        list19.add("21000");
        list19.add("22000");
        list19.add("23000");
        list19.add("24000");
        list19.add("25000");
        list19.add("26000");
        list19.add("27000");
        list19.add("28000");
        list19.add("29000");
        list19.add("30000");
        list19.add("35000");
        map19.put("18000", list19);
        listMap.add(map19);

        Map<String, List<String>> map20 = new HashMap<>();
        List<String> list20 = new ArrayList<>();
        list20.add("20000");
        list20.add("21000");
        list20.add("22000");
        list20.add("23000");
        list20.add("24000");
        list20.add("25000");
        list20.add("26000");
        list20.add("27000");
        list20.add("28000");
        list20.add("29000");
        list20.add("30000");
        list20.add("35000");
        map20.put("19000", list20);
        listMap.add(map20);

        Map<String, List<String>> map21 = new HashMap<>();
        List<String> list21 = new ArrayList<>();
        list21.add("21000");
        list21.add("22000");
        list21.add("23000");
        list21.add("24000");
        list21.add("25000");
        list21.add("26000");
        list21.add("27000");
        list21.add("28000");
        list21.add("29000");
        list21.add("30000");
        list21.add("35000");
        list21.add("40000");
        map21.put("20000", list21);
        listMap.add(map21);

        Map<String, List<String>> map22 = new HashMap<>();
        List<String> list22 = new ArrayList<>();
        list22.add("22000");
        list22.add("23000");
        list22.add("24000");
        list22.add("25000");
        list22.add("26000");
        list22.add("27000");
        list22.add("28000");
        list22.add("29000");
        list22.add("30000");
        list22.add("35000");
        list22.add("40000");
        map22.put("21000", list22);
        listMap.add(map22);

        Map<String, List<String>> map23 = new HashMap<>();
        List<String> list23 = new ArrayList<>();
        list23.add("23000");
        list23.add("24000");
        list23.add("25000");
        list23.add("26000");
        list23.add("27000");
        list23.add("28000");
        list23.add("29000");
        list23.add("30000");
        list23.add("35000");
        list23.add("40000");
        map23.put("22000", list23);
        listMap.add(map23);

        Map<String, List<String>> map24 = new HashMap<>();
        List<String> list24 = new ArrayList<>();
        list24.add("24000");
        list24.add("25000");
        list24.add("26000");
        list24.add("27000");
        list24.add("28000");
        list24.add("29000");
        list24.add("30000");
        list24.add("35000");
        list24.add("40000");
        list24.add("45000");
        map24.put("23000", list24);
        listMap.add(map24);

        Map<String, List<String>> map25 = new HashMap<>();
        List<String> list25 = new ArrayList<>();
        list25.add("25000");
        list25.add("26000");
        list25.add("27000");
        list25.add("28000");
        list25.add("29000");
        list25.add("30000");
        list25.add("35000");
        list25.add("40000");
        list25.add("45000");
        map25.put("24000", list25);
        listMap.add(map25);

        Map<String, List<String>> map26 = new HashMap<>();
        List<String> list26 = new ArrayList<>();
        list26.add("26000");
        list26.add("27000");
        list26.add("28000");
        list26.add("29000");
        list26.add("30000");
        list26.add("35000");
        list26.add("40000");
        list26.add("45000");
        list26.add("50000");
        map26.put("25000", list26);
        listMap.add(map26);

        Map<String, List<String>> map27 = new HashMap<>();
        List<String> list27 = new ArrayList<>();
        list26.add("27000");
        list27.add("28000");
        list27.add("29000");
        list27.add("30000");
        list27.add("35000");
        list27.add("40000");
        list27.add("45000");
        list27.add("50000");
        map27.put("26000", list27);
        listMap.add(map27);

        Map<String, List<String>> map28 = new HashMap<>();
        List<String> list28 = new ArrayList<>();
        list28.add("28000");
        list28.add("29000");
        list28.add("30000");
        list28.add("35000");
        list28.add("40000");
        list28.add("45000");
        list28.add("50000");
        map28.put("27000", list28);
        listMap.add(map28);

        Map<String, List<String>> map29 = new HashMap<>();
        List<String> list29 = new ArrayList<>();
        list29.add("29000");
        list29.add("30000");
        list29.add("35000");
        list29.add("40000");
        list29.add("45000");
        list29.add("50000");
        list29.add("55000");
        map29.put("28000", list29);
        listMap.add(map29);

        Map<String, List<String>> map30 = new HashMap<>();
        List<String> list30 = new ArrayList<>();
        list30.add("30000");
        list30.add("35000");
        list30.add("40000");
        list30.add("45000");
        list30.add("50000");
        list30.add("55000");
        map30.put("29000", list30);
        listMap.add(map30);

        Map<String, List<String>> map31 = new HashMap<>();
        List<String> list31 = new ArrayList<>();
        list31.add("35000");
        list31.add("40000");
        list31.add("45000");
        list31.add("50000");
        list31.add("55000");
        list31.add("60000");
        map31.put("30000", list31);
        listMap.add(map31);

        Map<String, List<String>> map32 = new HashMap<>();
        List<String> list32 = new ArrayList<>();
        list32.add("40000");
        list32.add("45000");
        list32.add("50000");
        list32.add("55000");
        list32.add("60000");
        list32.add("65000");
        map32.put("35000", list32);
        listMap.add(map32);

        Map<String, List<String>> map33 = new HashMap<>();
        List<String> list33 = new ArrayList<>();
        list33.add("45000");
        list33.add("50000");
        list33.add("55000");
        list33.add("60000");
        list33.add("65000");
        list33.add("70000");
        map33.put("40000", list33);
        listMap.add(map33);

        Map<String, List<String>> map34 = new HashMap<>();
        List<String> list34 = new ArrayList<>();
        list34.add("50000");
        list34.add("55000");
        list34.add("60000");
        list34.add("65000");
        list34.add("70000");
        list34.add("75000");
        map34.put("45000", list34);
        listMap.add(map34);

        Map<String, List<String>> map35 = new HashMap<>();
        List<String> list35 = new ArrayList<>();
        list35.add("55000");
        list35.add("60000");
        list35.add("65000");
        list35.add("70000");
        list35.add("75000");
        list35.add("80000");
        map35.put("50000", list35);
        listMap.add(map35);

        Map<String, List<String>> map36 = new HashMap<>();
        List<String> list36 = new ArrayList<>();
        list36.add("60000");
        list36.add("65000");
        list36.add("70000");
        list36.add("75000");
        list36.add("80000");
        list36.add("85000");
        map36.put("55000", list36);
        listMap.add(map36);

        Map<String, List<String>> map37 = new HashMap<>();
        List<String> list37 = new ArrayList<>();
        list37.add("65000");
        list37.add("70000");
        list37.add("75000");
        list37.add("80000");
        list37.add("85000");
        list37.add("90000");
        map37.put("60000", list37);
        listMap.add(map37);

        Map<String, List<String>> map38 = new HashMap<>();
        List<String> list38 = new ArrayList<>();
        list38.add("70000");
        list38.add("75000");
        list38.add("80000");
        list38.add("85000");
        list38.add("90000");
        list38.add("95000");
        map38.put("65000", list38);
        listMap.add(map38);

        Map<String, List<String>> map39 = new HashMap<>();
        List<String> list39 = new ArrayList<>();
        list39.add("75000");
        list39.add("80000");
        list39.add("85000");
        list39.add("90000");
        list39.add("95000");
        list39.add("100000");
        map39.put("70000", list39);
        listMap.add(map39);

        Map<String, List<String>> map40 = new HashMap<>();
        List<String> list40 = new ArrayList<>();
        list40.add("80000");
        list40.add("85000");
        list40.add("90000");
        list40.add("95000");
        list40.add("100000");
        map40.put("75000", list40);
        listMap.add(map40);

        Map<String, List<String>> map41 = new HashMap<>();
        List<String> list41 = new ArrayList<>();
        list41.add("85000");
        list41.add("90000");
        list41.add("95000");
        list41.add("100000");
        list41.add("110000");
        map41.put("80000", list41);
        listMap.add(map41);

        Map<String, List<String>> map42 = new HashMap<>();
        List<String> list42 = new ArrayList<>();
        list42.add("90000");
        list42.add("95000");
        list42.add("100000");
        list42.add("110000");
        map42.put("85000", list42);
        listMap.add(map42);

        Map<String, List<String>> map43 = new HashMap<>();
        List<String> list43 = new ArrayList<>();
        list43.add("95000");
        list43.add("100000");
        list43.add("110000");
        list43.add("120000");
        map43.put("90000", list43);
        listMap.add(map43);

        Map<String, List<String>> map44 = new HashMap<>();
        List<String> list44 = new ArrayList<>();
        list44.add("100000");
        list44.add("110000");
        list44.add("120000");
        map44.put("95000", list44);
        listMap.add(map44);

        Map<String, List<String>> map45 = new HashMap<>();
        List<String> list45 = new ArrayList<>();
        list45.add("110000");
        list45.add("120000");
        list45.add("130000");
        list45.add("140000");
        list45.add("150000");
        list45.add("160000");
        list45.add("170000");
        list45.add("180000");
        list45.add("190000");
        list45.add("200000");
        map45.put("100000", list45);
        listMap.add(map45);

        Map<String, List<String>> map46 = new HashMap<>();
        List<String> list46 = new ArrayList<>();
        list46.add("120000");
        list46.add("130000");
        list46.add("140000");
        list46.add("150000");
        list46.add("160000");
        list46.add("170000");
        list46.add("180000");
        list46.add("190000");
        list46.add("200000");
        list46.add("210000");
        list46.add("220000");
        map46.put("110000", list46);
        listMap.add(map46);

        Map<String, List<String>> map47 = new HashMap<>();
        List<String> list47 = new ArrayList<>();
        list47.add("130000");
        list47.add("140000");
        list47.add("150000");
        list47.add("160000");
        list47.add("170000");
        list47.add("180000");
        list47.add("190000");
        list47.add("200000");
        list47.add("210000");
        list47.add("220000");
        list47.add("230000");
        list47.add("240000");
        map47.put("120000", list47);
        listMap.add(map47);

        Map<String, List<String>> map48 = new HashMap<>();
        List<String> list48 = new ArrayList<>();
        list48.add("140000");
        list48.add("150000");
        list48.add("160000");
        list48.add("170000");
        list48.add("180000");
        list48.add("190000");
        list48.add("200000");
        list48.add("210000");
        list48.add("220000");
        list48.add("230000");
        list48.add("240000");
        list48.add("250000");
        list48.add("260000");
        map48.put("130000", list48);
        listMap.add(map48);

        Map<String, List<String>> map49 = new HashMap<>();
        List<String> list49 = new ArrayList<>();
        list49.add("150000");
        list49.add("160000");
        list49.add("170000");
        list49.add("180000");
        list49.add("190000");
        list49.add("200000");
        list49.add("210000");
        list49.add("220000");
        list49.add("230000");
        list49.add("240000");
        list49.add("250000");
        list49.add("260000");
        map49.put("140000", list49);
        listMap.add(map49);

        Map<String, List<String>> map50 = new HashMap<>();
        List<String> list50 = new ArrayList<>();
        list50.add("160000");
        list50.add("170000");
        list50.add("180000");
        list50.add("190000");
        list50.add("200000");
        list50.add("210000");
        list50.add("220000");
        list50.add("230000");
        list50.add("240000");
        list50.add("250000");
        list50.add("260000");
        map50.put("150000", list50);
        listMap.add(map50);

        Map<String, List<String>> map51 = new HashMap<>();
        List<String> list51 = new ArrayList<>();
        list51.add("170000");
        list51.add("180000");
        list51.add("190000");
        list51.add("200000");
        list51.add("210000");
        list51.add("220000");
        list51.add("230000");
        list51.add("240000");
        list51.add("250000");
        list51.add("260000");
        map51.put("160000", list51);
        listMap.add(map51);

        Map<String, List<String>> map52 = new HashMap<>();
        List<String> list52 = new ArrayList<>();
        list52.add("180000");
        list52.add("190000");
        list52.add("200000");
        list52.add("210000");
        list52.add("220000");
        list52.add("230000");
        list52.add("240000");
        list52.add("250000");
        list52.add("260000");
        map52.put("170000", list52);
        listMap.add(map52);

        Map<String, List<String>> map53 = new HashMap<>();
        List<String> list53 = new ArrayList<>();
        list53.add("190000");
        list53.add("200000");
        list53.add("210000");
        list53.add("220000");
        list53.add("230000");
        list53.add("240000");
        list53.add("250000");
        list53.add("260000");
        map53.put("180000", list53);
        listMap.add(map53);

        Map<String, List<String>> map54 = new HashMap<>();
        List<String> list54 = new ArrayList<>();
        list54.add("200000");
        list54.add("210000");
        list54.add("220000");
        list54.add("230000");
        list54.add("240000");
        list54.add("250000");
        list54.add("260000");
        map54.put("190000", list54);
        listMap.add(map54);

        Map<String, List<String>> map55 = new HashMap<>();
        List<String> list55 = new ArrayList<>();
        list55.add("210000");
        list55.add("220000");
        list55.add("230000");
        list55.add("240000");
        list55.add("250000");
        list55.add("260000");
        map55.put("200000", list55);
        listMap.add(map55);

        Map<String, List<String>> map56 = new HashMap<>();
        List<String> list56 = new ArrayList<>();
        list56.add("220000");
        list56.add("230000");
        list56.add("240000");
        list56.add("250000");
        list56.add("260000");
        map56.put("210000", list56);
        listMap.add(map56);

        Map<String, List<String>> map57 = new HashMap<>();
        List<String> list57 = new ArrayList<>();
        list57.add("230000");
        list57.add("240000");
        list57.add("250000");
        list57.add("260000");
        map57.put("220000", list57);
        listMap.add(map57);

        Map<String, List<String>> map58 = new HashMap<>();
        List<String> list58 = new ArrayList<>();
        list58.add("240000");
        list58.add("250000");
        list58.add("260000");
        map58.put("230000", list58);
        listMap.add(map58);

        Map<String, List<String>> map59 = new HashMap<>();
        List<String> list59 = new ArrayList<>();
        list59.add("250000");
        list59.add("260000");
        map59.put("240000", list59);
        listMap.add(map59);

        Map<String, List<String>> map60 = new HashMap<>();
        List<String> list60 = new ArrayList<>();
        list60.add("260000");
        map60.put("250000", list60);
        listMap.add(map60);



        return listMap;
    }
}
