package com.recruit.core.domain.request.other;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @Auther: <PERSON> kong
 * @Date: 2023/4/12 11:48
 * @Description:
 */
@Data
@ApiModel("浏览足迹1")
public class BrowsePositionRequest {

    @NotNull(message = "职位id不能为空")
    @ApiModelProperty("职位id")
    private Long positionInfoId;

    @NotNull(message = "企业id不能为空")
    @ApiModelProperty("企业id")
    private Long enterpriseId;

    @NotNull(message = "发布人id不能为空")
    @ApiModelProperty("发布人id")
    private Long publisherId;
}
