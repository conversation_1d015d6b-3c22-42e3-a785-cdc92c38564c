package com.recruit.core.domain.request.other;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * @Auther: <PERSON> kong
 * @Date: 2023/4/23 18:47
 * @Description:
 */
@Data
public class InterviewInvitationRequest {

    @NotNull(message = "接收用户id不能为空")
    @ApiModelProperty(value = "接收用户id")
    private Long recvId;

    @NotNull(message = "职位id不能为空")
    @ApiModelProperty("职位id")
    private Long positionInfoId;

    @NotNull(message = "面试时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    @ApiModelProperty("面试时间")
    private Date interviewTime;

    @NotBlank(message = "联系人姓名不能为空")
    @ApiModelProperty("联系人姓名")
    private String contactsName;

    @NotBlank(message = "联系人电话 不能为空")
    @ApiModelProperty("联系人电话")
    private String contactsPhone;

    @NotBlank(message = "面试地点不能为空")
    @ApiModelProperty("面试地点")
    private String interviewLocation;

    @NotBlank(message = "门牌号不能为空")
    @ApiModelProperty("门牌号")
    private String houseNumber;

    @ApiModelProperty("企业地图经度")
    private String mapLongitude;

    @ApiModelProperty("企业地图纬度")
    private String mapLatitude;

    @ApiModelProperty("备注")
    private String remark;
}
