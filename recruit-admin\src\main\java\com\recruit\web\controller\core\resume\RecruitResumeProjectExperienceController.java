package com.recruit.web.controller.core.resume;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.recruit.common.annotation.Log;
import com.recruit.common.core.controller.BaseController;
import com.recruit.common.core.domain.AjaxResult;
import com.recruit.common.enums.BusinessType;
import com.recruit.core.domain.RecruitResumeProjectExperience;
import com.recruit.core.service.IRecruitResumeProjectExperienceService;
import com.recruit.common.utils.poi.ExcelUtil;
import com.recruit.common.core.page.TableDataInfo;

/**
 * 简历项目经历Controller
 *
 * <AUTHOR>
 * @date 2023-03-19
 */
@RestController
@RequestMapping("/core/resumeProjectExperience")
public class RecruitResumeProjectExperienceController extends BaseController
{
    @Autowired
    private IRecruitResumeProjectExperienceService recruitResumeProjectExperienceService;

    /**
     * 查询简历项目经历列表
     */
    @PreAuthorize("@ss.hasPermi('core:resumeProjectExperience:list')")
    @GetMapping("/list")
    public TableDataInfo list(RecruitResumeProjectExperience recruitResumeProjectExperience)
    {
        startPage();
        List<RecruitResumeProjectExperience> list = recruitResumeProjectExperienceService.selectRecruitResumeProjectExperienceList(recruitResumeProjectExperience);
        return getDataTable(list);
    }

    /**
     * 导出简历项目经历列表
     */
    @PreAuthorize("@ss.hasPermi('core:resumeProjectExperience:export')")
    @Log(title = "简历项目经历", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RecruitResumeProjectExperience recruitResumeProjectExperience)
    {
        List<RecruitResumeProjectExperience> list = recruitResumeProjectExperienceService.selectRecruitResumeProjectExperienceList(recruitResumeProjectExperience);
        ExcelUtil<RecruitResumeProjectExperience> util = new ExcelUtil<RecruitResumeProjectExperience>(RecruitResumeProjectExperience.class);
        util.exportExcel(response, list, "简历项目经历数据");
    }

    /**
     * 获取简历项目经历详细信息
     */
    @PreAuthorize("@ss.hasPermi('core:resumeProjectExperience:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(recruitResumeProjectExperienceService.selectRecruitResumeProjectExperienceById(id));
    }

    /**
     * 新增简历项目经历
     */
    @PreAuthorize("@ss.hasPermi('core:resumeProjectExperience:add')")
    @Log(title = "简历项目经历", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RecruitResumeProjectExperience recruitResumeProjectExperience)
    {
        return toAjax(recruitResumeProjectExperienceService.insertRecruitResumeProjectExperience(recruitResumeProjectExperience));
    }

    /**
     * 修改简历项目经历
     */
    @PreAuthorize("@ss.hasPermi('core:resumeProjectExperience:edit')")
    @Log(title = "简历项目经历", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RecruitResumeProjectExperience recruitResumeProjectExperience)
    {
        return toAjax(recruitResumeProjectExperienceService.updateRecruitResumeProjectExperience(recruitResumeProjectExperience));
    }

    /**
     * 删除简历项目经历
     */
    @PreAuthorize("@ss.hasPermi('core:resumeProjectExperience:remove')")
    @Log(title = "简历项目经历", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(recruitResumeProjectExperienceService.deleteRecruitResumeProjectExperienceByIds(ids));
    }
}
