package com.recruit.core.domain;

import com.recruit.common.annotation.Excel;
import com.recruit.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 系统敏感词对象 sys_sensitive
 *
 * <AUTHOR>
 * @date 2023-05-06
 */
@Data
@ApiModel("系统敏感词")
public class SysSensitive extends BaseEntity
{
    private static final long serialVersionUID = 1L;


    private Long id;


    @Excel(name = "敏感词")
    @ApiModelProperty("敏感词")
    private String sensitives;


}
