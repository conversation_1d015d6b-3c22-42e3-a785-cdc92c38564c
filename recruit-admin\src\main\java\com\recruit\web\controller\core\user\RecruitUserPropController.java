package com.recruit.web.controller.core.user;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.recruit.common.annotation.Log;
import com.recruit.common.core.controller.BaseController;
import com.recruit.common.core.domain.AjaxResult;
import com.recruit.common.enums.BusinessType;
import org.springframework.web.multipart.MultipartFile;
import com.recruit.core.domain.RecruitUserProp;
import com.recruit.core.service.IRecruitUserPropService;
import com.recruit.common.utils.poi.ExcelUtil;
import com.recruit.common.core.page.TableDataInfo;

/**
 * 用户道具Controller
 *
 * <AUTHOR>
 * @date 2023-05-23
 */
@RestController
@RequestMapping("/core/userProp")
public class RecruitUserPropController extends BaseController
{
    @Autowired
    private IRecruitUserPropService recruitUserPropService;

    /**
     * 查询用户道具列表
     */
    @PreAuthorize("@ss.hasPermi('core:userProp:list')")
    @GetMapping("/list")
    public TableDataInfo list(RecruitUserProp recruitUserProp)
    {
        startPage();
        List<RecruitUserProp> list = recruitUserPropService.selectRecruitUserPropList(recruitUserProp);
        return getDataTable(list);
    }

    /**
     * 导出用户道具列表
     */
    @PreAuthorize("@ss.hasPermi('core:userProp:export')")
    @Log(title = "用户道具", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RecruitUserProp recruitUserProp)
    {
        List<RecruitUserProp> list = recruitUserPropService.selectRecruitUserPropList(recruitUserProp);
        ExcelUtil<RecruitUserProp> util = new ExcelUtil<RecruitUserProp>(RecruitUserProp.class);
        util.exportExcel(response, list, "用户道具数据");
    }

    /**
     * 获取用户道具详细信息
     */
    @PreAuthorize("@ss.hasPermi('core:userProp:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(recruitUserPropService.selectRecruitUserPropById(id));
    }

    /**
     * 新增用户道具
     */
    @PreAuthorize("@ss.hasPermi('core:userProp:add')")
    @Log(title = "用户道具", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RecruitUserProp recruitUserProp)
    {
        return toAjax(recruitUserPropService.insertRecruitUserProp(recruitUserProp));
    }

    /**
     * 修改用户道具
     */
    @PreAuthorize("@ss.hasPermi('core:userProp:edit')")
    @Log(title = "用户道具", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RecruitUserProp recruitUserProp)
    {
        return toAjax(recruitUserPropService.updateRecruitUserProp(recruitUserProp));
    }

    /**
     * 删除用户道具
     */
    @PreAuthorize("@ss.hasPermi('core:userProp:remove')")
    @Log(title = "用户道具", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(recruitUserPropService.deleteRecruitUserPropByIds(ids));
    }


    /**
     * 导入用户道具
     * @param file
     * @param updateSupport
     * @return
     * @throws Exception
     */
    @PreAuthorize("@ss.hasPermi('core:userProp:import')")
    @Log(title = "用户道具", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<RecruitUserProp> util = new ExcelUtil<>(RecruitUserProp.class);
        List<RecruitUserProp> lists = util.importExcel(file.getInputStream());
        String message = recruitUserPropService.importRecruitUserProp(lists, updateSupport);
        return AjaxResult.success(message);
    }


}
