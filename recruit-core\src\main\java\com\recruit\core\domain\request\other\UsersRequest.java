package com.recruit.core.domain.request.other;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @Auther: <PERSON> kong
 * @Date: 2023/4/12 15:41
 * @Description:
 */
@Data
@ApiModel("收藏用户")
public class UsersRequest {

    @NotNull(message = "用户id不能为空")
    @ApiModelProperty("用户id")
    private Long userId;
}
