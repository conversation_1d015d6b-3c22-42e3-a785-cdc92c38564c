package com.recruit.core.domain.request.resume;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * @Auther: <PERSON> kong
 * @Date: 2023/3/20 17:16
 * @Description:
 */
@Data
@ApiModel("求职意愿1")
public class ResumeJobDesireRequest {

    //@NotBlank(message = "期望岗位不能为空")
    @ApiModelProperty("期望岗位")
    private String expectedPosition;

    //@NotBlank(message = "工作职能不能为空")
    @ApiModelProperty("工作职能")
    private String workFunction;

    @NotBlank(message = "从事行业不能为空")
    @ApiModelProperty("从事行业")
    private String engagedInIndustry;


    @ApiModelProperty("期望城市")
    private String expectedCity;

    @ApiModelProperty("期望城市")
    private List<String> expectedCitys;

    @ApiModelProperty("最高薪资")
    private String maximumSalary;

    @ApiModelProperty("最低薪资")
    private String minimumWage;

    @ApiModelProperty("到岗时间")
    private String timeOfArrival;


    @NotBlank(message = "求职状态不能为空")
    @ApiModelProperty("求职状态")
    private String jobStatus;
}
