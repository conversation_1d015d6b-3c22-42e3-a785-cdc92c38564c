package com.recruit.common.core.domain.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("好友信息VO")
public class FriendVO {

    @ApiModelProperty(value = "好友id")
    private Long id;

    @ApiModelProperty(value = "好友昵称")
    private String nickName;


    @ApiModelProperty(value = "好友头像")
    private String headImage;


    /**
     * 职位表id
     */
    @ApiModelProperty(value = "职位表id")
    private Long positionInfoId;

    @ApiModelProperty(value = "职位表")
    private String positionInfoName;
    /**
     * 企业id
     */
    @ApiModelProperty(value = "企业id")
    private Long enterpriseId;

    /**
     * 最新消息
     */
    @ApiModelProperty(value = "最新消息")
    private String latestNews;
    /**
     * 最新消息时间
     */
    @ApiModelProperty(value = "最新消息时间")
    private String latestNewsTime;

    /**
     * 未读数量
     */
    @ApiModelProperty(value = "未读数量")
    private int obtainUnreadNum;

    /**
     * 消息类型 0:文字 1:图片 2:文件 3:语音  10:撤回消息
     */
    @ApiModelProperty(value = "消息类型 0:文字 1:图片 2:文件 3:语音  10:撤回消息")
    private Integer type;

    @ApiModelProperty(value = "建立链接0否，1是")
    private String establishingLink;

    @ApiModelProperty(value = "公司名称")
    private String enterpriseName;

    @ApiModelProperty(value = "职位")
    private String position;

    @ApiModelProperty(value = "期望岗位")
    private String expectedPositionName;
}
