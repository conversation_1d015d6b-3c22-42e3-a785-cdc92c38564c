package com.recruit.web.controller.core.pay;

import java.util.List;
import java.util.Objects;
import javax.servlet.http.HttpServletResponse;

import com.recruit.common.constant.HttpStatus;
import com.recruit.common.exception.ServiceException;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.recruit.common.annotation.Log;
import com.recruit.common.core.controller.BaseController;
import com.recruit.common.core.domain.AjaxResult;
import com.recruit.common.enums.BusinessType;
import org.springframework.web.multipart.MultipartFile;
import com.recruit.core.domain.SysPayChannel;
import com.recruit.core.service.ISysPayChannelService;
import com.recruit.common.utils.poi.ExcelUtil;
import com.recruit.common.core.page.TableDataInfo;

/**
 * 系统支付账户设置Controller
 *
 * <AUTHOR>
 * @date 2023-05-21
 */
@RestController
@RequestMapping("/core/payChannel")
public class SysPayChannelController extends BaseController
{
    @Autowired
    private ISysPayChannelService sysPayChannelService;

    /**
     * 查询系统支付账户设置列表
     */
    @PreAuthorize("@ss.hasPermi('core:payChannel:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysPayChannel sysPayChannel)
    {
        startPage();
        List<SysPayChannel> list = sysPayChannelService.selectSysPayChannelList(sysPayChannel);
        return getDataTable(list);
    }

    /**
     * 导出系统支付账户设置列表
     */
    @PreAuthorize("@ss.hasPermi('core:payChannel:export')")
    @Log(title = "系统支付账户设置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysPayChannel sysPayChannel)
    {
        List<SysPayChannel> list = sysPayChannelService.selectSysPayChannelList(sysPayChannel);
        ExcelUtil<SysPayChannel> util = new ExcelUtil<SysPayChannel>(SysPayChannel.class);
        util.exportExcel(response, list, "系统支付账户设置数据");
    }

    /**
     * 获取系统支付账户设置详细信息
     */
    @PreAuthorize("@ss.hasPermi('core:payChannel:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(sysPayChannelService.selectSysPayChannelById(id));
    }

    /**
     * 新增系统支付账户设置
     */
    @PreAuthorize("@ss.hasPermi('core:payChannel:add')")
    @Log(title = "系统支付账户设置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SysPayChannel sysPayChannel)
    {
        List<SysPayChannel> lists = sysPayChannelService.selectSysPayChannelList(new SysPayChannel(){{
            setPaymentChannel(sysPayChannel.getPaymentChannel());
        }});
        if(lists.size() > 0){
            throw new ServiceException("该支付渠道已存在不可重复添加！", HttpStatus.BAD_REQUEST);
        }
        return toAjax(sysPayChannelService.insertSysPayChannel(sysPayChannel));
    }

    /**
     * 修改系统支付账户设置
     */
    @PreAuthorize("@ss.hasPermi('core:payChannel:edit')")
    @Log(title = "系统支付账户设置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SysPayChannel sysPayChannel)
    {
        List<SysPayChannel> lists = sysPayChannelService.selectSysPayChannelList(new SysPayChannel(){{
            setPaymentChannel(sysPayChannel.getPaymentChannel());
        }});
        if(lists.size() > 0){
            if(!Objects.equals(lists.get(0).getId(), sysPayChannel.getId())){
                throw new ServiceException("该支付渠道已存在不可重复添加！", HttpStatus.BAD_REQUEST);
            }
        }
        return toAjax(sysPayChannelService.updateSysPayChannel(sysPayChannel));
    }

    /**
     * 删除系统支付账户设置
     */
    @PreAuthorize("@ss.hasPermi('core:payChannel:remove')")
    @Log(title = "系统支付账户设置", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(sysPayChannelService.deleteSysPayChannelByIds(ids));
    }


    /**
     * 导入系统支付账户设置
     * @param file
     * @param updateSupport
     * @return
     * @throws Exception
     */
    @PreAuthorize("@ss.hasPermi('core:payChannel:import')")
    @Log(title = "系统支付账户设置", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<SysPayChannel> util = new ExcelUtil<>(SysPayChannel.class);
        List<SysPayChannel> lists = util.importExcel(file.getInputStream());
        String message = sysPayChannelService.importSysPayChannel(lists, updateSupport);
        return AjaxResult.success(message);
    }


}
