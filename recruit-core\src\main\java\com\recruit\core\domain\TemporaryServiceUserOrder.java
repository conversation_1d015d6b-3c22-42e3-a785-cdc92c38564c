package com.recruit.core.domain;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.recruit.common.annotation.Excel;
import com.recruit.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 用户订单对象 temporary_service_user_order
 *
 * <AUTHOR>
 * @date 2023-06-02
 */
@Data
@ApiModel("用户订单")
public class TemporaryServiceUserOrder extends BaseEntity
{
    private static final long serialVersionUID = 1L;


    private Long id;

    @ApiModelProperty("下单人")
    private Long placeOrderUserId;

    @Excel(name = "下单人", sort = 1)
    @ApiModelProperty("下单人")
    private String placeOrderUserName;

    @Excel(name = "联系人", sort = 2)
    @ApiModelProperty("联系人")
    private String liaisonNan;

    @Excel(name = "下单人手机号", sort = 3)
    @ApiModelProperty("下单人手机号")
    private String placeOrderPhone;


    @ApiModelProperty("服务编码")
    private String serviceTrade;

    @ApiModelProperty("商品id")
    private Long serviceGoodsId;


    @Excel(name = "服务类别", sort = 4)
    @ApiModelProperty("服务类别")
    private String serviceGoodsName;

    @ApiModelProperty("商品价格id")
    private Long serviceGoodsPriceId;

    @Excel(name = "服务名称", sort = 5)
    @ApiModelProperty("服务名称")
    private String serviceGoodsPriceName;


    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "预约日期", width = 30, dateFormat = "yyyy-MM-dd", sort = 6)
    @ApiModelProperty("预约日期")
    private Date arrivalTime;

    @Excel(name = "上门时间", dictType = "appointment_time", sort = 7)
    @ApiModelProperty("到门时间，数据字典")
    private String toDoorTime;


    @Excel(name = "下单地址", sort = 8)
    @ApiModelProperty("下单地址")
    private String address;


    @Excel(name = "门牌号", sort = 9)
    @ApiModelProperty("门牌号")
    private String houseNumber;

    @ApiModelProperty("经度")
    private String longitude;

    @ApiModelProperty("纬度")
    private String latitude;

    @Excel(name = "订单价格", sort = 10)
    @ApiModelProperty("订单价格")
    private BigDecimal orderPrice;


    @Excel(name = "实际价格", sort = 11)
    @ApiModelProperty("实际价格")
    private BigDecimal realPrice;


    @Excel(name = "订单状态", dictType = "service_order_status", sort = 12)
    @ApiModelProperty("订单状态 0预约，1下单，2预约确认，3待支付，4待服务，5订单完成，6取消订单，7退款")
    private String orderStatus;


    @ApiModelProperty("订单信息")
    private String orderMsg;


    @ApiModelProperty("回调内容")
    private String callbackContent;

    @ApiModelProperty("评论状态")
    private String commentStatus;

    @ApiModelProperty("派单状态")
    private String dispatchStatus;


    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("回调时间")
    private Date callbackTime;


    @Excel(name = "支付状态", dictType = "pay_status", sort = 13)
    @ApiModelProperty("支付状态 0未支付，1支付中，2支付成功，3支付失败")
    private String payStatus;


    @Excel(name = "支付渠道", dictType = "payment_channel", sort = 14)
    @ApiModelProperty("支付渠道 1微信，2支付宝")
    private String payChannel;

    @ApiModelProperty("订单id")
    private String bizPayNo;

    /**
     * 评价
     */
    private List<TemporaryOrderEvaluate> orderEvaluateList;

    /**
     * 子订单
     */
    private List<TemporaryServiceUserSubOrder> serviceUserSubOrderList;
}
