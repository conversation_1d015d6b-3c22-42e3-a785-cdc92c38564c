package com.recruit.webApi;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.recruit.common.constant.HttpStatus;
import com.recruit.common.core.controller.BaseController;
import com.recruit.common.core.domain.AjaxResult;
import com.recruit.common.core.page.TableDataInfo;
import com.recruit.common.utils.DictUtils;
import com.recruit.common.utils.PhoneUtil;
import com.recruit.common.utils.StringUtils;
import com.recruit.common.utils.bean.BeanUtils;
import com.recruit.common.utils.ocr.CardSampleUtils;
import com.recruit.core.domain.CompanyInfo;
import com.recruit.core.domain.MerchantInfoEntry;
import com.recruit.core.domain.RecruitUserInfo;
import com.recruit.core.domain.SysServiceBanner;
import com.recruit.core.domain.TemporaryServiceGoods;
import com.recruit.core.domain.request.merchant.PlaceOrderAddressRequest;
import com.recruit.core.domain.request.temporary.ApplicationFormRequest;
import com.recruit.core.domain.response.CardSampleResponse;
import com.recruit.core.domain.response.PlaceOrderAddressResponse;
import com.recruit.core.domain.response.SampleResponse;
import com.recruit.core.service.ICompanyInfoService;
import com.recruit.core.service.IMerchantInfoEntryService;
import com.recruit.core.service.IRecruitUserInfoService;
import com.recruit.core.service.ISysServiceBannerService;
import com.recruit.core.service.ITemporaryServiceGoodsService;
import com.recruit.system.service.ISysConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;


/**
 * @Auther: Wu kong
 * @Date: 2023/5/27 11:04
 * @Description:
 */
@Api(tags= "(5-27)零工市场接口")
@Slf4j
@RestController
@RequestMapping("/web/api/temporaryWork")
public class TemporaryLaborMarketController extends BaseController {

    @Autowired
    private ISysConfigService configService;

    @Autowired
    private IRecruitUserInfoService recruitUserInfoService;

    @Autowired
    private ICompanyInfoService companyInfoService;

    @Autowired
    private IMerchantInfoEntryService merchantInfoService;

    @Autowired
    private ISysServiceBannerService sysServiceBannerService;

    @Autowired
    private ITemporaryServiceGoodsService temporaryServiceGoodsService;


    @ApiOperation("申请入驻单")
    @PostMapping("/applicationForm")
    public AjaxResult applicationForm(@RequestBody @Validated ApplicationFormRequest request)
    {
        MerchantInfoEntry merchantInfo = merchantInfoService.selectMerchantInfoEntryByUserId(getUserId());
        if(merchantInfo != null){
            throw new RuntimeException("您已申请入驻，请勿重复申请！");
        }

        MerchantInfoEntry serviceApplyForEntry = new MerchantInfoEntry();
        BeanUtils.copyBeanProp(serviceApplyForEntry, request);
        serviceApplyForEntry.setUserId(getUserId());
        //校验手机号
        String verifyPhone = PhoneUtil.verifyPhone(serviceApplyForEntry.getPhone());
        if(!StringUtils.equals(verifyPhone, "success")){
            return AjaxResult.error(HttpStatus.UNSUPPORTED_TYPE, verifyPhone);
        }
        if (request.getServiceTrades() != null) {
            StringBuilder serviceTrade = new StringBuilder();
            request.getServiceTrades().forEach(e -> {
                if (StringUtils.equals(serviceTrade, "")) {
                    serviceTrade.append(e);
                } else {
                    serviceTrade.append(",").append(e);
                }
            });
            serviceApplyForEntry.setServiceTrade(String.valueOf(serviceTrade));
        }
        serviceApplyForEntry.setAuditTime(new Date());

        //身份证正面
        if(serviceApplyForEntry.getIdCardZ() != null && !StringUtils.equals(serviceApplyForEntry.getIdCardZ(), "")){
            try {
                String idCardZInfo = CardSampleUtils.verifyIDCard(serviceApplyForEntry.getIdCardZ());
                CardSampleResponse ss =  JSON.parseObject(idCardZInfo, CardSampleResponse.class);
                switch (ss.getIdcard_number_type()){
                    case "-1":
                        throw new RuntimeException("身份证信息错误，请重新上传！");
                    case "0":
                        throw new RuntimeException("身份证证号不合法，请重新上传！");
                    case "1":
                        JSONObject jsonObjectMap = JSON.parseObject(ss.getWords_result());
                        //map对象
                        Map<String, SampleResponse> data =new HashMap<>();
                        //循环转换
                        Iterator it = jsonObjectMap.entrySet().iterator();
                        while (it.hasNext()) {
                            Map.Entry<String, Object> entry = (Map.Entry<String, Object>) it.next();

                            data.put(entry.getKey(), JSON.parseObject(entry.getValue().toString(), SampleResponse.class));
                        }
                        serviceApplyForEntry.setIdCard(data.get("公民身份号码").getWords());
                        break;
                    case "2":
                        throw new RuntimeException("身份证证号和性别、出生信息都不一致！");
                    case "3":
                        throw new RuntimeException("身份证证号和出生信息不一致！");
                    case "4":
                        throw new RuntimeException("身份证证号和性别信息不一致！");
                }
            } catch (Exception e) {
                throw new RuntimeException("身份证信息错误，请重新上传！");
            }
        }else {
            throw new RuntimeException("请上传身份证正面！");
        }
        //身份证反面
        if(serviceApplyForEntry.getIdCardF() != null && !StringUtils.equals(serviceApplyForEntry.getIdCardF(), "")){
            try {
                String idCardFInfo = CardSampleUtils.verifyIDCard(serviceApplyForEntry.getIdCardF());
                CardSampleResponse ss =  JSON.parseObject(idCardFInfo, CardSampleResponse.class);
                switch (ss.getIdcard_number_type()){
                    case "-1":
                        throw new RuntimeException("身份证信息错误，请重新上传！");
                    case "0":
                        throw new RuntimeException("身份证证号不合法，请重新上传！");
                    case "1":
                        /*JSONObject jsonObjectMap = JSON.parseObject(ss.getWords_result());
                        //map对象
                        Map<String, SampleResponse> data =new HashMap<>();
                        //循环转换
                        Iterator it = jsonObjectMap.entrySet().iterator();
                        while (it.hasNext()) {
                            Map.Entry<String, Object> entry = (Map.Entry<String, Object>) it.next();

                            data.put(entry.getKey(), JSON.parseObject(entry.getValue().toString(), SampleResponse.class));
                        }*/
                        break;
                    case "2":
                        throw new RuntimeException("身份证证号和性别、出生信息都不一致！");
                    case "3":
                        throw new RuntimeException("身份证证号和出生信息不一致！");
                    case "4":
                        throw new RuntimeException("身份证证号和性别信息不一致！");
                }
            } catch (Exception e) {
                throw new RuntimeException("身份证信息错误，请重新上传！");
            }
        }else {
            throw new RuntimeException("请上传身份证反面！");
        }

        return toAjax(merchantInfoService.insertMerchantInfoEntry(serviceApplyForEntry));
    }

    @ApiOperation("查看申请记录")
    @GetMapping("/getApplicationRecord")
    public AjaxResult getApplicationRecord()
    {
        MerchantInfoEntry applyForEntry = merchantInfoService.selectMerchantInfoEntryByUserId(getUserId());
        if(applyForEntry != null) {
            if (applyForEntry.getServiceTrade() != null && !StringUtils.equals(applyForEntry.getServiceTrade(), "")) {
                String[] serviceTrade = applyForEntry.getServiceTrade().split(",");
                List<String> serviceTrades = new ArrayList<>(Arrays.asList(serviceTrade));
                applyForEntry.setServiceTrades(serviceTrades);
            }
        }
        return success(applyForEntry);
    }


    @ApiOperation("查询客服咨询电话 按钮名称《客服咨询》")
    @GetMapping("/getCompanyInfo")
    public AjaxResult getCompanyInfo()
    {
        List<CompanyInfo> list = companyInfoService.selectCompanyInfoList(new CompanyInfo());
        Map<String, Object> maps = new HashMap<>();
        if(list.get(0).getConsultant() != null && !StringUtils.equals(list.get(0).getConsultant(), "")) {
            maps.put("consultant", list.get(0).getConsultant());
        }else {
            maps.put("consultant", "");
        }
        return success(maps);
    }

    @ApiOperation("查询零工开关")
    @GetMapping("/getJoblessSwitch")
    public AjaxResult getJoblessSwitch() {
        Boolean joblessSwitch = Boolean.valueOf(configService.selectConfigByKey("jobless_switch"));
        Map<String, Object> maps = new HashMap<>();
        maps.put("joblessSwitch", joblessSwitch);
        return success(maps);
    }


    @ApiOperation("查询零工banner，即详情描述图片 type 0小程序 1PC端")
    @GetMapping("/getOddJobBanner")
    public TableDataInfo getOddJobBanner(String type) {
        SysServiceBanner serviceBanner = new SysServiceBanner();
        serviceBanner.setStatus("0");
        serviceBanner.setType(type);
        List<SysServiceBanner> lists = sysServiceBannerService.selectSysServiceBannerList(serviceBanner);
        return getDataTable(lists);
    }


    @ApiOperation("查询服务详情")
    @GetMapping("/getServiceDetails")
    public AjaxResult getServiceDetails(String serviceTrade) {
        TemporaryServiceGoods serviceGoods = temporaryServiceGoodsService.selectTemporaryServiceGoodsByServiceTrade(serviceTrade);
        if(serviceGoods == null){
            throw new RuntimeException("该服务信息不存在！");
        }else {
            if(!StringUtils.isEmpty(serviceGoods.getTemporaryServiceGoodsPriceList())){
                serviceGoods.getTemporaryServiceGoodsPriceList().forEach(e->{
                    e.setRealityPrice(e.getGoodsPrice().multiply(new BigDecimal(e.getGoodsNum())));
                    if(!StringUtils.equals(e.getUnitType(), "")) {
                        e.setUnitType(DictUtils.getDictLabel("unit_type", e.getUnitType()));
                    }
                });
            }
        }
        return success(serviceGoods);
    }


    @ApiOperation("查询用户下单地址")
    @GetMapping(value = "/getPlaceOrderAddress", produces = {"application/json"})
    @ApiResponses({
            @ApiResponse(code = 200, message = "返回实体", response = PlaceOrderAddressResponse.class)
    })
    public AjaxResult getPlaceOrderAddress() {
        PlaceOrderAddressResponse response = new PlaceOrderAddressResponse();
        RecruitUserInfo userInfo = recruitUserInfoService.selectRecruitUserInfoById(getUserId());
        if(userInfo != null){
            response.setSex(userInfo.getSex());
            response.setAddress(userInfo.getAddress());
            response.setHouseNumber(userInfo.getHouseNumber());
            response.setLatitude(userInfo.getLatitude());
            response.setLongitude(userInfo.getLongitude());
            response.setLiaisonNan(userInfo.getLiaisonNan());
            if(userInfo.getLiaisonNanPhone() != null && !StringUtils.equals(userInfo.getLiaisonNanPhone(), "")){
                response.setLiaisonNanPhone(userInfo.getLiaisonNanPhone());
            }else {
                response.setLiaisonNanPhone(userInfo.getPhone());
            }

        }
        return success(response);
    }


    @ApiOperation("设置或更改下单地址")
    @PostMapping("/setPlaceOrderAddress")
    public AjaxResult setPlaceOrderAddress(@RequestBody @Validated PlaceOrderAddressRequest request)
    {
        RecruitUserInfo recruitUserInfo = new RecruitUserInfo();
        recruitUserInfo.setId(getUserId());
        recruitUserInfo.setSex(request.getSex());
        recruitUserInfo.setAddress(request.getAddress());
        recruitUserInfo.setHouseNumber(request.getHouseNumber());
        recruitUserInfo.setLatitude(request.getLatitude());
        recruitUserInfo.setLongitude(request.getLongitude());
        recruitUserInfo.setLiaisonNan(request.getLiaisonNan());
        recruitUserInfo.setLiaisonNanPhone(request.getLiaisonNanPhone());
        return toAjax(recruitUserInfoService.updateRecruitUserInfo(recruitUserInfo));
    }
}
