package com.recruit.web.controller.core.user;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.recruit.common.annotation.Log;
import com.recruit.common.core.controller.BaseController;
import com.recruit.common.core.domain.AjaxResult;
import com.recruit.common.enums.BusinessType;
import org.springframework.web.multipart.MultipartFile;
import com.recruit.core.domain.RecruitUserConsumeRecord;
import com.recruit.core.service.IRecruitUserConsumeRecordService;
import com.recruit.common.utils.poi.ExcelUtil;
import com.recruit.common.core.page.TableDataInfo;

/**
 * 用户消费记录Controller
 *
 * <AUTHOR>
 * @date 2023-05-23
 */
@RestController
@RequestMapping("/core/userConsumeRecord")
public class RecruitUserConsumeRecordController extends BaseController
{
    @Autowired
    private IRecruitUserConsumeRecordService recruitUserConsumeRecordService;

    /**
     * 查询用户消费记录列表
     */
    @PreAuthorize("@ss.hasPermi('core:userConsumeRecord:list')")
    @GetMapping("/list")
    public TableDataInfo list(RecruitUserConsumeRecord recruitUserConsumeRecord)
    {
        startPage();
        List<RecruitUserConsumeRecord> list = recruitUserConsumeRecordService.selectRecruitUserConsumeRecordList(recruitUserConsumeRecord);
        return getDataTable(list);
    }

    /**
     * 导出用户消费记录列表
     */
    @PreAuthorize("@ss.hasPermi('core:userConsumeRecord:export')")
    @Log(title = "用户消费记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RecruitUserConsumeRecord recruitUserConsumeRecord)
    {
        List<RecruitUserConsumeRecord> list = recruitUserConsumeRecordService.selectRecruitUserConsumeRecordList(recruitUserConsumeRecord);
        ExcelUtil<RecruitUserConsumeRecord> util = new ExcelUtil<RecruitUserConsumeRecord>(RecruitUserConsumeRecord.class);
        util.exportExcel(response, list, "用户消费记录数据");
    }

    /**
     * 获取用户消费记录详细信息
     */
    @PreAuthorize("@ss.hasPermi('core:userConsumeRecord:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(recruitUserConsumeRecordService.selectRecruitUserConsumeRecordById(id));
    }

    /**
     * 新增用户消费记录
     */
    @PreAuthorize("@ss.hasPermi('core:userConsumeRecord:add')")
    @Log(title = "用户消费记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RecruitUserConsumeRecord recruitUserConsumeRecord)
    {
        return toAjax(recruitUserConsumeRecordService.insertRecruitUserConsumeRecord(recruitUserConsumeRecord));
    }

    /**
     * 修改用户消费记录
     */
    @PreAuthorize("@ss.hasPermi('core:userConsumeRecord:edit')")
    @Log(title = "用户消费记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RecruitUserConsumeRecord recruitUserConsumeRecord)
    {
        return toAjax(recruitUserConsumeRecordService.updateRecruitUserConsumeRecord(recruitUserConsumeRecord));
    }

    /**
     * 删除用户消费记录
     */
    @PreAuthorize("@ss.hasPermi('core:userConsumeRecord:remove')")
    @Log(title = "用户消费记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(recruitUserConsumeRecordService.deleteRecruitUserConsumeRecordByIds(ids));
    }


    /**
     * 导入用户消费记录
     * @param file
     * @param updateSupport
     * @return
     * @throws Exception
     */
    @PreAuthorize("@ss.hasPermi('core:userConsumeRecord:import')")
    @Log(title = "用户消费记录", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<RecruitUserConsumeRecord> util = new ExcelUtil<>(RecruitUserConsumeRecord.class);
        List<RecruitUserConsumeRecord> lists = util.importExcel(file.getInputStream());
        String message = recruitUserConsumeRecordService.importRecruitUserConsumeRecord(lists, updateSupport);
        return AjaxResult.success(message);
    }


}
