package com.recruit.common.core.domain.entity;

import com.recruit.common.annotation.Excel;
import com.recruit.common.core.domain.BaseEntity;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 所属行业对象 sys_company_industry
 *
 * <AUTHOR>
 * @date 2023-03-28
 */
@Data
public class RecruitCompanyIndustry extends BaseEntity
{
    private static final long serialVersionUID = 1L;


    private Long id;

    private Long parentId;
    @Excel(name = "code")
    private String code;


    @Excel(name = "名称")
    private String name;


    @Excel(name = "父code")
    private String fCode;

    @Excel(name = "祖级列表")
    private String ancestors;


    @Excel(name = "显示顺序")
    private Integer orderNum;


    @Excel(name = "行业类别")
    private String industryType;

    /** 子部门 */
    private List<RecruitCompanyIndustry> children = new ArrayList<RecruitCompanyIndustry>();

}
