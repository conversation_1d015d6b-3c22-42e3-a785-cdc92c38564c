package com.recruit.core.domain;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.recruit.common.annotation.Excel;
import com.recruit.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 工作地址对象 recruit_work_address
 *
 * <AUTHOR>
 * @date 2023-03-26
 */
@Data
@ApiModel("工作地址")
public class RecruitWorkAddress extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 主键 */
    private Long enterpriseId;
    /**
     * 用户id
     */
    private Long userId;

    /** 工作地址 */
    @Excel(name = "工作地址")
    @ApiModelProperty("工作地址")
    private String workAddress;

    /** 经度 */
    @Excel(name = "经度")
    @ApiModelProperty("经度")
    private String longitude;

    /** 纬度 */
    @Excel(name = "纬度")
    @ApiModelProperty("纬度")
    private String latitude;

    /** 门牌号 */
    @Excel(name = "门牌号")
    @ApiModelProperty("门牌号")
    private String houseNumber;

    /** 门牌号 */
    @Excel(name = "是否首选")
    @ApiModelProperty("是否首选，0未选中，1选中")
    private String selectedStatus;

}
