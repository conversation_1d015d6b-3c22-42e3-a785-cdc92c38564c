package com.recruit.core.domain.request.pay;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Auther: <PERSON> kong
 * @Date: 2023/5/22 8:50
 * @Description:
 */
@Data
@ApiModel("充值请求实体")
public class MemberFeeRequest {


    @ApiModelProperty("充值金额")
    private BigDecimal rechargeAmount;

    @ApiModelProperty("支付渠道 1微信，2支付宝")
    private String payChannel;

    @ApiModelProperty("渠道 1小程序，2PC")
    private String channel;

    @ApiModelProperty("订单类型，1充值，2服务，3保证金")
    private String orderType;

    @ApiModelProperty("订单id")
    private String flowNo;

    private String description;
}
