package com.recruit.core.domain.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Auther: Wu kong
 * @Date: 2023/6/2 19:00
 * @Description:
 */
@Data
public class PlaceOrderAddressResponse {

    @ApiModelProperty("性别")
    private String sex;

    /** 地址 */
    @ApiModelProperty("地址")
    private String address;

    /** 门牌号 */
    @ApiModelProperty("门牌号")
    private String houseNumber;

    /** 经度 */
    @ApiModelProperty("经度")
    private String longitude;

    /** 纬度 */
    @ApiModelProperty("纬度")
    private String latitude;

    /** 联系人 */
    @ApiModelProperty("联系人")
    private String liaisonNan;

    @ApiModelProperty("联系人手机号")
    private String liaisonNanPhone;
}
