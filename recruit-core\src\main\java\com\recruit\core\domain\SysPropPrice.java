package com.recruit.core.domain;

import java.math.BigDecimal;
import com.recruit.common.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 道具价格对象 sys_prop_price
 *
 * <AUTHOR>
 * @date 2023-05-24
 */
@Data
public class SysPropPrice
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 道具表id */
    @Excel(name = "价格表id")
    @ApiModelProperty("价格表id")
    private Long sysPropPriceId;

    @Excel(name = "道具表id")
    @ApiModelProperty("道具表id")
    private Long sysPropId;

    /** 道具金额 */
    @Excel(name = "道具金额")
    @ApiModelProperty("道具金额")
    private BigDecimal propAmount;

    /** 道具兑换积分 */
    @Excel(name = "道具积分")
    @ApiModelProperty("道具积分")
    private String propIntegral;

    /** 有效期 */
    @Excel(name = "时间(天)")
    @ApiModelProperty("时间(天)")
    private Long subExpirationNum;

    /** 折扣 */
    @Excel(name = "折扣")
    @ApiModelProperty("折扣")
    private String discount;

    @ApiModelProperty("折扣金额")
    private BigDecimal discountAmount;

    @ApiModelProperty("折扣积分")
    private String discountIntegral;


    //道具类型
    private String propType;
    //有效期
    private Long expirationNum;
    //道具描述
    private String propDescribe;
    //类型
    private String chatType;
}
