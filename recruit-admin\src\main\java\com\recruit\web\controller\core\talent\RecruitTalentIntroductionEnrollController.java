package com.recruit.web.controller.core.talent;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.recruit.common.config.WxMaProperties;
import com.recruit.common.core.domain.entity.TemplateData;
import com.recruit.common.core.domain.entity.WxMssVo;
import com.recruit.common.utils.ChineseNationalityCodeUtil;
import com.recruit.common.utils.DateUtils;
import com.recruit.common.utils.StringUtils;
import com.recruit.core.domain.NationDomain;
import com.recruit.core.domain.RecruitTalentEducationCode;
import com.recruit.core.domain.RecruitTalentIntroductionInfo;
import com.recruit.core.domain.response.MessageResponse;
import com.recruit.core.service.IRecruitTalentEducationCodeService;
import com.recruit.core.service.IRecruitTalentIntroductionInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.recruit.common.annotation.Log;
import com.recruit.common.core.controller.BaseController;
import com.recruit.common.core.domain.AjaxResult;
import com.recruit.common.enums.BusinessType;
import com.recruit.core.domain.RecruitTalentIntroductionEnroll;
import com.recruit.core.service.IRecruitTalentIntroductionEnrollService;
import com.recruit.common.utils.poi.ExcelUtil;
import com.recruit.common.core.page.TableDataInfo;
import org.springframework.web.client.RestTemplate;

/**
 * 人才引进报名信息Controller
 *
 * <AUTHOR>
 * @date 2024-03-15
 */
@RestController
@RequestMapping("/core/talentIntroductionEnroll")
public class RecruitTalentIntroductionEnrollController extends BaseController
{

    @Autowired
    private IRecruitTalentEducationCodeService recruitTalentEducationCodeService;

    @Autowired
    private IRecruitTalentIntroductionInfoService recruitTalentIntroductionInfoService;

    @Autowired
    private IRecruitTalentIntroductionEnrollService recruitTalentIntroductionEnrollService;


    /**
     * 查询人才引进报名信息列表
     */
    @GetMapping("/list")
    public TableDataInfo list(RecruitTalentIntroductionEnroll recruitTalentIntroductionEnroll)
    {
        startPage();
        List<RecruitTalentIntroductionEnroll> list = recruitTalentIntroductionEnrollService.selectRecruitTalentIntroductionEnrollList(recruitTalentIntroductionEnroll);
        return getDataTable(list);
    }

    /**
     * 导出人才引进报名信息列表
     */
    @Log(title = "人才引进报名信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RecruitTalentIntroductionEnroll recruitTalentIntroductionEnroll)
    {
        List<RecruitTalentIntroductionEnroll> list = recruitTalentIntroductionEnrollService.selectRecruitTalentIntroductionEnrollListTwo(recruitTalentIntroductionEnroll);
        ExcelUtil<RecruitTalentIntroductionEnroll> util = new ExcelUtil<RecruitTalentIntroductionEnroll>(RecruitTalentIntroductionEnroll.class);
        try {
            util.exportExcel(response, list, "人才引进报名数据");
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    /**
     * 获取人才引进报名信息详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        RecruitTalentIntroductionEnroll introductionEnroll = recruitTalentIntroductionEnrollService.selectRecruitTalentIntroductionEnrollById(id);
        if(introductionEnroll != null){
            if(introductionEnroll.getReviewedTime() != null){
                introductionEnroll.setReviewedTime(DateUtils.getNowDate());
            }
            //人才引进信息
            RecruitTalentIntroductionInfo introductionInfo = recruitTalentIntroductionInfoService.selectRecruitTalentIntroductionInfoById(introductionEnroll.getTalentIntroductionInfoId());
            if(introductionInfo != null){
                introductionEnroll.setIntroductionInfo(introductionInfo);
            }
        }
        return success(introductionEnroll);
    }

    /**
     * 新增人才引进报名信息
     */
    @Log(title = "人才引进报名信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RecruitTalentIntroductionEnroll recruitTalentIntroductionEnroll)
    {
        return toAjax(recruitTalentIntroductionEnrollService.insertRecruitTalentIntroductionEnroll(recruitTalentIntroductionEnroll));
    }

    /**
     * 修改人才引进报名信息
     */
    @Log(title = "人才引进报名信息", businessType = BusinessType.UPDATE)
    @PutMapping
    @PreAuthorize("@ss.hasPermi('core:talentIntroductionEnroll:edit')")
    public AjaxResult edit(@RequestBody RecruitTalentIntroductionEnroll recruitTalentIntroductionEnroll)
    {
        if(recruitTalentIntroductionEnroll.getJobApplicationCode() != null && !StringUtils.equals(recruitTalentIntroductionEnroll.getJobApplicationCode(), "")){
            RecruitTalentEducationCode educationCode = recruitTalentEducationCodeService.selectEducationCode(recruitTalentIntroductionEnroll.getJobApplicationCode());
            if(educationCode != null){
                recruitTalentIntroductionEnroll.setJobApplicationName(educationCode.getSpecialityName());
            }
        }
        //审核人
        recruitTalentIntroductionEnroll.setReviewedBy(getUsername());
        int ss = recruitTalentIntroductionEnrollService.updateRecruitTalentIntroductionEnroll(recruitTalentIntroductionEnroll);
        if(ss > 0){
            if(recruitTalentIntroductionEnroll.getStatus() != 1){
                Map<String, String> maps = new HashMap<>();
                //报名时间
                maps.put("time2", DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, recruitTalentIntroductionEnroll.getEnrollTime()));
                //报名岗位
                String employer = StringUtils.substring(recruitTalentIntroductionEnroll.getNameOfEmployer(), 0, 20);
                maps.put("thing3", employer);
                //审核结果
                if(recruitTalentIntroductionEnroll.getStatus() == 0){
                    maps.put("phrase4", "审核不通过");
                }else if(recruitTalentIntroductionEnroll.getStatus() == 2){
                    maps.put("phrase4", "审核通过");
                }
                //审核时间
                maps.put("time5", DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, recruitTalentIntroductionEnroll.getReviewedTime()));
                //审核备注
                if(recruitTalentIntroductionEnroll.getReviewComments() != null && !StringUtils.equals(recruitTalentIntroductionEnroll.getReviewComments(), "")) {
                    String comments = StringUtils.substring(recruitTalentIntroductionEnroll.getReviewComments(), 0, 20);
                    maps.put("thing6", comments);
                }else {
                    maps.put("thing6", "无");
                }
                push(recruitTalentIntroductionEnroll.getOpenId(), maps);
            }
        }
        return toAjax(ss);
    }

    public MessageResponse push(String openid, Map<String, String> maps) {
        RestTemplate restTemplate = new RestTemplate();
        //这里简单起见我们每次都获取最新的access_token（时间开发中，应该在access_token快过期时再重新获取）
        String url = "https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token=" + getAccess_token();
        //拼接推送的模版
        WxMssVo wxMssVo = new WxMssVo();
        wxMssVo.setTouser(openid);//用户的openid（要发送给那个用户，通常这里应该动态传进来的）
        wxMssVo.setTemplate_id("isU66tU3OtbW9QcguSmEpk5BQQjQqfE_lBiU6GKv60A");//订阅消息模板id
        wxMssVo.setPage("pages/joinUs/record/index");

        Map<String, TemplateData> m = new HashMap<>(3);
        //报名时间
        m.put("time2", new TemplateData(){{setValue(maps.get("time2"));}});
        //报名岗位
        m.put("thing3", new TemplateData(){{setValue(maps.get("thing3"));}});
        //审核结果
        m.put("phrase4", new TemplateData(){{setValue(maps.get("phrase4"));}});
        //审核时间
        m.put("time5", new TemplateData(){{setValue(maps.get("time5"));}});
        //审核备注
        m.put("thing6", new TemplateData(){{setValue(maps.get("thing6"));}});
        wxMssVo.setData(m);
        ResponseEntity<String> responseEntity =
                restTemplate.postForEntity(url, wxMssVo, String.class);
        return JSON.parseObject(responseEntity.getBody(), MessageResponse.class);
    }

    private static RestTemplate restTemplate;
    private final WxMaProperties properties;
    public RecruitTalentIntroductionEnrollController(WxMaProperties properties) {
        this.properties = properties;
    }

    /*
     * 获取access_token
     * appid和appsecret到小程序后台获取
     * */
    public String getAccess_token() {
        //获取access_token
        String appid = properties.getAppid();  //appid和appsecret到小程序后台获取
        String appsecret = properties.getSecret();  //appid和appsecret到小程序后台获取
        String url = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential" +
                "&appid=" + appid + "&secret=" + appsecret;
        if(restTemplate==null){
            restTemplate = new RestTemplate();
        }
        String json = restTemplate.getForObject(url, String.class);
        JSONObject myJson = JSONObject.parseObject(json);
        return myJson.get("access_token").toString();
    }


    /**
     * 删除人才引进报名信息
     */
    @Log(title = "人才引进报名信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    @PreAuthorize("@ss.hasPermi('core:talentIntroductionEnroll:remove')")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(recruitTalentIntroductionEnrollService.deleteRecruitTalentIntroductionEnrollByIds(ids));
    }

    /**
     * 获取民族列表
     * @return
     */
    @GetMapping(value = "/getEthnicList")
    public AjaxResult getEthnicList()
    {
        List<NationDomain> lists = new ArrayList<>();
        Map<Integer, String> maps = ChineseNationalityCodeUtil.getNationList();
        for (Map.Entry<Integer, String> entry : maps.entrySet()) {
            NationDomain nationDomain = new NationDomain();
            nationDomain.setNationId(String.valueOf(entry.getKey()));
            nationDomain.setNationName(entry.getValue());
            lists.add(nationDomain);
        }
        return success(lists);
    }

}
