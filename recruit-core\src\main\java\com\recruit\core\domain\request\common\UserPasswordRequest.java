package com.recruit.core.domain.request.common;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Auther: <PERSON> kong
 * @Date: 2023/3/19 17:05
 * @Description:
 */
@Data
@ApiModel("设置密码1")
public class UserPasswordRequest {

    @ApiModelProperty("用户id")
    private Long userId;

    @ApiModelProperty("密码")
    private String password;

    @ApiModelProperty("新密码")
    private String newPassword;
}
