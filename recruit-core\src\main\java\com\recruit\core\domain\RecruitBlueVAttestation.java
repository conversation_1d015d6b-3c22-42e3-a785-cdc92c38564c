package com.recruit.core.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.recruit.common.annotation.Excel;
import com.recruit.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 企业蓝V认证对象 recruit_blue_v_attestation
 *
 * <AUTHOR>
 * @date 2023-05-04
 */
@Data
@ApiModel("企业蓝V认证")
public class RecruitBlueVAttestation extends BaseEntity
{
    private static final long serialVersionUID = 1L;


    private Long id;


    @Excel(name = "企业id")
    @ApiModelProperty("企业id")
    private Long enterpriseId;


    @ApiModelProperty("企业名称")
    private String enterpriseName;


    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("开始时间")
    private Date startTime;


    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("结束时间")
    private Date endTime;


    @Excel(name = "状态")
    @ApiModelProperty("状态")
    private String state;


}
