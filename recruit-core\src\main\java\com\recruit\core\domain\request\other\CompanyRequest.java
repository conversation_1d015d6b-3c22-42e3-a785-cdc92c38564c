package com.recruit.core.domain.request.other;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @Auther: <PERSON> kong
 * @Date: 2023/4/12 15:41
 * @Description:
 */
@Data
@ApiModel("收藏公司")
public class CompanyRequest {

    @NotNull(message = "企业id不能为空")
    @ApiModelProperty("企业id")
    private Long enterpriseId;
}
