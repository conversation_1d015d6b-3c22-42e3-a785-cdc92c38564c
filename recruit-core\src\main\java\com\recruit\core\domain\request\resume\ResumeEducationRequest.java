package com.recruit.core.domain.request.resume;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * @Auther: <PERSON> kong
 * @Date: 2023/3/20 17:57
 * @Description:
 */
@Data
@ApiModel("教育经历1")
public class ResumeEducationRequest {

    @ApiModelProperty("id")
    private Long id;

    @NotBlank(message = "学校名称不能为空")
    @ApiModelProperty("学校名称")
    private String schoolName;

    @NotNull(message = "入校时间不能为空")
    @ApiModelProperty("入校时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date admissionTime;

    @NotNull(message = "离校时间不能为空")
    @ApiModelProperty("离校时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date departureTime;

    @ApiModelProperty("所学专业")
    private String major;


    @NotBlank(message = "最高学历不能为空")
    @ApiModelProperty("最高学历")
    private String highestEducation;
}
