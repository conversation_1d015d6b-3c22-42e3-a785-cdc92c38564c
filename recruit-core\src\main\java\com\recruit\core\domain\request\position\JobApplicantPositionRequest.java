package com.recruit.core.domain.request.position;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Map;

/**
 * @Auther: Wu kong
 * @Date: 2023/3/27 10:47
 * @Description:
 */
@Data
@ApiModel("查询在招职位列表2")
public class JobApplicantPositionRequest {

    @ApiModelProperty("查询类别，1推荐，2最新，3急聘")
    private String queryCategory;

    @ApiModelProperty("所在地区")
    private String region;

    @ApiModelProperty("工作经验")
    private String workExperience;

    @ApiModelProperty("最低学历")
    private String minimumEducation;

    @ApiModelProperty("性别要求")
    private String genderRequirements;

    @ApiModelProperty("企业性质")
    private String enterpriseNature;

    @ApiModelProperty("企业规模")
    private String scale;

    @ApiModelProperty("最高薪资")
    private String maximumSalary;

    @ApiModelProperty("最低薪资")
    private String minimumWage;

    @ApiModelProperty("职位")
    private String position;

    @ApiModelProperty("福利")
    private String materialBenefits;

    private Integer grade;

    /** 搜索值 */
    @JsonIgnore
    private String searchValue;

    /** 请求参数 */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Map<String, Object> params;

}
