package com.recruit.core.domain.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Auther: <PERSON> kong
 * @Date: 2023/3/22 0:01
 * @Description:
 */
@Data
@ApiModel("新增联系人信息1")
public class ContactsInfoResponse {

    @ApiModelProperty("头像")
    private String headSculpture;

    @ApiModelProperty("姓名")
    private String userName;
    @ApiModelProperty("姓名")
    private String nickname;

    @ApiModelProperty("认证姓名")
    private String authenticationName;

    @ApiModelProperty("职位")
    private String position;

    @ApiModelProperty("企业名称")
    private String enterpriseName;

    @ApiModelProperty("邮箱")
    private String mailbox;

    @ApiModelProperty("认证状态")
    private String bindingStatus;

    @ApiModelProperty("手机号")
    private String phone;

    @ApiModelProperty("微信号")
    private String wechatNum;

    @ApiModelProperty("微信号")
    private String jobSeekers;

    @ApiModelProperty("微信号")
    private String recruiter;

    @ApiModelProperty("微信openId")
    private String openId;
}
