package com.recruit.core.domain.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Auther: <PERSON> kong
 * @Date: 2023/3/20 16:02
 * @Description:
 */
@Data
@ApiModel("薪资要求")
public class SalaryRequirementsResponse {

    @ApiModelProperty("薪资")
    public String salary;

    @ApiModelProperty("薪资列表")
    public List<SalaryRequirementsResponse> salaryList;

}
