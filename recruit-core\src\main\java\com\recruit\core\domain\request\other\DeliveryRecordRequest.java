package com.recruit.core.domain.request.other;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Auther: <PERSON> kong
 * @Date: 2023/4/12 0:28
 * @Description:
 */
@Data
@ApiModel("添加投递记录")
public class DeliveryRecordRequest {

    @ApiModelProperty("企业id")
    private Long enterpriseId;

    @ApiModelProperty("职位id")
    private Long positionInfoId;


    @ApiModelProperty("附件简历")
    private String attachmentResumeUrl;
}
