package com.recruit.web.controller.core.common;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.recruit.common.annotation.Log;
import com.recruit.common.core.controller.BaseController;
import com.recruit.common.core.domain.AjaxResult;
import com.recruit.common.enums.BusinessType;
import com.recruit.core.domain.SysEmote;
import com.recruit.core.service.ISysEmoteService;
import com.recruit.common.utils.poi.ExcelUtil;
import com.recruit.common.core.page.TableDataInfo;

/**
 * 系统情Controller
 *
 * <AUTHOR>
 * @date 2023-04-11
 */
@RestController
@RequestMapping("/core/emote")
public class SysEmoteController extends BaseController
{
    @Autowired
    private ISysEmoteService sysEmoteService;

    /**
     * 查询系统情列表
     */
    @PreAuthorize("@ss.hasPermi('core:emote:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysEmote sysEmote)
    {
        startPage();
        List<SysEmote> list = sysEmoteService.selectSysEmoteList(sysEmote);
        return getDataTable(list);
    }

    /**
     * 导出系统情列表
     */
    @PreAuthorize("@ss.hasPermi('core:emote:export')")
    @Log(title = "系统情", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysEmote sysEmote)
    {
        List<SysEmote> list = sysEmoteService.selectSysEmoteList(sysEmote);
        ExcelUtil<SysEmote> util = new ExcelUtil<SysEmote>(SysEmote.class);
        util.exportExcel(response, list, "系统情数据");
    }

    /**
     * 获取系统情详细信息
     */
    @PreAuthorize("@ss.hasPermi('core:emote:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(sysEmoteService.selectSysEmoteById(id));
    }

    /**
     * 新增系统情
     */
    @PreAuthorize("@ss.hasPermi('core:emote:add')")
    @Log(title = "系统情", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SysEmote sysEmote)
    {
        return toAjax(sysEmoteService.insertSysEmote(sysEmote));
    }

    /**
     * 修改系统情
     */
    @PreAuthorize("@ss.hasPermi('core:emote:edit')")
    @Log(title = "系统情", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SysEmote sysEmote)
    {
        return toAjax(sysEmoteService.updateSysEmote(sysEmote));
    }

    /**
     * 删除系统情
     */
    @PreAuthorize("@ss.hasPermi('core:emote:remove')")
    @Log(title = "系统情", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(sysEmoteService.deleteSysEmoteByIds(ids));
    }
}
