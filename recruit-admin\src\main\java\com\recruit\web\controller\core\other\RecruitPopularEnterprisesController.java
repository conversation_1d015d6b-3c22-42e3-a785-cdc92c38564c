package com.recruit.web.controller.core.other;

import com.recruit.common.annotation.Log;
import com.recruit.common.core.controller.BaseController;
import com.recruit.common.core.domain.AjaxResult;
import com.recruit.common.core.page.TableDataInfo;
import com.recruit.common.enums.BusinessType;
import com.recruit.common.utils.poi.ExcelUtil;
import com.recruit.core.domain.RecruitPopularEnterprises;
import com.recruit.core.service.IRecruitPopularEnterprisesService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 网络招聘会Controller
 *
 * <AUTHOR>
 * @date 2023-04-21
 */
@RestController
@RequestMapping("/core/popularEnterprises")
public class RecruitPopularEnterprisesController extends BaseController
{
    @Autowired
    private IRecruitPopularEnterprisesService RecruitPopularEnterprisesService;

    /**
     * 查询网络招聘会列表
     */
    @PreAuthorize("@ss.hasPermi('core:popularEnterprises:list')")
    @GetMapping("/list")
    public TableDataInfo list(RecruitPopularEnterprises RecruitPopularEnterprises)
    {
        startPage();
        List<RecruitPopularEnterprises> list = RecruitPopularEnterprisesService.selectRecruitPopularEnterprisesList(RecruitPopularEnterprises);
        return getDataTable(list);
    }

    /**
     * 导出网络招聘会列表
     */
    @PreAuthorize("@ss.hasPermi('core:popularEnterprises:export')")
    @Log(title = "网络招聘会", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RecruitPopularEnterprises RecruitPopularEnterprises)
    {
        List<RecruitPopularEnterprises> list = RecruitPopularEnterprisesService.selectRecruitPopularEnterprisesList(RecruitPopularEnterprises);
        ExcelUtil<RecruitPopularEnterprises> util = new ExcelUtil<RecruitPopularEnterprises>(RecruitPopularEnterprises.class);
        util.exportExcel(response, list, "网络招聘会数据");
    }

    /**
     * 获取网络招聘会详细信息
     */
    @PreAuthorize("@ss.hasPermi('core:popularEnterprises:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(RecruitPopularEnterprisesService.selectRecruitPopularEnterprisesById(id));
    }

    /**
     * 新增网络招聘会
     */
    @PreAuthorize("@ss.hasPermi('core:popularEnterprises:add')")
    @Log(title = "网络招聘会", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RecruitPopularEnterprises RecruitPopularEnterprises)
    {
        RecruitPopularEnterprises.setState("1");
        return toAjax(RecruitPopularEnterprisesService.insertRecruitPopularEnterprises(RecruitPopularEnterprises));
    }

    /**
     * 修改网络招聘会
     */
    @PreAuthorize("@ss.hasPermi('core:popularEnterprises:edit')")
    @Log(title = "网络招聘会", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RecruitPopularEnterprises RecruitPopularEnterprises)
    {
        return toAjax(RecruitPopularEnterprisesService.updateRecruitPopularEnterprises(RecruitPopularEnterprises));
    }

    /**
     * 删除网络招聘会
     */
    @PreAuthorize("@ss.hasPermi('core:popularEnterprises:remove')")
    @Log(title = "网络招聘会", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(RecruitPopularEnterprisesService.deleteRecruitPopularEnterprisesByIds(ids));
    }
}
