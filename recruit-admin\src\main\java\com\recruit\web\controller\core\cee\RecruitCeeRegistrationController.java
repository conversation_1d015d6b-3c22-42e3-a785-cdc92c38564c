package com.recruit.web.controller.core.cee;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.recruit.common.annotation.Anonymous;
import com.recruit.common.annotation.RepeatSubmit;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.recruit.common.annotation.Log;
import com.recruit.common.core.controller.BaseController;
import com.recruit.common.core.domain.AjaxResult;
import com.recruit.common.enums.BusinessType;
import com.recruit.core.domain.RecruitCeeRegistration;
import com.recruit.core.service.IRecruitCeeRegistrationService;
import com.recruit.common.utils.poi.ExcelUtil;
import com.recruit.common.core.page.TableDataInfo;

/**
 * 高考报名管理Controller
 *
 * <AUTHOR>
 * @date 2024-05-06
 */
@RestController
@RequestMapping("/core/cee")
public class RecruitCeeRegistrationController extends BaseController
{
    @Autowired
    private IRecruitCeeRegistrationService recruitCeeRegistrationService;

    /**
     * 查询高考报名管理列表
     */
    @PreAuthorize("@ss.hasPermi('core:cee:list')")
    @GetMapping("/list")
    public TableDataInfo list(RecruitCeeRegistration recruitCeeRegistration)
    {
        startPage();
        List<RecruitCeeRegistration> list = recruitCeeRegistrationService.selectRecruitCeeRegistrationList(recruitCeeRegistration);
        return getDataTable(list);
    }

    /**
     * 导出高考报名管理列表
     */
    @PreAuthorize("@ss.hasPermi('core:cee:export')")
    @Log(title = "高考报名管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RecruitCeeRegistration recruitCeeRegistration)
    {
        List<RecruitCeeRegistration> list = recruitCeeRegistrationService.selectRecruitCeeRegistrationList(recruitCeeRegistration);
        ExcelUtil<RecruitCeeRegistration> util = new ExcelUtil<>(RecruitCeeRegistration.class);
        util.exportExcel(response, list, "高考报名管理数据");
    }

    /**
     * 获取高考报名管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('core:cee:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(recruitCeeRegistrationService.selectRecruitCeeRegistrationById(id));
    }

    /**
     * 新增高考报名管理
     */
//    @PreAuthorize("@ss.hasPermi('core:cee:add')")
    @Anonymous
    @RepeatSubmit
    @PostMapping
    public AjaxResult add(@RequestBody RecruitCeeRegistration recruitCeeRegistration)
    {
        RecruitCeeRegistration param = new RecruitCeeRegistration();
        param.setPhone(recruitCeeRegistration.getPhone());
        List<RecruitCeeRegistration> registrations = recruitCeeRegistrationService.selectRecruitCeeRegistrationList(param);
        if (registrations.size() > 0) {
            return AjaxResult.error("您已提交成功，请等候工作人员与您联系！");
        }
        return toAjax(recruitCeeRegistrationService.insertRecruitCeeRegistration(recruitCeeRegistration));
    }

    /**
     * 修改高考报名管理
     */
    @PreAuthorize("@ss.hasPermi('core:cee:edit')")
    @Log(title = "高考报名管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RecruitCeeRegistration recruitCeeRegistration)
    {
        return toAjax(recruitCeeRegistrationService.updateRecruitCeeRegistration(recruitCeeRegistration));
    }

    /**
     * 删除高考报名管理
     */
    @PreAuthorize("@ss.hasPermi('core:cee:remove')")
    @Log(title = "高考报名管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(recruitCeeRegistrationService.deleteRecruitCeeRegistrationByIds(ids));
    }
}
