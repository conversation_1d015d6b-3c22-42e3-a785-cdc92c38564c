package com.recruit.core.domain.request.enterprise;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @Auther: <PERSON> kong
 * @Date: 2023/5/5 10:32
 * @Description:
 */
@Data
@ApiModel("企业认证管理报告1")
public class CertificationReportRequest {

    @NotBlank(message = "证明报告不能为空")
    @ApiModelProperty("证明报告")
    private String attestationReportUrl;
}
