package com.recruit.core.domain;

import com.recruit.common.annotation.Excel;
import com.recruit.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 系统支付账户设置对象 sys_pay_channel
 *
 * <AUTHOR>
 * @date 2023-05-21
 */
@Data
@ApiModel("系统支付账户设置")
public class SysPayChannel extends BaseEntity
{
    private static final long serialVersionUID = 1L;


    private Long id;


    @Excel(name = "支付渠道")
    @ApiModelProperty("支付渠道")
    private String paymentChannel;


    @Excel(name = "应用ID")
    @ApiModelProperty("应用ID")
    private String appId;


    @Excel(name = "应用密钥")
    @ApiModelProperty("应用密钥")
    private String appSecret;


    @Excel(name = "支付商户号")
    @ApiModelProperty("支付商户号")
    private String mchId;


    @Excel(name = "商户key")
    @ApiModelProperty("商户key")
    private String mchKey;


    @Excel(name = "回调地址")
    @ApiModelProperty("回调地址")
    private String notifyUrl;


    @Excel(name = "序列号")
    @ApiModelProperty("序列号")
    private String serialNumber;


    @Excel(name = "私钥key")
    @ApiModelProperty("私钥key")
    private String privateKey;


    @Excel(name = "公钥key")
    @ApiModelProperty("公钥key")
    private String publicKey;


}
