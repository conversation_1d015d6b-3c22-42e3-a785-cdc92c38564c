package com.recruit.core.domain.request.other;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @Auther: <PERSON> kong
 * @Date: 2023/4/12 12:00
 * @Description:
 */
@Data
@ApiModel("浏览足迹2")
public class EnterpriseBrowsingUsersRequest {


    @NotNull(message = "用户id不能为空")
    @ApiModelProperty("用户id")
    private Long userId;

}
