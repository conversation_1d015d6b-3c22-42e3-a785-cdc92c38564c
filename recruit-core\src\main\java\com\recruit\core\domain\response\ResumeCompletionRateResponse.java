package com.recruit.core.domain.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Auther: <PERSON> kong
 * @Date: 2023/4/23 11:19
 * @Description:
 */
@Data
@ApiModel("简历完成度1")
public class ResumeCompletionRateResponse {

    @ApiModelProperty("基本资料")
    private boolean basicInfo;

    @ApiModelProperty("职业技能")
    private boolean professionalSkills;

    @ApiModelProperty("工作经历")
    private boolean resumeWorkHistory;

    @ApiModelProperty("教育经历")
    private boolean resumeEducation;

    @ApiModelProperty("项目经历")
    private boolean resumeProjectExperience;

    @ApiModelProperty("求职意愿")
    private boolean jobSeekingIntention;

    @ApiModelProperty("自我评价")
    private boolean otherInfo;

    @ApiModelProperty("完成度")
    private int completionDegree;

}
