package com.recruit.web.controller.core.talent;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.recruit.common.annotation.Log;
import com.recruit.common.core.controller.BaseController;
import com.recruit.common.core.domain.AjaxResult;
import com.recruit.common.enums.BusinessType;
import org.springframework.web.multipart.MultipartFile;
import com.recruit.core.domain.RecruitTalentIntroductionNotice;
import com.recruit.core.service.IRecruitTalentIntroductionNoticeService;
import com.recruit.common.utils.poi.ExcelUtil;
import com.recruit.common.core.page.TableDataInfo;
import com.recruit.core.domain.RecruitTalentIntroduction;
import com.recruit.core.service.IRecruitTalentIntroductionService;

/**
 * 人才引进公告Controller
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
@RestController
@RequestMapping("/core/talentnotice")
public class RecruitTalentIntroductionNoticeController extends BaseController
{
    @Autowired
    private IRecruitTalentIntroductionNoticeService recruitTalentIntroductionNoticeService;
    
    @Autowired
    private IRecruitTalentIntroductionService recruitTalentIntroductionService;

    /**
     * 查询人才引进公告列表
     */
    @PreAuthorize("@ss.hasPermi('core:talentnotice:list')")
    @GetMapping("/list")
    public TableDataInfo list(RecruitTalentIntroductionNotice recruitTalentIntroductionNotice)
    {
        startPage();
        List<RecruitTalentIntroductionNotice> list = recruitTalentIntroductionNoticeService.selectRecruitTalentIntroductionNoticeList(recruitTalentIntroductionNotice);
        return getDataTable(list);
    }

    /**
     * 导出人才引进公告列表
     */
    @PreAuthorize("@ss.hasPermi('core:talentnotice:export')")
    @Log(title = "人才引进公告", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RecruitTalentIntroductionNotice recruitTalentIntroductionNotice)
    {
        List<RecruitTalentIntroductionNotice> list = recruitTalentIntroductionNoticeService.selectRecruitTalentIntroductionNoticeList(recruitTalentIntroductionNotice);
        ExcelUtil<RecruitTalentIntroductionNotice> util = new ExcelUtil<RecruitTalentIntroductionNotice>(RecruitTalentIntroductionNotice.class);
        util.exportExcel(response, list, "人才引进公告数据");
    }

    /**
     * 获取人才引进公告详细信息
     */
    @PreAuthorize("@ss.hasPermi('core:talentnotice:query')")
    @GetMapping(value = "/{noticeId}")
    public AjaxResult getInfo(@PathVariable("noticeId") Long noticeId)
    {
        return success(recruitTalentIntroductionNoticeService.selectRecruitTalentIntroductionNoticeByNoticeId(noticeId));
    }

    /**
     * 新增人才引进公告
     */
    @PreAuthorize("@ss.hasPermi('core:talentnotice:add')")
    @Log(title = "人才引进公告", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RecruitTalentIntroductionNotice recruitTalentIntroductionNotice)
    {
        return toAjax(recruitTalentIntroductionNoticeService.insertRecruitTalentIntroductionNotice(recruitTalentIntroductionNotice));
    }

    /**
     * 修改人才引进公告
     */
    @PreAuthorize("@ss.hasPermi('core:talentnotice:edit')")
    @Log(title = "人才引进公告", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RecruitTalentIntroductionNotice recruitTalentIntroductionNotice)
    {
        return toAjax(recruitTalentIntroductionNoticeService.updateRecruitTalentIntroductionNotice(recruitTalentIntroductionNotice));
    }

    /**
     * 删除人才引进公告
     */
    @PreAuthorize("@ss.hasPermi('core:talentnotice:remove')")
    @Log(title = "人才引进公告", businessType = BusinessType.DELETE)
	@DeleteMapping("/{noticeIds}")
    public AjaxResult remove(@PathVariable Long[] noticeIds)
    {
        return toAjax(recruitTalentIntroductionNoticeService.deleteRecruitTalentIntroductionNoticeByNoticeIds(noticeIds));
    }

    /**
     * 获取人才引进列表(用于下拉框)
     */
    @GetMapping("/getTalentIntroductionOptions")
    public AjaxResult getTalentIntroductionOptions()
    {
        List<RecruitTalentIntroduction> list = recruitTalentIntroductionService.selectRecruitTalentIntroductionList(new RecruitTalentIntroduction());
        return success(list);
    }

}
