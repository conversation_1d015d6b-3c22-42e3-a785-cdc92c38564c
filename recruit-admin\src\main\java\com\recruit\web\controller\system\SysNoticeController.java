package com.recruit.web.controller.system;

import java.util.List;
import com.recruit.common.constant.RedisMessageType;
import com.recruit.common.utils.StringUtils;
import com.recruit.web.core.redisMSMQ.MessageProducer;
import org.quartz.SchedulerException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.recruit.common.annotation.Log;
import com.recruit.common.core.controller.BaseController;
import com.recruit.common.core.domain.AjaxResult;
import com.recruit.common.core.page.TableDataInfo;
import com.recruit.common.enums.BusinessType;
import com.recruit.system.domain.SysNotice;
import com.recruit.system.service.ISysNoticeService;

/**
 * 公告 信息操作处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/notice")
public class SysNoticeController extends BaseController
{
    @Autowired
    private ISysNoticeService noticeService;

    @Autowired
    private MessageProducer messageProducer;

    /**
     * 获取通知公告列表
     */
    @PreAuthorize("@ss.hasPermi('system:notice:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysNotice notice)
    {
        startPage();
        List<SysNotice> list = noticeService.selectNoticeList(notice);
        return getDataTable(list);
    }

    /**
     * 根据通知公告编号获取详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:notice:query')")
    @GetMapping(value = "/{noticeId}")
    public AjaxResult getInfo(@PathVariable Long noticeId)
    {
        return success(noticeService.selectNoticeById(noticeId));
    }

    /**
     * 新增通知公告
     */
    @PreAuthorize("@ss.hasPermi('system:notice:add')")
    @Log(title = "通知公告", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody SysNotice notice)
    {
        notice.setCreateBy(getUsername());
        int ss = noticeService.insertNotice(notice);
        if(ss > 0){
            //计划策略 1立即执行
            if(StringUtils.equals(notice.getMisfirePolicy(), "1")) {
                //异步执行
                messageProducer.lPush(RedisMessageType.SYS_NOTICE, notice);
            }
        }
        return toAjax(ss);
    }

    /**
     * 修改通知公告
     */
    @PreAuthorize("@ss.hasPermi('system:notice:edit')")
    @Log(title = "通知公告", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody SysNotice notice)
    {
        notice.setUpdateBy(getUsername());
        int ss = noticeService.updateNotice(notice);
        if(ss > 0){
            //计划策略 1立即执行
            if(StringUtils.equals(notice.getMisfirePolicy(), "1")) {
                //异步执行
                messageProducer.lPush(RedisMessageType.SYS_NOTICE, notice);
            }
        }
        return toAjax(ss);
    }

    /**
     * 删除通知公告
     */
    @PreAuthorize("@ss.hasPermi('system:notice:remove')")
    @Log(title = "通知公告", businessType = BusinessType.DELETE)
    @DeleteMapping("/{noticeIds}")
    public AjaxResult remove(@PathVariable Long[] noticeIds)
    {
        return toAjax(noticeService.deleteNoticeByIds(noticeIds));
    }

    @Log(title = "发送通知", businessType = BusinessType.UPDATE)
    @PutMapping("/sendingNotice")
    public AjaxResult sendingNotice(@RequestBody SysNotice notice) throws SchedulerException
    {
        SysNotice notices = noticeService.selectNoticeById(notice.getNoticeId());
        if(notices != null){
            notices.setMisfirePolicy("1");
            //计划策略 1立即执行
            noticeService.updateNotice(notices);
            //异步执行
            messageProducer.lPush(RedisMessageType.SYS_NOTICE, notices);
            return success();
        }else {
            return error("通知不存在，请查验！");
        }
    }


    @GetMapping("/sysNoticeListAll")
    public TableDataInfo sysNoticeListAll()
    {
        List<SysNotice> list = noticeService.selectNoticeList(new SysNotice());
        return getDataTable(list);
    }
}
