package com.recruit.core.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.recruit.common.annotation.Excel;
import com.recruit.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 用户道具对象 recruit_user_prop
 *
 * <AUTHOR>
 * @date 2023-05-23
 */
@Data
@ApiModel("用户道具")
public class RecruitUserProp extends BaseEntity
{
    private static final long serialVersionUID = 1L;


    private Long id;


    @Excel(name = "道具表id")
    @ApiModelProperty("道具表id")
    private Long sysPropId;


    @Excel(name = "用户id")
    @ApiModelProperty("用户id")
    private Long userId;


    @ApiModelProperty("职位id")
    private Long positionInfoId;


    @ApiModelProperty("职位编码")
    private String positionCode;
    @ApiModelProperty("职位名称")
    private String positionInfoName;


    @ApiModelProperty("用户id")
    private String userName;


    @Excel(name = "道具类型")
    @ApiModelProperty("道具类型")
    private String propType;


    @Excel(name = "道具描述")
    @ApiModelProperty("道具描述")
    private String propDescribe;


    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "到期时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("到期时间")
    private Date expirationDate;


    @Excel(name = "使用状态 0未使用，1使用中，2已使用，3过期")
    @ApiModelProperty("使用状态 0未使用，1使用中，2已使用，3过期")
    private String onState;

    @ApiModelProperty("类型 1：求职者 2：招聘者")
    private String chatType;

    @ApiModelProperty("时间卡，时间数")
    private Long subExpirationNum;

    @ApiModelProperty("使用时间")
    private Date usageTime;


}
