package com.recruit.webApi.im;

import com.recruit.common.core.controller.BaseController;
import com.recruit.common.core.domain.AjaxResult;
import com.recruit.common.core.domain.vo.FriendVO;
import com.recruit.common.core.page.TableDataInfo;
import com.recruit.common.utils.DateUtils;
import com.recruit.core.domain.Friend;
import com.recruit.core.domain.PrivateMessage;
import com.recruit.core.domain.RecruitPositionInfo;
import com.recruit.core.domain.RecruitUserInfo;
import com.recruit.core.domain.RecruitUserSysNotice;
import com.recruit.core.domain.request.im.FriendRequest;
import com.recruit.core.service.IRecruitPositionInfoService;
import com.recruit.core.service.IRecruitPositionService;
import com.recruit.core.service.IRecruitUserInfoService;
import com.recruit.core.service.IRecruitUserSysNoticeService;
import com.recruit.core.service.IFriendService;
import com.recruit.core.service.IPrivateMessageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Auther: Wu kong
 * @Date: 2023/4/7 11:48
 * @Description:
 */
@Api(tags= "IM好友信息")
@RestController
@RequestMapping("/web/api/ImFriend")
public class ImFriendController extends BaseController {

    @Autowired
    private IFriendService friendService;

    @Autowired
    private IPrivateMessageService privateMessageService;

    @Autowired
    private IRecruitPositionService recruitPositionService;

    @Autowired
    private IRecruitUserInfoService recruitUserInfoService;

    @Autowired
    private IRecruitPositionInfoService recruitPositionInfoService;

    @Autowired
    private IRecruitUserSysNoticeService recruitUserSysNoticeService;


    @GetMapping("/selectSystemNotice")
    @ApiOperation(value = "系统通知",notes="notice_type 公告类型（1通知 2公告）传分页字段 pageNum， pageSize")
    public TableDataInfo selectSystemNotice(RecruitUserSysNotice userSysNotice)
    {
        startPage();
        userSysNotice.setUserId(getUserId());
        List<RecruitUserSysNotice> lists = recruitUserSysNoticeService.selectRecruitUserSysNoticeList(userSysNotice);
        return getDataTable(lists);
    }

    @GetMapping("/getSystemNotice/{noticeId}")
    @ApiOperation(value = "查看系统通知详情",notes="notice_type 公告类型（1通知 2公告）")
    public AjaxResult getSystemNotice(@NotEmpty(message = "消息不可为空") @PathVariable("noticeId") Long noticeId)
    {
        return success(recruitUserSysNoticeService.selectRecruitUserSysNoticeById(noticeId));
    }


    @GetMapping("/getSystemNoticeNews")
    @ApiOperation(value = "查看系统通知最新消息")
    public AjaxResult getSystemNoticeNews()
    {
        RecruitUserSysNotice sysNotice = recruitUserSysNoticeService.getTheLatestNews(getUserId());
        if(sysNotice != null) {
            //最新消息时间
            if (DateUtils.isNow(sysNotice.getSendTime())) {
                sysNotice.setLatestNewsTime(DateUtils.parseDateToStr(DateUtils.HH_MM, sysNotice.getSendTime()));
            } else {
                sysNotice.setLatestNewsTime(DateUtils.parseDateToStr(DateUtils.MM_DD, sysNotice.getSendTime()));
            }
        }
        return success(sysNotice);
    }


    /*@DeleteMapping("/removalSystemNotice/{id}")
    @ApiOperation(value = "删除系统通知",notes="删除系统通知")
    public AjaxResult removalSystemNotice(@NotNull(message = "系统通知id不能为空") @PathVariable Long id){
        return success(recruitUserSysNoticeService.deleteRecruitUserSysNoticeById(id));
    }*/


    @GetMapping("/list")
    @ApiOperation(value = "聊天列表",notes="聊天列表")
    public TableDataInfo findFriends(){

        RecruitUserInfo userInfo = recruitUserInfoService.selectRecruitUserInfoById(getUserId());
        List<Friend> friends = friendService.findFriendByUserId(getUserId(), userInfo.getType());
        List<FriendVO> vos = friends.stream().map(f->{
            FriendVO vo = new FriendVO();
            vo.setId(f.getFriendId());
            vo.setHeadImage(f.getFriendHeadImage());
            vo.setNickName(f.getFriendNickName());
            vo.setPositionInfoId(f.getPositionInfoId());
            vo.setPositionInfoName(recruitPositionService.getMap(f.getPositionInfoName()));
            //公司名称
            vo.setEnterpriseName(f.getEnterpriseName());
            //期望岗位
            vo.setExpectedPositionName(f.getExpectedPositionName());
            //职位
            vo.setPosition(f.getPosition());
            PrivateMessage privateMessage = privateMessageService.getTheLatestNews(getUserId(), f.getFriendId(), userInfo.getType());
            if(privateMessage != null){
                try {
                    String subStr = privateMessage.getContent().substring(0, 15) + "...";
                    vo.setLatestNews(subStr);
                }catch (Exception e){
                    vo.setLatestNews(privateMessage.getContent());
                }

                //最新消息时间
                if(DateUtils.isNow(privateMessage.getSendTime())){
                    vo.setLatestNewsTime(DateUtils.parseDateToStr(DateUtils.HH_MM, privateMessage.getSendTime()));
                }else {
                    vo.setLatestNewsTime(DateUtils.parseDateToStr(DateUtils.MM_DD, privateMessage.getSendTime()));
                }
                vo.setType(privateMessage.getType());
            }
            vo.setObtainUnreadNum(privateMessageService.pullUnreadMessage(f.getFriendId()));
            return vo;
        }).collect(Collectors.toList());
        return getDataTable(vos);
    }


    @GetMapping("/getUnreadMessagesNum")
    @ApiOperation(value = "查询总未读消息数")
    public AjaxResult getUnreadMessagesNum()
    {
        return success(privateMessageService.getUnreadMessagesNum());
    }


    @PostMapping("/add")
    @ApiOperation(value = "添加好友",notes="双方建立好友关系")
    public AjaxResult addFriend(@RequestBody @Validated FriendRequest request){

        RecruitUserInfo userInfo = recruitUserInfoService.selectRecruitUserInfoById(getUserId());
        if(request.getPositionInfoId() == null){
            RecruitPositionInfo positionInfo = new RecruitPositionInfo();
            if(userInfo.getType().equals("2")){
                positionInfo = recruitPositionInfoService.selectPositionInfoByUserId(getUserId());
                if(positionInfo != null) {
                    request.setPositionInfoId(positionInfo.getId());
                    request.setEnterpriseId(positionInfo.getEnterpriseId());
                }
            }else {
                positionInfo = recruitPositionInfoService.selectPositionInfoByPublisherId(request.getFriendId());
                if(positionInfo != null) {
                    request.setPositionInfoId(positionInfo.getId());
                    request.setEnterpriseId(positionInfo.getEnterpriseId());
                }
            }
        }
        FriendVO friendVO = friendService.checkIfTheUserExists(request.getFriendId(), getUserId(), userInfo.getType());
        if(friendVO == null){
            friendService.addFriend(request.getFriendId(), getUserId(), userInfo.getType(), request.getPositionInfoId(), request.getEnterpriseId());
        }else {
            friendVO.setPositionInfoId(request.getPositionInfoId());
            friendVO.setEnterpriseId(request.getEnterpriseId());
            friendService.updateImFriend(friendVO);
        }
        return success();
    }

    /*@GetMapping("/find/{friendId}")
    @ApiOperation(value = "查找好友信息",notes="查找好友信息")
    public AjaxResult findFriend(@NotEmpty(message = "好友id不可为空") @PathVariable("friendId") Long friendId){
        return success(friendService.findFriend(friendId));
    }*/

    /*@DeleteMapping("/delete/{friendId}")
    @ApiOperation(value = "删除好友（一般用不上）",notes="解除好友关系")
    public AjaxResult delFriend(@NotEmpty(message = "好友id不可为空") @PathVariable("friendId") Long friendId){
        friendService.delFriend(friendId, getUserId());
        return success();
    }*/

    @DeleteMapping("/removalMessage/{id}")
    @ApiOperation(value = "消息列表删除消息",notes="消息列表删除消息")
    public AjaxResult removalMessage(@NotNull(message = "消息id不能为空") @PathVariable Long id){
        return success(friendService.removalMessage(id, getUserId()));
    }


}
