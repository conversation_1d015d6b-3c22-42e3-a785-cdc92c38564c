package com.recruit.web.controller.core.merchant;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.recruit.core.domain.MerchantInfoEntry;
import com.recruit.core.service.IMerchantInfoEntryService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.recruit.common.annotation.Log;
import com.recruit.common.core.controller.BaseController;
import com.recruit.common.core.domain.AjaxResult;
import com.recruit.common.enums.BusinessType;
import org.springframework.web.multipart.MultipartFile;
import com.recruit.core.domain.TemporaryMerchantPunish;
import com.recruit.core.service.ITemporaryMerchantPunishService;
import com.recruit.common.utils.poi.ExcelUtil;
import com.recruit.common.core.page.TableDataInfo;

/**
 * 商户处罚信息Controller
 *
 * <AUTHOR>
 * @date 2023-05-30
 */
@RestController
@RequestMapping("/core/merchantPunish")
public class TemporaryMerchantPunishController extends BaseController
{
    @Autowired
    private IMerchantInfoEntryService merchantInfoEntryService;

    @Autowired
    private ITemporaryMerchantPunishService temporaryMerchantPunishService;

    /**
     * 查询商户处罚信息列表
     */
    @GetMapping("/list")
    public TableDataInfo list(TemporaryMerchantPunish temporaryMerchantPunish)
    {
        startPage();
        List<TemporaryMerchantPunish> list = temporaryMerchantPunishService.selectTemporaryMerchantPunishList(temporaryMerchantPunish);
        return getDataTable(list);
    }

    /**
     * 导出商户处罚信息列表
     */
    @Log(title = "商户处罚信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TemporaryMerchantPunish temporaryMerchantPunish)
    {
        List<TemporaryMerchantPunish> list = temporaryMerchantPunishService.selectTemporaryMerchantPunishList(temporaryMerchantPunish);
        ExcelUtil<TemporaryMerchantPunish> util = new ExcelUtil<TemporaryMerchantPunish>(TemporaryMerchantPunish.class);
        util.exportExcel(response, list, "商户处罚信息数据");
    }

    /**
     * 获取商户处罚信息详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(temporaryMerchantPunishService.selectTemporaryMerchantPunishById(id));
    }

    /**
     * 新增商户处罚信息
     */
    @Log(title = "商户处罚信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TemporaryMerchantPunish temporaryMerchantPunish)
    {
        MerchantInfoEntry merchantInfo = new MerchantInfoEntry();
        merchantInfo.setId(temporaryMerchantPunish.getMerchantInfoId());
        merchantInfo.setPenaltyTime(temporaryMerchantPunish.getEntTime());
        merchantInfo.setApplicationStatus("3");
        merchantInfoEntryService.updateMerchantInfoEntry(merchantInfo);

        return toAjax(temporaryMerchantPunishService.insertTemporaryMerchantPunish(temporaryMerchantPunish));
    }

    /**
     * 修改商户处罚信息
     */
    @Log(title = "商户处罚信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TemporaryMerchantPunish temporaryMerchantPunish)
    {
        return toAjax(temporaryMerchantPunishService.updateTemporaryMerchantPunish(temporaryMerchantPunish));
    }

    /**
     * 删除商户处罚信息
     */
    @Log(title = "商户处罚信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(temporaryMerchantPunishService.deleteTemporaryMerchantPunishByIds(ids));
    }


    /**
     * 导入商户处罚信息
     * @param file
     * @param updateSupport
     * @return
     * @throws Exception
     */
    @Log(title = "商户处罚信息", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<TemporaryMerchantPunish> util = new ExcelUtil<>(TemporaryMerchantPunish.class);
        List<TemporaryMerchantPunish> lists = util.importExcel(file.getInputStream());
        String message = temporaryMerchantPunishService.importTemporaryMerchantPunish(lists, updateSupport);
        return AjaxResult.success(message);
    }


}
