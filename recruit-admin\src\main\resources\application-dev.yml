# 项目相关配置
public:
  # 名称
  name: <PERSON>Kong
  # 版本
  version: 3.8.5
  # 版权年份
  copyrightYear: 2023
  # 实例演示开关
  demoEnabled: true
  # 文件路径 示例（ Windows配置D:/WuKong/uploadPath，Linux配置 /home/<USER>/uploadPath）
  profile: /data/uploadPath
  # 获取ip地址开关
  addressEnabled: false
  # 验证码类型 math 数组计算 char 字符验证
  captchaType: math
  # 上传
  uploadUrl: http://127.0.0.1:8080

# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080
  port: 8088
  servlet:
    # 应用的访问路径
    context-path: /
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # 连接数满后的排队数，默认为100
    accept-count: 1000
    threads:
      # tomcat最大线程数，默认为200
      max: 800
      # Tomcat启动初始化的线程数，默认值10
      min-spare: 100

websocket:
  enable: true
  port: 8087
tcpsocket:
  enable: false # 暂时不开启
  port: 8089

# 日志配置
logging:
  level:
    org.mybatis: DEBUG
    com.WuKong: DEBUG
    org.springframework: WARN
    org.mybatis.spring: TRACE
    java.sql.Connection: DEBUG
    java.sql.PreparedStatement: TRACE

# 用户配置
user:
  password:
    # 密码最大错误次数
    maxRetryCount: 5
    # 密码锁定时间（默认1分钟）
    lockTime: 1

# Spring配置
spring:
  # 数据源配置
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driverClassName: com.mysql.cj.jdbc.Driver
    druid:
      # 主库数据源
      master:
        url: *****************************************************************************************************************************************************
        username: root
        password: 123456
      # 从库数据源
      slave:
        # 从数据源开关/默认关闭
        enabled: false
        url:
        username:
        password:
      # 初始连接数
      initialSize: 5
      # 最小连接池数量
      minIdle: 10
      # 最大连接池数量
      maxActive: 20
      # 配置获取连接等待超时的时间
      maxWait: 60000
      # 配置连接超时时间
      connectTimeout: 30000
      # 配置网络超时时间
      socketTimeout: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      timeBetweenEvictionRunsMillis: 60000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      minEvictableIdleTimeMillis: 300000
      # 配置一个连接在池中最大生存的时间，单位是毫秒
      maxEvictableIdleTimeMillis: 900000
      # 配置检测连接是否有效
      validationQuery: SELECT 1 FROM DUAL
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      webStatFilter:
        enabled: true
      statViewServlet:
        enabled: true
        # 设置白名单，不填则允许所有访问
        allow:
        url-pattern: /druid/*
        # 控制台管理用户名和密码
        login-username: admin
        login-password: 123456
      filter:
        stat:
          enabled: true
          # 慢SQL记录
          log-slow-sql: true
          slow-sql-millis: 1000
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  # 文件上传
  servlet:
     multipart:
       # 单个文件大小
       max-file-size:  10MB
       # 设置总上传的文件大小
       max-request-size:  20MB
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: true
  # redis 配置
  redis:
    # 地址
    host: 127.0.0.1
    # 端口，默认为6379
    port: 6379
    # 数据库索引
    database: 1
    # 密码
    password:
    # 连接超时时间
    timeout: 10s
    lettuce:
      pool:
        # 连接池中的最小空闲连接
        min-idle: 0
        # 连接池中的最大空闲连接
        max-idle: 8
        # 连接池的最大数据库连接数
        max-active: 8
        # #连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms

# token配置
token:
    # 令牌自定义标识
    header: Authorization
    # 令牌密钥
    secret: abcdefghijklmnopqrstuvwxyz
    # 令牌有效期（默认30分钟）
    expireTime: 30
    # 令牌有效期（默认4320分钟 3天）
    expireTimeTwo: 4320

# MyBatis配置
mybatis:
    # 搜索指定包别名
    typeAliasesPackage: com.recruit.**.domain
    # 配置mapper的扫描，找到所有的mapper.xml映射文件
    mapperLocations: classpath*:mapper/**/*Mapper.xml
    # 加载全局的配置文件
    configLocation: classpath:mybatis/mybatis-config.xml
    configuration:
      log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

# PageHelper分页插件
pagehelper:
  helperDialect: mysql
  supportMethodsArguments: true
  params: count=countSql

# Swagger配置
swagger:
  # 是否开启swagger
  enabled: true
  # 请求前缀
  pathMapping: /dev-api

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*

wx:
  miniapp:
    appid: wx9b1da9361e65e88d
    secret: 97d27823e88079e21c9690448de81198
    token: #微信小程序消息服务器配置的token
    aesKey: #微信小程序消息服务器配置的EncodingAESKey
    msgDataFormat: JSON
  # 公众号配置
  mp:
    configs:
      - appId: wx0e886834b061fa20
        secret: 7343511a6ee88aeb8b990afe25e16c4f
        token: xxxxxxxxxx
        aesKey: xxxxxxxxxxxxxxxxxxxx

litemall:
  #通知相关配置
  notify:
    mail:
      # 邮件通知配置,邮箱一般用于接收业务通知例如收到新的订单，sendto 定义邮件接收者，通常为商城运营人员
      enable: false
      host: smtp.exmail.qq.com
      username: <EMAIL>
      password: XXXXXXXXXXXXX
      sendfrom: <EMAIL>
      sendto: <EMAIL>
      port: 465
    # 短消息模版通知配置
    # 短信息用于通知客户，例如发货短信通知，注意配置格式；template-name，template-templateId 请参考 NotifyType 枚举值
    sms:
      enable: true
      # 如果是阿里云短信，则设置active的值aliyun
      # 如果是华为云短信，则设置active的值huawei
      active: tencent
      sign: 哈密博汇人才
      template:
        - name: captcha
          templateId: 1764873
        - name: forgot
          templateId: 1790592
        - name: modify
          templateId: 1807665
      tencent:
        appid: 1400907908
        appkey: 2683d8743f2f10df7a3c66398ca8039b
      aliyun:
        regionId: xxx
        accessKeyId: xxx
        accessKeySecret: xxx
      huawei:
        sender: 1069368924410003544
        accessKeyId: 41VZZ1GdAY9Lw4ryX81Fp3Ta04Fx
        secretAccessKey: YBfeizFRS0e9MfwzinKImnDWGyrd

minio:
  endpoint: http://36.138.126.177:9000 #内网地址
  public: https://minio.hmrczpw.com:9002  #外网访问地址
  accessKey: eM0Wbl0yJfGQE8H7
  secretKey: FFJLrWnAdO2Os216F2ba7XL2fWKJnh6N
  bucketName: box-im
  imagePath: image
  filePath: file
