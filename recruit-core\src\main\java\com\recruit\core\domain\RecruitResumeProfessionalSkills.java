package com.recruit.core.domain;

import com.recruit.common.annotation.Excel;
import com.recruit.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 简历职业技能对象 recruit_resume_professional_skills
 *
 * <AUTHOR>
 * @date 2023-03-21
 */
@Data
@ApiModel("简历职业技能")
public class RecruitResumeProfessionalSkills extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 用户id */
    @Excel(name = "用户id")
    @ApiModelProperty("用户id")
    private Long userId;

    /** 技能名称 */
    @Excel(name = "技能名称")
    @ApiModelProperty("技能名称")
    private String skillName;

    /** 熟练程度 */
    @Excel(name = "熟练程度")
    @ApiModelProperty("熟练程度")
    private String proficiency;

    @Excel(name = "熟练程度名称")
    @ApiModelProperty("熟练程度名称")
    private String proficiencyName;


}
