package com.recruit.webApi;

import com.recruit.common.core.controller.BaseController;
import com.recruit.common.core.domain.AjaxResult;
import com.recruit.common.core.page.TableDataInfo;
import com.recruit.core.domain.RecruitTalentIntroductionNotice;
import com.recruit.core.service.IRecruitTalentIntroductionNoticeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import java.util.List;

/**
 * 人才引进公告Controller
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
@RestController
@RequestMapping("/web/api/talentnotice")
public class RecruitTalentIntroductionNoticeApiController extends BaseController
{
    @Autowired
    private IRecruitTalentIntroductionNoticeService recruitTalentIntroductionNoticeService;

    /**
     * 查询人才引进公告列表
     */
    @GetMapping("/list")
    public TableDataInfo list(RecruitTalentIntroductionNotice recruitTalentIntroductionNotice)
    {
        startPage();
        recruitTalentIntroductionNotice.setStatus("0");
        List<RecruitTalentIntroductionNotice> list = recruitTalentIntroductionNoticeService.selectRecruitTalentIntroductionNoticeList(recruitTalentIntroductionNotice);
        return getDataTable(list);
    }

    /**
     * 获取人才引进公告详细信息
     */
    @GetMapping(value = "/{noticeId}")
    public AjaxResult getInfo(@PathVariable("noticeId") Long noticeId)
    {
        return success(recruitTalentIntroductionNoticeService.selectRecruitTalentIntroductionNoticeByNoticeId(noticeId));
    }

}
