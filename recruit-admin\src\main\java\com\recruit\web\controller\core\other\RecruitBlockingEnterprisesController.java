package com.recruit.web.controller.core.other;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.recruit.common.annotation.Log;
import com.recruit.common.core.controller.BaseController;
import com.recruit.common.core.domain.AjaxResult;
import com.recruit.common.enums.BusinessType;
import com.recruit.core.domain.RecruitBlockingEnterprises;
import com.recruit.core.service.IRecruitBlockingEnterprisesService;
import com.recruit.common.utils.poi.ExcelUtil;
import com.recruit.common.core.page.TableDataInfo;

/**
 * 屏蔽公司Controller
 *
 * <AUTHOR>
 * @date 2023-04-12
 */
@RestController
@RequestMapping("/core/blockingEnterprises")
public class RecruitBlockingEnterprisesController extends BaseController
{
    @Autowired
    private IRecruitBlockingEnterprisesService recruitBlockingEnterprisesService;

    /**
     * 查询屏蔽公司列表
     */
    @PreAuthorize("@ss.hasPermi('core:blockingEnterprises:list')")
    @GetMapping("/list")
    public TableDataInfo list(RecruitBlockingEnterprises recruitBlockingEnterprises)
    {
        startPage();
        List<RecruitBlockingEnterprises> list = recruitBlockingEnterprisesService.selectRecruitBlockingEnterprisesList(recruitBlockingEnterprises);
        return getDataTable(list);
    }

    /**
     * 导出屏蔽公司列表
     */
    @PreAuthorize("@ss.hasPermi('core:blockingEnterprises:export')")
    @Log(title = "屏蔽公司", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RecruitBlockingEnterprises recruitBlockingEnterprises)
    {
        List<RecruitBlockingEnterprises> list = recruitBlockingEnterprisesService.selectRecruitBlockingEnterprisesList(recruitBlockingEnterprises);
        ExcelUtil<RecruitBlockingEnterprises> util = new ExcelUtil<RecruitBlockingEnterprises>(RecruitBlockingEnterprises.class);
        util.exportExcel(response, list, "屏蔽公司数据");
    }

    /**
     * 获取屏蔽公司详细信息
     */
    @PreAuthorize("@ss.hasPermi('core:blockingEnterprises:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(recruitBlockingEnterprisesService.selectRecruitBlockingEnterprisesById(id));
    }

    /**
     * 新增屏蔽公司
     */
    @PreAuthorize("@ss.hasPermi('core:blockingEnterprises:add')")
    @Log(title = "屏蔽公司", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RecruitBlockingEnterprises recruitBlockingEnterprises)
    {
        return toAjax(recruitBlockingEnterprisesService.insertRecruitBlockingEnterprises(recruitBlockingEnterprises));
    }

    /**
     * 修改屏蔽公司
     */
    @PreAuthorize("@ss.hasPermi('core:blockingEnterprises:edit')")
    @Log(title = "屏蔽公司", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RecruitBlockingEnterprises recruitBlockingEnterprises)
    {
        return toAjax(recruitBlockingEnterprisesService.updateRecruitBlockingEnterprises(recruitBlockingEnterprises));
    }

    /**
     * 删除屏蔽公司
     */
    @PreAuthorize("@ss.hasPermi('core:blockingEnterprises:remove')")
    @Log(title = "屏蔽公司", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(recruitBlockingEnterprisesService.deleteRecruitBlockingEnterprisesByIds(ids));
    }
}
