package com.recruit.web.controller.core.position;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import com.recruit.common.notify.NotifyService;
import com.recruit.common.utils.StringUtils;
import com.recruit.core.domain.RecruitWorkAddress;
import com.recruit.core.domain.SysSmsSendingRecord;
import com.recruit.core.domain.SysSmsTemplate;
import com.recruit.core.service.IRecruitPositionService;
import com.recruit.core.service.IRecruitWorkAddressService;
import com.recruit.core.service.ISysSmsSendingRecordService;
import com.recruit.core.service.ISysSmsTemplateService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.recruit.common.annotation.Log;
import com.recruit.common.core.controller.BaseController;
import com.recruit.common.core.domain.AjaxResult;
import com.recruit.common.enums.BusinessType;
import com.recruit.core.domain.RecruitPositionInfo;
import com.recruit.core.service.IRecruitPositionInfoService;
import com.recruit.common.utils.poi.ExcelUtil;
import com.recruit.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 职位信息Controller
 *
 * <AUTHOR>
 * @date 2023-03-26
 */
@RestController
@RequestMapping("/core/positionInfo")
public class RecruitPositionInfoController extends BaseController
{
    @Resource
    private NotifyService notifyService;

    @Autowired
    private ISysSmsTemplateService sysSmsTemplateService;

    @Autowired
    private IRecruitPositionService recruitPositionService;

    @Autowired
    private ISysSmsSendingRecordService sysSmsSendingRecordService;

    @Autowired
    private IRecruitPositionInfoService recruitPositionInfoService;

    @Autowired
    private IRecruitWorkAddressService recruitWorkAddressService;


    /**
     * 查询职位信息列表
     */
    @PreAuthorize("@ss.hasPermi('core:positionInfo:list')")
    @GetMapping("/list")
    public TableDataInfo list(RecruitPositionInfo recruitPositionInfo)
    {
        startPage();
        List<RecruitPositionInfo> list = recruitPositionInfoService.selectRecruitPositionInfoList(recruitPositionInfo);
        list.forEach(e->{
            if(e.getPositionCode() != null && !StringUtils.equals(e.getPositionCode(), "")) {
                e.setPositionName(recruitPositionService.getMap(e.getPositionCode()));
            }
        });
        return getDataTable(list);
    }

    /**
     * 推文查询岗位
     * @param recruitPositionInfo
     * @return
     */
    @GetMapping("/getPostList")
    public TableDataInfo getPostList(RecruitPositionInfo recruitPositionInfo)
    {
        String positionCode = recruitPositionService.getMap2(recruitPositionInfo.getPositionName());
        if(positionCode != null){
            recruitPositionInfo.setPosition(recruitPositionInfo.getPositionName());
        }else {
            recruitPositionInfo.setPositionCode(recruitPositionInfo.getPositionName());
        }
        List<RecruitPositionInfo> list = recruitPositionInfoService.selectRecruitPositionInfoList(recruitPositionInfo);
        list.forEach(e->{
            if(e.getPositionCode() != null && !StringUtils.equals(e.getPositionCode(), "")) {
                e.setPositionName(recruitPositionService.getMap(e.getPositionCode())+"《"+e.getEnterpriseName()+"》");
            }
        });
        return getDataTable(list);
    }
    @PostMapping("/getPostListTwo")
    public TableDataInfo getPostListTwo(@RequestBody RecruitPositionInfo recruitPositionInfo)
    {
        List<RecruitPositionInfo> list = recruitPositionInfoService.selectRecruitPositionInfoList(recruitPositionInfo);
        list.forEach(e -> {
            if (e.getPositionCode() != null && !StringUtils.equals(e.getPositionCode(), "")) {
                e.setPositionName(recruitPositionService.getMap(e.getPositionCode()) + "《" + e.getEnterpriseName() + "》");
            }
        });
        return getDataTable(list);
    }

    /**
     * 导出职位信息列表
     */
    @PreAuthorize("@ss.hasPermi('core:positionInfo:export')")
    @Log(title = "职位信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RecruitPositionInfo recruitPositionInfo)
    {
        List<RecruitPositionInfo> list = recruitPositionInfoService.selectRecruitPositionInfoList(recruitPositionInfo);
        list.forEach(e->{
            if(e.getPositionCode() != null && !StringUtils.equals(e.getPositionCode(), "")) {
                e.setPositionName(recruitPositionService.getMap(e.getPositionCode()));
            }
            //用工地址
            e.setWorkAddress(e.getWorkAddress()+e.getHouseNumber());
        });

        ExcelUtil<RecruitPositionInfo> util = new ExcelUtil<RecruitPositionInfo>(RecruitPositionInfo.class);
        util.exportExcel(response, list, "职位信息数据");
    }

    /**
     * 获取职位信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('core:positionInfo:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        RecruitPositionInfo positionInfo = recruitPositionInfoService.selectRecruitPositionInfoById(id);
        if(positionInfo != null) {
            RecruitWorkAddress workAddress = new RecruitWorkAddress();
            workAddress.setUserId(positionInfo.getPublisherId());
            workAddress.setEnterpriseId(positionInfo.getEnterpriseId());
            List<RecruitWorkAddress> list = recruitWorkAddressService.selectRecruitWorkAddressList(workAddress);
            positionInfo.setWorkAddressList(list);
        }
        return success(positionInfo);
    }

    /**
     * 新增职位信息
     */
    @PreAuthorize("@ss.hasPermi('core:positionInfo:add')")
    @Log(title = "职位信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RecruitPositionInfo recruitPositionInfo)
    {
        int ss = recruitPositionInfoService.insertRecruitPositionInfo(recruitPositionInfo);
        if(ss > 0){
            if(recruitPositionInfo.getWorkAddressId() != null) {
                RecruitWorkAddress workAddress = new RecruitWorkAddress();
                workAddress.setId(recruitPositionInfo.getWorkAddressId());
                workAddress.setWorkAddress(recruitPositionInfo.getWorkAddress());
                workAddress.setHouseNumber(recruitPositionInfo.getHouseNumber());
                recruitWorkAddressService.updateRecruitWorkAddress(workAddress);
            }else {
                RecruitWorkAddress workAddress = new RecruitWorkAddress();
                workAddress.setWorkAddress(recruitPositionInfo.getWorkAddress());
                workAddress.setHouseNumber(recruitPositionInfo.getHouseNumber());
                workAddress.setUserId(recruitPositionInfo.getPublisherId());
                workAddress.setEnterpriseId(recruitPositionInfo.getEnterpriseId());
                recruitWorkAddressService.insertRecruitWorkAddress(workAddress);
                recruitPositionInfo.setWorkAddressId(workAddress.getId());
            }
        }
        ss = recruitPositionInfoService.updateRecruitPositionInfo(recruitPositionInfo);
        if(ss > 0){
            try {
                if (StringUtils.equals(recruitPositionInfo.getPositionStatus(), "1")) {
                    sendAuthenticationMessage(recruitPositionInfo.getPhone(), recruitPositionInfo.getPublisherId());
                }
            }catch (Exception e){
                logger.error("推送失败！");
            }
        }
        return toAjax(ss);
    }

    /**
     * 修改职位信息
     */
    @PreAuthorize("@ss.hasPermi('core:positionInfo:edit')")
    @Log(title = "职位信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RecruitPositionInfo recruitPositionInfo)
    {
        if(recruitPositionInfo.getWorkAddressId() != null) {
            RecruitWorkAddress workAddress = new RecruitWorkAddress();
            workAddress.setId(recruitPositionInfo.getWorkAddressId());
            workAddress.setWorkAddress(recruitPositionInfo.getWorkAddress());
            workAddress.setHouseNumber(recruitPositionInfo.getHouseNumber());
            recruitWorkAddressService.updateRecruitWorkAddress(workAddress);
        }else {
            RecruitWorkAddress workAddress = new RecruitWorkAddress();
            workAddress.setWorkAddress(recruitPositionInfo.getWorkAddress());
            workAddress.setHouseNumber(recruitPositionInfo.getHouseNumber());
            workAddress.setUserId(recruitPositionInfo.getPublisherId());
            workAddress.setEnterpriseId(recruitPositionInfo.getEnterpriseId());
            recruitWorkAddressService.insertRecruitWorkAddress(workAddress);
            recruitPositionInfo.setWorkAddressId(workAddress.getId());
        }
        int ss = recruitPositionInfoService.updateRecruitPositionInfo(recruitPositionInfo);
        if(ss > 0){
            if(StringUtils.equals(recruitPositionInfo.getPositionStatus(), "1")){
                sendAuthenticationMessage(recruitPositionInfo.getPhone(), recruitPositionInfo.getPublisherId());
            }
        }
        return toAjax(ss);
    }


    /**
     * 推送认证消息
     */
    private void sendAuthenticationMessage(String phone, Long userId){
        SysSmsTemplate smsTemplate = sysSmsTemplateService.selectSmsTemplateBySendType("5");
        if(smsTemplate != null){
            notifyService.notifySms(phone, smsTemplate.getTemplateContent(), String.valueOf(smsTemplate.getTemplateId()));

            //记录 职位认证消息 记录
            SysSmsSendingRecord smsSendingRecord = new SysSmsSendingRecord();
            if(userId != null) {
                smsSendingRecord.setUserId(userId);
            }
            smsSendingRecord.setPhone(phone);
            smsSendingRecord.setNoticeTitle(smsTemplate.getTemplateName());
            smsSendingRecord.setNoticeType("1");
            smsSendingRecord.setNoticeContent(smsTemplate.getTemplateContent());
            smsSendingRecord.setSentType("1");
            smsSendingRecord.setSmsTemplateId(smsTemplate.getId());
            smsSendingRecord.setSendTime(new Date());
            sysSmsSendingRecordService.insertSysSmsSendingRecord(smsSendingRecord);
        }
    }

    /**
     * 审核职位信息
     */
    @PreAuthorize("@ss.hasPermi('core:positionInfo:process')")
    @Log(title = "职位信息", businessType = BusinessType.PROCESS)
    @PutMapping("/process")
    public AjaxResult process(@RequestBody RecruitPositionInfo recruitPositionInfo)
    {
        return toAjax(recruitPositionInfoService.updateRecruitPositionInfo(recruitPositionInfo));
    }

    /**
     * 删除职位信息
     */
    @PreAuthorize("@ss.hasPermi('core:positionInfo:remove')")
    @Log(title = "职位信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(recruitPositionInfoService.deleteRecruitPositionInfoByIds(ids));
    }


    /**
     * 导出职位信息模板
     */
    @Log(title = "导出职位信息模板", businessType = BusinessType.EXPORT)
    @GetMapping("/importTemplate")
    public AjaxResult importTemplate()
    {
        ExcelUtil<RecruitPositionInfo> util = new ExcelUtil<>(RecruitPositionInfo.class);
        return util.importTemplateExcel("职位信息模板");
    }


    /**
     * 导入职位信息
     * @param file
     * @param updateSupport
     * @return
     * @throws Exception
     */
    @Log(title = "导入职位信息", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<RecruitPositionInfo> util = new ExcelUtil<>(RecruitPositionInfo.class);
        List<RecruitPositionInfo> lists = util.importExcel(file.getInputStream());
        return success(recruitPositionInfoService.importData(lists));
    }

}
