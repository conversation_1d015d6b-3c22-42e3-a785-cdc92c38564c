package com.recruit.core.domain.request.resume;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * @Auther: <PERSON> kong
 * @Date: 2023/3/21 8:54
 * @Description:
 */
@Data
@ApiModel("工作经历1")
public class ResumeWorkHistoryRequest {

    @ApiModelProperty("id")
    private Long id;

    @NotBlank(message = "单位名称不能为空")
    @ApiModelProperty("单位名称")
    private String unitName;

    @NotBlank(message = "担任职位不能为空")
    @ApiModelProperty("担任职位")
    private String position;

    @ApiModelProperty("入职时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date entryTime;

    @ApiModelProperty("离职时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date leavedate;

    @ApiModelProperty("至今 0否 1是")
    private String upToNow;


    @ApiModelProperty("工作描述")
    private String jobDescription;

}
