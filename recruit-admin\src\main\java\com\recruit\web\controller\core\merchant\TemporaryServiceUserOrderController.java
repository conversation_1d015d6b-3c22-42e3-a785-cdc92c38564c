package com.recruit.web.controller.core.merchant;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.recruit.common.utils.StringUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.recruit.common.annotation.Log;
import com.recruit.common.core.controller.BaseController;
import com.recruit.common.core.domain.AjaxResult;
import com.recruit.common.enums.BusinessType;
import org.springframework.web.multipart.MultipartFile;
import com.recruit.core.domain.TemporaryServiceUserOrder;
import com.recruit.core.service.ITemporaryServiceUserOrderService;
import com.recruit.common.utils.poi.ExcelUtil;
import com.recruit.common.core.page.TableDataInfo;

/**
 * 用户订单Controller
 *
 * <AUTHOR>
 * @date 2023-06-02
 */
@RestController
@RequestMapping("/core/serviceUserOrder")
public class TemporaryServiceUserOrderController extends BaseController
{
    @Autowired
    private ITemporaryServiceUserOrderService temporaryServiceUserOrderService;

    /**
     * 查询用户订单列表
     */
    @PreAuthorize("@ss.hasPermi('core:serviceUserOrder:list')")
    @GetMapping("/list")
    public TableDataInfo list(TemporaryServiceUserOrder temporaryServiceUserOrder)
    {
        startPage();
        List<TemporaryServiceUserOrder> list = temporaryServiceUserOrderService.selectTemporaryServiceUserOrderList(temporaryServiceUserOrder);
        list.forEach(e->{
            if(e.getCommentStatus() == null || StringUtils.equals(e.getCommentStatus(), "")){
                e.setCommentStatus("0");
            }
        });
        return getDataTable(list);
    }

    /**
     * 导出用户订单列表
     */
    @PreAuthorize("@ss.hasPermi('core:serviceUserOrder:export')")
    @Log(title = "用户订单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TemporaryServiceUserOrder temporaryServiceUserOrder)
    {
        List<TemporaryServiceUserOrder> list = temporaryServiceUserOrderService.selectTemporaryServiceUserOrderList(temporaryServiceUserOrder);
        ExcelUtil<TemporaryServiceUserOrder> util = new ExcelUtil<TemporaryServiceUserOrder>(TemporaryServiceUserOrder.class);
        util.exportExcel(response, list, "用户订单数据");
    }

    /**
     * 获取用户订单详细信息
     */
    @PreAuthorize("@ss.hasPermi('core:serviceUserOrder:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(temporaryServiceUserOrderService.selectTemporaryServiceUserOrderById(id));
    }

    /**
     * 新增用户订单
     */
    @PreAuthorize("@ss.hasPermi('core:serviceUserOrder:add')")
    @Log(title = "用户订单", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TemporaryServiceUserOrder temporaryServiceUserOrder)
    {
        return toAjax(temporaryServiceUserOrderService.insertTemporaryServiceUserOrder(temporaryServiceUserOrder));
    }

    /**
     * 修改用户订单
     */
    @PreAuthorize("@ss.hasPermi('core:serviceUserOrder:edit')")
    @Log(title = "用户订单", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TemporaryServiceUserOrder temporaryServiceUserOrder)
    {
        return toAjax(temporaryServiceUserOrderService.updateTemporaryServiceUserOrder(temporaryServiceUserOrder));
    }

    /**
     * 用户订单派单
     */
    @PreAuthorize("@ss.hasPermi('core:serviceUserOrder:edit')")
    @Log(title = "用户订单派单", businessType = BusinessType.UPDATE)
    @PutMapping("/dispatch")
    public AjaxResult dispatch(@RequestBody TemporaryServiceUserOrder temporaryServiceUserOrder)
    {
        //判断是否派单
        if(temporaryServiceUserOrder.getServiceUserSubOrderList().size() == 0){
            throw new RuntimeException("未添加商户！");
        }
        return toAjax(temporaryServiceUserOrderService.updateTemporaryServiceUserOrderTwo(temporaryServiceUserOrder));
    }

    /**
     * 删除用户订单
     */
    @PreAuthorize("@ss.hasPermi('core:serviceUserOrder:remove')")
    @Log(title = "用户订单", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(temporaryServiceUserOrderService.deleteTemporaryServiceUserOrderByIds(ids));
    }


    /**
     * 导入用户订单
     * @param file
     * @param updateSupport
     * @return
     * @throws Exception
     */
    @PreAuthorize("@ss.hasPermi('core:serviceUserOrder:import')")
    @Log(title = "用户订单", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<TemporaryServiceUserOrder> util = new ExcelUtil<>(TemporaryServiceUserOrder.class);
        List<TemporaryServiceUserOrder> lists = util.importExcel(file.getInputStream());
        String message = temporaryServiceUserOrderService.importTemporaryServiceUserOrder(lists, updateSupport);
        return AjaxResult.success(message);
    }


}
