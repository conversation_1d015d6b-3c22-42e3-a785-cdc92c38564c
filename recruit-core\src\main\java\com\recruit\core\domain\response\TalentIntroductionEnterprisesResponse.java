package com.recruit.core.domain.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Auther: <PERSON> kong
 * @Date: 2023/5/11 18:10
 * @Description:
 */
@Data
@ApiModel("人才引进企业信息2")
public class TalentIntroductionEnterprisesResponse {

    @ApiModelProperty("单位名称")
    private String nameOfEmployer;

    @ApiModelProperty("人才引进id")
    private Long talentIntroductionId;
}
