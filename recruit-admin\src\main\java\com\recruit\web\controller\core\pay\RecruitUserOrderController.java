package com.recruit.web.controller.core.pay;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.recruit.common.annotation.Log;
import com.recruit.common.core.controller.BaseController;
import com.recruit.common.core.domain.AjaxResult;
import com.recruit.common.enums.BusinessType;
import org.springframework.web.multipart.MultipartFile;
import com.recruit.core.domain.RecruitUserOrder;
import com.recruit.core.service.IRecruitUserOrderService;
import com.recruit.common.utils.poi.ExcelUtil;
import com.recruit.common.core.page.TableDataInfo;

/**
 * 用户订单Controller
 *
 * <AUTHOR>
 * @date 2023-05-22
 */
@RestController
@RequestMapping("/core/userOrder")
public class RecruitUserOrderController extends BaseController
{
    @Autowired
    private IRecruitUserOrderService recruitUserOrderService;

    /**
     * 查询用户订单列表
     */
    @PreAuthorize("@ss.hasPermi('core:userOrder:list')")
    @GetMapping("/list")
    public TableDataInfo list(RecruitUserOrder recruitUserOrder)
    {
        startPage();
        List<RecruitUserOrder> list = recruitUserOrderService.selectRecruitUserOrderList(recruitUserOrder);
        return getDataTable(list);
    }

    /**
     * 导出用户订单列表
     */
    @PreAuthorize("@ss.hasPermi('core:userOrder:export')")
    @Log(title = "用户订单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RecruitUserOrder recruitUserOrder)
    {
        List<RecruitUserOrder> list = recruitUserOrderService.selectRecruitUserOrderList(recruitUserOrder);
        ExcelUtil<RecruitUserOrder> util = new ExcelUtil<RecruitUserOrder>(RecruitUserOrder.class);
        util.exportExcel(response, list, "用户订单数据");
    }

    /**
     * 获取用户订单详细信息
     */
    @PreAuthorize("@ss.hasPermi('core:userOrder:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(recruitUserOrderService.selectRecruitUserOrderById(id));
    }

    /**
     * 新增用户订单
     */
    @PreAuthorize("@ss.hasPermi('core:userOrder:add')")
    @Log(title = "用户订单", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RecruitUserOrder recruitUserOrder)
    {
        return toAjax(recruitUserOrderService.insertRecruitUserOrder(recruitUserOrder));
    }

    /**
     * 修改用户订单
     */
    @PreAuthorize("@ss.hasPermi('core:userOrder:edit')")
    @Log(title = "用户订单", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RecruitUserOrder recruitUserOrder)
    {
        return toAjax(recruitUserOrderService.updateRecruitUserOrder(recruitUserOrder));
    }

    /**
     * 删除用户订单
     */
    @PreAuthorize("@ss.hasPermi('core:userOrder:remove')")
    @Log(title = "用户订单", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(recruitUserOrderService.deleteRecruitUserOrderByIds(ids));
    }


    /**
     * 导入用户订单
     * @param file
     * @param updateSupport
     * @return
     * @throws Exception
     */
    @PreAuthorize("@ss.hasPermi('core:userOrder:import')")
    @Log(title = "用户订单", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<RecruitUserOrder> util = new ExcelUtil<>(RecruitUserOrder.class);
        List<RecruitUserOrder> lists = util.importExcel(file.getInputStream());
        String message = recruitUserOrderService.importRecruitUserOrder(lists, updateSupport);
        return AjaxResult.success(message);
    }


}
