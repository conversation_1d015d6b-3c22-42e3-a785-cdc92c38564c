package com.recruit.core.domain;

import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.recruit.common.annotation.Excel;
import com.recruit.common.core.domain.BaseEntity;
import com.recruit.core.domain.response.OnlineJobFairsResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 网络招聘会对象 recruit_online_job_fairs
 *
 * <AUTHOR>
 * @date 2023-05-03
 */
@Data
@ApiModel("网络招聘会")
public class RecruitOnlineJobFairs extends BaseEntity
{
    private static final long serialVersionUID = 1L;


    private Long id;


    @Excel(name = "期数")
    @ApiModelProperty("期数")
    private String numberOfPeriods;

    @ApiModelProperty("小程序图片")
    private String appPicUrl;

    @ApiModelProperty("PC图片")
    private String pcPicUrl;


    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "开始时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("开始时间")
    private Date startTime;


    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "结束时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("结束时间")
    private Date endTime;


    @Excel(name = "状态")
    @ApiModelProperty("状态")
    private String state;

    private Long total;


    private List<OnlineJobFairsResponse> onlineJobFairsList;
}
