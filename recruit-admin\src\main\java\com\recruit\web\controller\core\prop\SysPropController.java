package com.recruit.web.controller.core.prop;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.recruit.common.annotation.Log;
import com.recruit.common.core.controller.BaseController;
import com.recruit.common.core.domain.AjaxResult;
import com.recruit.common.enums.BusinessType;
import org.springframework.web.multipart.MultipartFile;
import com.recruit.core.domain.SysProp;
import com.recruit.core.service.ISysPropService;
import com.recruit.common.utils.poi.ExcelUtil;
import com.recruit.common.core.page.TableDataInfo;

/**
 * 系统道具Controller
 *
 * <AUTHOR>
 * @date 2023-05-24
 */
@RestController
@RequestMapping("/core/sysProp")
public class SysPropController extends BaseController
{
    @Autowired
    private ISysPropService sysPropService;

    /**
     * 查询系统道具列表
     */
    @PreAuthorize("@ss.hasPermi('core:sysProp:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysProp sysProp)
    {
        startPage();
        List<SysProp> list = sysPropService.selectSysPropList(sysProp);
        return getDataTable(list);
    }

    /**
     * 导出系统道具列表
     */
    @PreAuthorize("@ss.hasPermi('core:sysProp:export')")
    @Log(title = "系统道具", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysProp sysProp)
    {
        List<SysProp> list = sysPropService.selectSysPropList(sysProp);
        ExcelUtil<SysProp> util = new ExcelUtil<SysProp>(SysProp.class);
        util.exportExcel(response, list, "系统道具数据");
    }

    /**
     * 获取系统道具详细信息
     */
    @PreAuthorize("@ss.hasPermi('core:sysProp:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(sysPropService.selectSysPropById(id));
    }

    /**
     * 新增系统道具
     */
    @PreAuthorize("@ss.hasPermi('core:sysProp:add')")
    @Log(title = "系统道具", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SysProp sysProp)
    {
        return toAjax(sysPropService.insertSysProp(sysProp));
    }

    /**
     * 修改系统道具
     */
    @PreAuthorize("@ss.hasPermi('core:sysProp:edit')")
    @Log(title = "系统道具", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SysProp sysProp)
    {
        return toAjax(sysPropService.updateSysProp(sysProp));
    }

    /**
     * 删除系统道具
     */
    @PreAuthorize("@ss.hasPermi('core:sysProp:remove')")
    @Log(title = "系统道具", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(sysPropService.deleteSysPropByIds(ids));
    }


    /**
     * 导入系统道具
     * @param file
     * @param updateSupport
     * @return
     * @throws Exception
     */
    @PreAuthorize("@ss.hasPermi('core:sysProp:import')")
    @Log(title = "系统道具", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<SysProp> util = new ExcelUtil<>(SysProp.class);
        List<SysProp> lists = util.importExcel(file.getInputStream());
        String message = sysPropService.importSysProp(lists, updateSupport);
        return AjaxResult.success(message);
    }


}
