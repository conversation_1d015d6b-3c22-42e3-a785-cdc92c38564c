package com.recruit.web.controller.core.other;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.recruit.common.annotation.Log;
import com.recruit.common.core.controller.BaseController;
import com.recruit.common.core.domain.AjaxResult;
import com.recruit.common.enums.BusinessType;
import com.recruit.core.domain.RecruitInterviewInfo;
import com.recruit.core.service.IRecruitInterviewInfoService;
import com.recruit.common.utils.poi.ExcelUtil;
import com.recruit.common.core.page.TableDataInfo;

/**
 * 面试邀请Controller
 *
 * <AUTHOR>
 * @date 2023-04-23
 */
@RestController
@RequestMapping("/core/InterviewInfo")
public class RecruitInterviewInfoController extends BaseController
{
    @Autowired
    private IRecruitInterviewInfoService recruitInterviewInfoService;

    /**
     * 查询面试邀请列表
     */
    @PreAuthorize("@ss.hasPermi('core:InterviewInfo:list')")
    @GetMapping("/list")
    public TableDataInfo list(RecruitInterviewInfo recruitInterviewInfo)
    {
        startPage();
        List<RecruitInterviewInfo> list = recruitInterviewInfoService.selectRecruitInterviewInfoList(recruitInterviewInfo);
        return getDataTable(list);
    }

    /**
     * 导出面试邀请列表
     */
    @PreAuthorize("@ss.hasPermi('core:InterviewInfo:export')")
    @Log(title = "面试邀请", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RecruitInterviewInfo recruitInterviewInfo)
    {
        List<RecruitInterviewInfo> list = recruitInterviewInfoService.selectRecruitInterviewInfoList(recruitInterviewInfo);
        ExcelUtil<RecruitInterviewInfo> util = new ExcelUtil<RecruitInterviewInfo>(RecruitInterviewInfo.class);
        util.exportExcel(response, list, "面试邀请数据");
    }

    /**
     * 获取面试邀请详细信息
     */
    @PreAuthorize("@ss.hasPermi('core:InterviewInfo:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(recruitInterviewInfoService.selectRecruitInterviewInfoById(id));
    }

    /**
     * 新增面试邀请
     */
    @PreAuthorize("@ss.hasPermi('core:InterviewInfo:add')")
    @Log(title = "面试邀请", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RecruitInterviewInfo recruitInterviewInfo)
    {
        return toAjax(recruitInterviewInfoService.insertRecruitInterviewInfo(recruitInterviewInfo));
    }

    /**
     * 修改面试邀请
     */
    @PreAuthorize("@ss.hasPermi('core:InterviewInfo:edit')")
    @Log(title = "面试邀请", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RecruitInterviewInfo recruitInterviewInfo)
    {
        return toAjax(recruitInterviewInfoService.updateRecruitInterviewInfo(recruitInterviewInfo));
    }

    /**
     * 删除面试邀请
     */
    @PreAuthorize("@ss.hasPermi('core:InterviewInfo:remove')")
    @Log(title = "面试邀请", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(recruitInterviewInfoService.deleteRecruitInterviewInfoByIds(ids));
    }
}
