package com.recruit.core.domain.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Auther: <PERSON> kong
 * @Date: 2023/3/24 17:19
 * @Description:
 */
@Data
@ApiModel("营业执照校验信息返回")
public class OcrSampleResponse {

    @ApiModelProperty("识别结果")
    private String words_result;

    @ApiModelProperty("图像方向。\n" +
            "- - 1：未定义，\n" +
            "- 0：正向，\n" +
            "- 1：逆时针90度，\n" +
            "- 2：逆时针180度，\n" +
            "- 3：逆时针270度")
    private String direction;

    @ApiModelProperty("识别结果数，表示words_result的元素个数")
    private String words_result_num;

    @ApiModelProperty("请求标识码，随机数，唯一。")
    private String log_id;

    @ApiModelProperty("错误信息")
    private String error_msg;

    @ApiModelProperty("错误码")
    private String error_code;
}
