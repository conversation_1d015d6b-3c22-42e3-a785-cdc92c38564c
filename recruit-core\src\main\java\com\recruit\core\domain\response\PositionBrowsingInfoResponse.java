package com.recruit.core.domain.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.recruit.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @Auther: <PERSON> kong
 * @Date: 2023/4/12 13:39
 * @Description:
 */
@Data
@ApiModel("查看职位浏览信息")
public class PositionBrowsingInfoResponse {

    private Long id;

    @Excel(name = "职位id")
    @ApiModelProperty("职位id")
    private Long positionInfoId;


    @ApiModelProperty("职位")
    private String position;

    @Excel(name = "企业id")
    @ApiModelProperty("企业id")
    private Long enterpriseId;

    @ApiModelProperty("企业名称")
    private String enterpriseName;

    @ApiModelProperty("发布人id")
    private Long publisherId;


    @Excel(name = "用户id")
    @ApiModelProperty("用户id")
    private Long userId;


    @ApiModelProperty("类型 1 求职者，2招聘者")
    private String type;

    @ApiModelProperty("头像")
    private String headSculpture;

    @ApiModelProperty("用户名称")
    private String userName;

    @ApiModelProperty("职位编码")
    private String positionCode;

    @ApiModelProperty("职位编码名称")
    private String positionName;

    @ApiModelProperty("工作经验")
    private String workExperience;

    @ApiModelProperty("工作经验名称")
    private String workExperienceName;

    @ApiModelProperty("最低学历")
    private String minimumEducation;

    @ApiModelProperty("最低学历名称")
    private String minimumEducationName;

    @ApiModelProperty("最高薪资")
    private String maximumSalary;

    @ApiModelProperty("最低薪资")
    private String minimumWage;

    @ApiModelProperty("公司福利")
    private String materialBenefits;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
}
