package com.recruit.webApi;

import com.recruit.common.core.controller.BaseController;
import com.recruit.common.core.domain.AjaxResult;
import com.recruit.common.core.domain.entity.SysDictData;
import com.recruit.common.core.domain.entity.SysDictType;
import com.recruit.common.core.page.TableDataInfo;
import com.recruit.common.utils.DateUtils;
import com.recruit.common.utils.SalaryUtils;
import com.recruit.common.utils.StringUtils;
import com.recruit.core.domain.RecruitAdvert;
import com.recruit.core.domain.RecruitArea;
import com.recruit.core.domain.RecruitOnlineJobFairs;
import com.recruit.core.domain.RecruitTalentIntroduction;
import com.recruit.core.domain.SysEmote;
import com.recruit.core.domain.SysLiveAdHoc;
import com.recruit.core.domain.response.SalaryRequirementsResponse;
import com.recruit.core.service.IRecruitAdvertService;
import com.recruit.core.service.IRecruitAreaService;
import com.recruit.core.service.IRecruitOnlineJobFairsService;
import com.recruit.core.service.IRecruitTalentIntroductionService;
import com.recruit.core.service.ISysEmoteService;
import com.recruit.core.service.ISysLiveAdHocService;
import com.recruit.system.service.ISysDictTypeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import javax.validation.constraints.NotBlank;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @Auther: Wu kong
 * @Date: 2023/3/19 9:08
 * @Description:
 */
@Api(tags= "公共接口")
@RestController
@RequestMapping("/webApi/common")
public class CommonsController extends BaseController {


    @Autowired
    private ISysEmoteService sysEmoteService;

    @Autowired
    private ISysDictTypeService dictTypeService;

    @Autowired
    private IRecruitAreaService recruitAreaService;

    @Autowired
    private ISysLiveAdHocService sysLiveAdHocService;

    @Autowired
    private IRecruitAdvertService recruitAdvertService;

    @Autowired
    private IRecruitOnlineJobFairsService recruitOnlineJobFairsService;

    @Autowired
    private IRecruitTalentIntroductionService recruitTalentIntroductionService;



    @ApiOperation("查询广告图片 type 0小程序 1PC端, seat 字段 0banner,1左下，2右下 左下右下PC端专用")
    @GetMapping("/getAdvert")
    public TableDataInfo getAdvert(@RequestParam String type)
    {
        RecruitAdvert recruitAdvert = new RecruitAdvert();
        recruitAdvert.setType(type);
        recruitAdvert.setStatus("0");
        recruitAdvert.setSeat("0");
        recruitAdvert.setStartTime(DateUtils.dateTime(DateUtils.YYYY_MM_DD, DateUtils.getDate()));
        recruitAdvert.setEndTime(DateUtils.dateTime(DateUtils.YYYY_MM_DD, DateUtils.getDate()));

        List<RecruitAdvert> list = recruitAdvertService.selectRecruitAdvertListTwo(recruitAdvert);
        if(StringUtils.equals(type, "1")) {
            recruitAdvert.setSeat("1");
            startPageTwo(1, 1);
            list.addAll(recruitAdvertService.selectRecruitAdvertListTwo(recruitAdvert));
            recruitAdvert.setSeat("2");
            startPageTwo(1, 1);
            list.addAll(recruitAdvertService.selectRecruitAdvertListTwo(recruitAdvert));
        }

        //网络招聘会
        RecruitOnlineJobFairs onlineJobFairs = recruitOnlineJobFairsService.selectRecruitOnlineJobFairs();
        if (onlineJobFairs != null) {
            if (StringUtils.equals(type, "1")) {
                RecruitAdvert adverts = new RecruitAdvert();
                adverts.setId(onlineJobFairs.getId());
                adverts.setType("1");
                adverts.setJobFairs("Y");
                adverts.setSeat("0");
                adverts.setStatus("0");
                adverts.setPicUrl(onlineJobFairs.getPcPicUrl());
                adverts.setStartTime(onlineJobFairs.getStartTime());
                adverts.setEndTime(onlineJobFairs.getEndTime());
                adverts.setNumberOfPeriods(onlineJobFairs.getNumberOfPeriods());
                list.add(adverts);
            }
        }

        //查询直播
        SysLiveAdHoc  liveAdHoc = sysLiveAdHocService.selectSysLiveAdHoc();
        if(liveAdHoc != null){
            if (StringUtils.equals(type, "0")) {
                RecruitAdvert advert = new RecruitAdvert();
                advert.setId(liveAdHoc.getId());
                advert.setType("0");
                advert.setLiveAdHoc("Y");
                advert.setSeat("0");
                advert.setStatus("0");
                advert.setLinkUrl(liveAdHoc.getLiveAddress());
                advert.setPicUrl(liveAdHoc.getAppPicUrl());
                advert.setStartTime(liveAdHoc.getStartTime());
                advert.setEndTime(liveAdHoc.getEndTime());
                advert.setNumberOfPeriods(liveAdHoc.getTitle());
                list.add(advert);
            } else if (StringUtils.equals(type, "1")) {
                RecruitAdvert adverts = new RecruitAdvert();
                adverts.setId(liveAdHoc.getId());
                adverts.setType("1");
                adverts.setLiveAdHoc("Y");
                adverts.setSeat("0");
                adverts.setStatus("0");
                adverts.setLinkUrl(liveAdHoc.getLiveAddress());
                adverts.setPicUrl(liveAdHoc.getPcPicUrl());
                adverts.setStartTime(liveAdHoc.getStartTime());
                adverts.setEndTime(liveAdHoc.getEndTime());
                adverts.setNumberOfPeriods(liveAdHoc.getTitle());
                list.add(adverts);
            }
        }

        RecruitTalentIntroduction recruitTalentIntroduction = new RecruitTalentIntroduction();
        recruitTalentIntroduction.setStartTime( DateUtils.getNowDate());
        recruitTalentIntroduction.setEndTime( DateUtils.getNowDate());
        //人才引进
        RecruitTalentIntroduction talentIntroduction = recruitTalentIntroductionService.selectRecruitTalentIntroduction(recruitTalentIntroduction);
        if (talentIntroduction != null) {
            if (StringUtils.equals(type, "1")) {
                RecruitAdvert adverts = new RecruitAdvert();
                adverts.setId(talentIntroduction.getId());
                adverts.setType("1");
                adverts.setTalentIntroduction("Y");
                adverts.setSeat("0");
                adverts.setStatus("0");
                adverts.setPicUrl(talentIntroduction.getPcPicUrl());
                adverts.setStartTime(talentIntroduction.getStartTime());
                adverts.setEndTime(talentIntroduction.getEndTime());
                adverts.setNumberOfPeriods(talentIntroduction.getNumberOfPeriods());
                list.add(adverts);
            }
        }

        return getDataTable(list);
    }

    @ApiOperation("查询人才引进图片 type 0小程序")
    @GetMapping("/gettalentIntroduction")
    public AjaxResult gettalentIntroduction(@RequestParam String type)
    {
        RecruitTalentIntroduction recruitTalentIntroduction = new RecruitTalentIntroduction();
        recruitTalentIntroduction.setStartTime( DateUtils.dateTime(DateUtils.YYYY_MM_DD_HH_MM_SS, DateUtils.getTime()));
        recruitTalentIntroduction.setEndTime( DateUtils.dateTime(DateUtils.YYYY_MM_DD_HH_MM_SS, DateUtils.getTime()));
        //人才引进
        RecruitTalentIntroduction talentIntroduction = recruitTalentIntroductionService.selectRecruitTalentIntroduction(recruitTalentIntroduction);
        RecruitAdvert adverts = new RecruitAdvert();
        if (talentIntroduction != null) {
            if (StringUtils.equals(type, "0")) {
                adverts.setId(talentIntroduction.getId());
                adverts.setType("0");
                adverts.setTalentIntroduction("Y");
                adverts.setSeat("0");
                adverts.setStatus("0");
                adverts.setPicUrl(talentIntroduction.getAppPicUrl());
                adverts.setStartTime(talentIntroduction.getStartTime());
                adverts.setEndTime(talentIntroduction.getEndTime());
                adverts.setNumberOfPeriods(talentIntroduction.getNumberOfPeriods());
            } else if (StringUtils.equals(type, "1")) {
                adverts.setId(talentIntroduction.getId());
                adverts.setType("1");
                adverts.setTalentIntroduction("Y");
                adverts.setSeat("0");
                adverts.setStatus("0");
                adverts.setPicUrl(talentIntroduction.getPcPicUrl());
                adverts.setStartTime(talentIntroduction.getStartTime());
                adverts.setEndTime(talentIntroduction.getEndTime());
                adverts.setNumberOfPeriods(talentIntroduction.getNumberOfPeriods());
            }
        }
        return success(adverts);
    }

    @ApiOperation("数据字典类型")
    @GetMapping("/getDictType")
    public TableDataInfo getDictType()
    {
        List<SysDictType> list = dictTypeService.selectDictTypeAll();
        return getDataTable(list);
    }

    @ApiOperation("数据字典信息")
    @GetMapping("/getDictData")
    public TableDataInfo getDictData(@NotBlank(message = "类型不能为空") String dictType)
    {
        List<SysDictData> list = dictTypeService.selectDictDataByType(dictType);
        if(StringUtils.equals(dictType, "appointment_time")){
            list.forEach(s->{
                String[] aa = s.getDictLabel().split("-");
                long diff = 0;
                SimpleDateFormat sd = new SimpleDateFormat("yyyy-MM-dd HH:mm");
                long nd = 1800000;// 半小时的毫秒数
                Date now = new Date();
                Date start = null;
                try {
                    String sss = DateUtils.getDate()+" "+aa[1];
                    start = sd.parse(sss);
                    diff = now.getTime() - start.getTime();
                    if (diff > nd) {
                        s.setBecomeDue("1");
                    } else {
                        s.setBecomeDue("0");
                    }
                } catch (ParseException e) {
                    throw new RuntimeException(e);
                }
            });
        }
        return getDataTable(list);
    }

    @ApiOperation("查询工作区域")
    @GetMapping("/getAreaList")
    public TableDataInfo getAreaList()
    {
        List<RecruitArea> list = recruitAreaService.selectRecruitAreaList(new RecruitArea());
        return getDataTable(list);
    }


    @ApiOperation("获取表情列表")
    @GetMapping("/getEmoteList")
    public TableDataInfo getEmoteList(){
        return getDataTable(sysEmoteService.selectSysEmoteList(new SysEmote()));
    }


    @ApiOperation("获取薪资要求")
    @GetMapping("/getSalaryRequirements")
    public TableDataInfo getSalaryRequirements(){
        List<Map<String, List<String>>> listMap = SalaryUtils.getSalaryRequirements();
        List<SalaryRequirementsResponse> list = new ArrayList<>();
        //循环遍历到list中去
        for( Map<String, List<String>> mapList : listMap ) {
            for( String key : mapList.keySet() ) {
                List<String> ss = mapList.get(key);
                SalaryRequirementsResponse response = new SalaryRequirementsResponse();
                response.setSalary(key);
                List<SalaryRequirementsResponse> list1 = new ArrayList<>();
                ss.forEach(s->{
                    SalaryRequirementsResponse response1 = new SalaryRequirementsResponse();
                    response1.setSalary(s);
                    list1.add(response1);
                });
                response.setSalaryList(list1);
                list.add(response);
            }
        }
        return getDataTable(list);
    }




}
