package com.recruit.core.domain.request.wx;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import me.chanjar.weixin.mp.bean.draft.WxMpDraftArticles;

import java.util.List;

@Data
public class WxDraftRequest {

    public String mediaId;

    public List<WxMpDraftArticles> articles;

    @ApiModelProperty("企业id")
    private String enterpriseInfoId;

    @ApiModelProperty("职位id")
    private String positionInfoId;

    @ApiModelProperty("推文类型")
    private String tweetType;

    @ApiModelProperty("公众号模板")
    private String publicTemplateType;

    @ApiModelProperty("模板类型")
    private String templateType;

    @ApiModelProperty("图文消息的内容")
    private String content;

    //职位id
    private Long[] positionInfoIds;

    //企业id
    private Long[] enterpriseInfoIds;
}
