package com.recruit.core.domain;

import com.recruit.common.annotation.Excel;
import com.recruit.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 企业认证管理对象 recruit_attestation_manage
 *
 * <AUTHOR>
 * @date 2023-05-04
 */
@Data
@ApiModel("企业认证管理")
public class RecruitAttestationManage extends BaseEntity
{
    private static final long serialVersionUID = 1L;


    private Long id;


    @Excel(name = "企业id")
    @ApiModelProperty("企业id")
    private Long enterpriseId;


    @Excel(name = "用户id")
    @ApiModelProperty("用户id")
    private Long userId;


    @Excel(name = "证明报告")
    @ApiModelProperty("证明报告")
    private String attestationReportUrl;


    @Excel(name = "认证状态")
    @ApiModelProperty("认证状态")
    private String attestationStatus;

    @ApiModelProperty("用户姓名")
    private String userName;

    @ApiModelProperty("企业名称")
    private String enterpriseName;


}
