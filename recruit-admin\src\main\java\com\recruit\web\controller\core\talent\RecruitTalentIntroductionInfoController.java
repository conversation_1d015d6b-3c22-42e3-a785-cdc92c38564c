package com.recruit.web.controller.core.talent;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.recruit.common.core.domain.entity.SysRegion;
import com.recruit.common.core.domain.entity.SysUser;
import com.recruit.common.utils.SecurityUtils;
import com.recruit.common.utils.StringUtils;
import com.recruit.core.service.ISysRegionService;
import com.recruit.system.service.ISysConfigService;
import com.recruit.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.recruit.common.annotation.Log;
import com.recruit.common.core.controller.BaseController;
import com.recruit.common.core.domain.AjaxResult;
import com.recruit.common.enums.BusinessType;
import com.recruit.core.domain.RecruitTalentIntroductionInfo;
import com.recruit.core.service.IRecruitTalentIntroductionInfoService;
import com.recruit.common.utils.poi.ExcelUtil;
import com.recruit.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;
import com.recruit.core.domain.response.TalentIntroductionStatisticsDTO;

/**
 * 人才引进信息Controller
 *
 * <AUTHOR>
 * @date 2023-05-11
 */
@RestController
@RequestMapping("/core/talentIntroductionInfo")
public class RecruitTalentIntroductionInfoController extends BaseController
{
    @Autowired
    private ISysUserService userService;

    @Autowired
    private ISysConfigService configService;

    @Autowired
    private ISysRegionService sysRegionService;

    @Autowired
    private IRecruitTalentIntroductionInfoService recruitTalentIntroductionInfoService;

    /**
     * 查询人才引进信息列表
     */
    @GetMapping("/list")
    public TableDataInfo list(RecruitTalentIntroductionInfo recruitTalentIntroductionInfo)
    {
        startPage();
        List<RecruitTalentIntroductionInfo> list = recruitTalentIntroductionInfoService.selectRecruitTalentIntroductionInfoList(recruitTalentIntroductionInfo);
        return getDataTable(list);
    }

    /**
     * 导出人才引进信息列表
     */
    @Log(title = "人才引进信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RecruitTalentIntroductionInfo recruitTalentIntroductionInfo)
    {
        List<RecruitTalentIntroductionInfo> list = recruitTalentIntroductionInfoService.selectRecruitTalentIntroductionInfoList(recruitTalentIntroductionInfo);
        ExcelUtil<RecruitTalentIntroductionInfo> util = new ExcelUtil<RecruitTalentIntroductionInfo>(RecruitTalentIntroductionInfo.class);
        util.exportExcel(response, list, "人才引进信息数据");
    }

    /**
     * 获取人才引进信息详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        RecruitTalentIntroductionInfo talentIntroductionInfo = recruitTalentIntroductionInfoService.selectRecruitTalentIntroductionInfoById(id);
        if(talentIntroductionInfo.getRegion() != null && !StringUtils.equals(talentIntroductionInfo.getRegion(), "")){
            List<String> regions = new ArrayList<>(Arrays.asList(talentIntroductionInfo.getRegion().split(",")));
            talentIntroductionInfo.setRegions(regions);
        }
        return success(talentIntroductionInfo);
    }

    /**
     * 新增人才引进信息
     */
    @Log(title = "人才引进信息", businessType = BusinessType.INSERT)
    @PostMapping
    @Transactional
    public AjaxResult add(@RequestBody RecruitTalentIntroductionInfo recruitTalentIntroductionInfo)
    {
        //设置省市区
        region(recruitTalentIntroductionInfo);
        //插入默认人才引进单位默认用户信息
        insertDefaultUser(recruitTalentIntroductionInfo);
        return toAjax(recruitTalentIntroductionInfoService.insertRecruitTalentIntroductionInfo(recruitTalentIntroductionInfo));
    }

    /**
     * 修改人才引进信息
     */
    @Log(title = "人才引进信息", businessType = BusinessType.UPDATE)
    @PutMapping
    @Transactional
    public AjaxResult edit(@RequestBody RecruitTalentIntroductionInfo talentIntroductionInfo)
    {
        //设置省市区
        region(talentIntroductionInfo);
        //插入默认人才引进单位默认用户信息
        insertDefaultUser(talentIntroductionInfo);
        return toAjax(recruitTalentIntroductionInfoService.updateRecruitTalentIntroductionInfo(talentIntroductionInfo));
    }

    /**
     * 插入默认人才引进单位默认用户信息
     * @param talentIntroductionInfo
     * @return
     */
    public RecruitTalentIntroductionInfo insertDefaultUser(RecruitTalentIntroductionInfo talentIntroductionInfo){
        SysUser sysUser = userService.selectUserByUserName(talentIntroductionInfo.getNameOfEmployer());
        if(sysUser != null){
            talentIntroductionInfo.setUserId(sysUser.getUserId());
        }else {
            SysUser sysUser2 = new SysUser();
            //部门编号
            sysUser2.setDeptId(114L);
            //登录名称
            sysUser2.setUserName(talentIntroductionInfo.getNameOfEmployer());
            //用户名称
            sysUser2.setNickName(talentIntroductionInfo.getNameOfEmployer());
            //性别
            sysUser2.setSex("0");
            //密码
            String password = configService.selectConfigByKey("sys.user.initPassword");
            sysUser2.setPassword(SecurityUtils.encryptPassword(password));
            //状态
            sysUser2.setStatus("0");
            //创建人
            sysUser2.setCreateBy("admin");
            //角色组
            Long[] array2 = new Long[1];
            Arrays.fill(array2, 5L); // 将数组所有元素都填充为10
            sysUser2.setRoleIds(array2);
            userService.insertUser(sysUser2);
            talentIntroductionInfo.setUserId(sysUser2.getUserId());
        }
        return talentIntroductionInfo;
    }

    /**
     * 设置省市区
     * @param recruitTalentIntroductionInfo
     * @return
     */
    public RecruitTalentIntroductionInfo region(RecruitTalentIntroductionInfo recruitTalentIntroductionInfo){
        if (recruitTalentIntroductionInfo.getRegions() != null) {
            StringBuilder region = new StringBuilder();
            StringBuilder regionName = new StringBuilder();
            recruitTalentIntroductionInfo.getRegions().forEach(e->{
                //查询省市区名称
                if(StringUtils.equals(regionName, "")){
                    SysRegion sysRegion = sysRegionService.selectSysRegionById(Long.valueOf(e));
                    regionName.append(sysRegion.getName());
                }else {
                    SysRegion sysRegion = sysRegionService.selectSysRegionById(Long.valueOf(e));
                    regionName.append("/").append(sysRegion.getName());
                }
                if(StringUtils.equals(region, "")){
                    region.append(e);
                }else {
                    region.append(",").append(e);
                }
            });
            recruitTalentIntroductionInfo.setRegionName(String.valueOf(regionName));
            recruitTalentIntroductionInfo.setRegion(String.valueOf(region));
        }
        return recruitTalentIntroductionInfo;
    }


    @Log(title = "人员引进信息状态更改", businessType = BusinessType.UPDATE)
    @PutMapping("/changeIntroductionInfoStatus")
    public AjaxResult changeIntroductionInfoStatus(@RequestBody RecruitTalentIntroductionInfo talentIntroductionInfo)
    {
        return toAjax(recruitTalentIntroductionInfoService.updateRecruitTalentIntroductionInfo(talentIntroductionInfo));
    }

    /**
     * 删除人才引进信息
     */
    @Log(title = "人才引进信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(recruitTalentIntroductionInfoService.deleteRecruitTalentIntroductionInfoByIds(ids));
    }


    /**
     * 导出职位信息模板
     */
    @Log(title = "导出人才引进企业模板", businessType = BusinessType.EXPORT)
    @GetMapping("/importTemplate")
    public AjaxResult importTemplate()
    {
        ExcelUtil<RecruitTalentIntroductionInfo> util = new ExcelUtil<>(RecruitTalentIntroductionInfo.class);
        return util.importTemplateExcel("人才引进企业模板");
    }

    /**
     * 导入人才引进企业
     * @param file
     * @param updateSupport
     * @return
     * @throws Exception
     */
    @Log(title = "导入人才引进企业", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    @Transactional
    public AjaxResult importData(MultipartFile file, boolean updateSupport, Long talentIntroductionId) throws Exception
    {
        ExcelUtil<RecruitTalentIntroductionInfo> util = new ExcelUtil<>(RecruitTalentIntroductionInfo.class);
        List<RecruitTalentIntroductionInfo> lists = util.importExcel(file.getInputStream());
        lists.forEach(this::insertDefaultUser);
        return success(recruitTalentIntroductionInfoService.importData(lists, talentIntroductionId));
    }

    /**
     * 获取用人单位信息
     * @return
     */
    @GetMapping("/getEmployerInfoList")
    public TableDataInfo getEmployerInfoList()
    {
        List<RecruitTalentIntroductionInfo> list = recruitTalentIntroductionInfoService.querySubordinateEnterprises(null);
        return getDataTable(list);
    }

    /**
     * 获取岗位信息
     * @return
     */
    @GetMapping("/getObtainingPositionsList")
    public TableDataInfo getObtainingPositionsList(RecruitTalentIntroductionInfo recruitTalentIntroductionInfo)
    {
        List<RecruitTalentIntroductionInfo> list = recruitTalentIntroductionInfoService.selectRecruitTalentIntroductionInfoList(recruitTalentIntroductionInfo);
        return getDataTable(list);
    }

    /**
     * 查询人才引进报名统计信息列表
     */
    @GetMapping("/statistics/{talentIntroductionId}")
    public TableDataInfo statistics(@PathVariable("talentIntroductionId") Long talentIntroductionId,
                                   @RequestParam(value = "nameOfEmployer", required = false) String nameOfEmployer,
                                   @RequestParam(value = "startEnrollTime", required = false) String startEnrollTime,
                                   @RequestParam(value = "endEnrollTime", required = false) String endEnrollTime,
                                   @RequestParam(value = "orderByColumn", required = false) String orderByColumn,
                                   @RequestParam(value = "isAsc", required = false) String isAsc)
    {
        List<TalentIntroductionStatisticsDTO> list = recruitTalentIntroductionInfoService.selectTalentIntroductionStatistics(
            talentIntroductionId, nameOfEmployer, startEnrollTime, endEnrollTime, orderByColumn, isAsc);
        return getDataTable(list);
    }

    /**
     * 导出人才引进报名统计信息列表
     */
    @Log(title = "人才引进报名统计", businessType = BusinessType.EXPORT)
    @GetMapping("/exportStatistics/{talentIntroductionId}")
    public void exportStatistics(HttpServletResponse response, 
                                @PathVariable("talentIntroductionId") Long talentIntroductionId,
                                @RequestParam(value = "nameOfEmployer", required = false) String nameOfEmployer,
                                @RequestParam(value = "startEnrollTime", required = false) String startEnrollTime,
                                @RequestParam(value = "endEnrollTime", required = false) String endEnrollTime,
                                @RequestParam(value = "orderByColumn", required = false) String orderByColumn,
                                @RequestParam(value = "isAsc", required = false) String isAsc)
    {
        List<TalentIntroductionStatisticsDTO> list = recruitTalentIntroductionInfoService.selectTalentIntroductionStatistics(
            talentIntroductionId, nameOfEmployer, startEnrollTime, endEnrollTime, orderByColumn, isAsc);
        ExcelUtil<TalentIntroductionStatisticsDTO> util = new ExcelUtil<TalentIntroductionStatisticsDTO>(TalentIntroductionStatisticsDTO.class);
        util.exportExcel(response, list, "人才引进报名统计数据");
    }

}
