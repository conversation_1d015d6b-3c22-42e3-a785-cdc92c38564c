package com.recruit.core.domain.request.talent;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


@Data
@ApiModel("学历信息")
public class QualificationBsRequest {

    @ApiModelProperty("毕业学校")
    private String school_name;

    @ApiModelProperty("专业")
    private String specialty;

    @ApiModelProperty("主修课程")
    private String main_course;

    @ApiModelProperty("获得学位时间")
    private String graduation_time;

    @ApiModelProperty("毕业证照片")
    private String graduation_img;

    @ApiModelProperty("学位证照片")
    private String degree_img;

    @ApiModelProperty("学信网照片")
    private String chsi_img;

}
