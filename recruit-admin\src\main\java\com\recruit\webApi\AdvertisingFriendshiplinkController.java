package com.recruit.webApi;

import com.recruit.common.core.controller.BaseController;
import com.recruit.common.core.domain.AjaxResult;
import com.recruit.common.core.page.TableDataInfo;
import com.recruit.core.domain.AdvertisingSpaceInfo;
import com.recruit.core.domain.FriendlyLinks;
import com.recruit.core.domain.response.AdvertisingSpaceResponse;
import com.recruit.core.service.IAdvertisingSpaceInfoService;
import com.recruit.core.service.IFriendlyLinksService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Auther: <PERSON> kong
 * @Date: 2023/6/18 11:28
 * @Description:
 */
@Api(tags= "(06-18)广告及友情链接")
@Slf4j
@RestController
@RequestMapping("/webApi/advertisingFriendshiplink")
public class AdvertisingFriendshiplinkController extends BaseController {

    @Autowired
    private IFriendlyLinksService friendlyLinksService;

    @Autowired
    private IAdvertisingSpaceInfoService advertisingSpaceInfoService;

    @ApiOperation("获取友情链接")
    @GetMapping("/getFriendlyLinks")
    public TableDataInfo getFriendlyLinks()
    {
        List<FriendlyLinks> list = friendlyLinksService.selectFriendlyLinksList(new FriendlyLinks());
        return getDataTable(list);
    }



    @ApiOperation("获取广告位列表")
    @GetMapping("/getAdvertisingSpaceList")
    public AjaxResult getAdvertisingSpaceList()
    {
        AdvertisingSpaceResponse response = new AdvertisingSpaceResponse();
        List<AdvertisingSpaceInfo> list = advertisingSpaceInfoService.selectAdvertisingSpaceInfoList(new AdvertisingSpaceInfo());

        Map<String,List<AdvertisingSpaceInfo>> collect = list.stream().collect(Collectors.groupingBy(AdvertisingSpaceInfo::getAdvertisingType));

        if(collect.get("1") != null){
            response.setOneAdvertisingSpaceList(collect.get("1"));
        }
        if(collect.get("2") != null){
            response.setTwoAdvertisingSpaceList(collect.get("2"));
        }
        if(collect.get("3") != null){
            response.setThreeAdvertisingSpaceList(collect.get("3"));
        }
        return success(response);
    }

}
