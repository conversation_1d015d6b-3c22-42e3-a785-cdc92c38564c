package com.recruit.core.domain.request.enterprise;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @Auther: <PERSON> kong
 * @Date: 2023/3/21 23:52
 * @Description:
 */
@Data
@ApiModel("个人企业资质认证1")
public class IndividualQualificationRequest {

    @NotBlank(message = "营业执照不能为空")
    @ApiModelProperty("营业执照")
    private String businessUrl;

    @NotNull(message = "企业id不能为空")
    @ApiModelProperty("企业id")
    private Long enterpriseId;
}
