package com.recruit.web.controller.core.resume;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.recruit.common.annotation.Log;
import com.recruit.common.core.controller.BaseController;
import com.recruit.common.core.domain.AjaxResult;
import com.recruit.common.enums.BusinessType;
import com.recruit.core.domain.RecruitResumeJobDesire;
import com.recruit.core.service.IRecruitResumeJobDesireService;
import com.recruit.common.utils.poi.ExcelUtil;
import com.recruit.common.core.page.TableDataInfo;

/**
 * 简历求职意愿Controller
 *
 * <AUTHOR>
 * @date 2023-03-19
 */
@RestController
@RequestMapping("/core/resumeJobDesire")
public class RecruitResumeJobDesireController extends BaseController
{
    @Autowired
    private IRecruitResumeJobDesireService recruitResumeJobDesireService;

    /**
     * 查询简历求职意愿列表
     */
    @PreAuthorize("@ss.hasPermi('core:resumeJobDesire:list')")
    @GetMapping("/list")
    public TableDataInfo list(RecruitResumeJobDesire recruitResumeJobDesire)
    {
        startPage();
        List<RecruitResumeJobDesire> list = recruitResumeJobDesireService.selectRecruitResumeJobDesireList(recruitResumeJobDesire);
        return getDataTable(list);
    }

    /**
     * 导出简历求职意愿列表
     */
    @PreAuthorize("@ss.hasPermi('core:resumeJobDesire:export')")
    @Log(title = "简历求职意愿", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RecruitResumeJobDesire recruitResumeJobDesire)
    {
        List<RecruitResumeJobDesire> list = recruitResumeJobDesireService.selectRecruitResumeJobDesireList(recruitResumeJobDesire);
        ExcelUtil<RecruitResumeJobDesire> util = new ExcelUtil<RecruitResumeJobDesire>(RecruitResumeJobDesire.class);
        util.exportExcel(response, list, "简历求职意愿数据");
    }

    /**
     * 获取简历求职意愿详细信息
     */
    @PreAuthorize("@ss.hasPermi('core:resumeJobDesire:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(recruitResumeJobDesireService.selectRecruitResumeJobDesireById(id));
    }

    /**
     * 新增简历求职意愿
     */
    @PreAuthorize("@ss.hasPermi('core:resumeJobDesire:add')")
    @Log(title = "简历求职意愿", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RecruitResumeJobDesire recruitResumeJobDesire)
    {
        return toAjax(recruitResumeJobDesireService.insertRecruitResumeJobDesire(recruitResumeJobDesire));
    }

    /**
     * 修改简历求职意愿
     */
    @PreAuthorize("@ss.hasPermi('core:resumeJobDesire:edit')")
    @Log(title = "简历求职意愿", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RecruitResumeJobDesire recruitResumeJobDesire)
    {
        return toAjax(recruitResumeJobDesireService.updateRecruitResumeJobDesire(recruitResumeJobDesire));
    }

    /**
     * 删除简历求职意愿
     */
    @PreAuthorize("@ss.hasPermi('core:resumeJobDesire:remove')")
    @Log(title = "简历求职意愿", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(recruitResumeJobDesireService.deleteRecruitResumeJobDesireByIds(ids));
    }
}
