package com.recruit.webApi;

import com.recruit.common.core.controller.BaseController;
import com.recruit.common.core.domain.AjaxResult;
import com.recruit.common.core.domain.TreeSelect;
import com.recruit.common.core.domain.TreeSelectTwo;
import com.recruit.common.core.domain.entity.RecruitCompanyIndustry;
import com.recruit.common.core.domain.entity.RecruitPosition;
import com.recruit.common.core.domain.entity.SysRegion;
import com.recruit.common.core.domain.entity.SysServiceTrade;
import com.recruit.common.core.page.TableDataInfo;
import com.recruit.core.domain.SysPopularSearchTerms;
import com.recruit.core.service.IRecruitCompanyIndustryService;
import com.recruit.core.service.IRecruitPositionService;
import com.recruit.core.service.ISysPopularSearchTermsService;
import com.recruit.core.service.ISysRegionService;
import com.recruit.core.service.ISysServiceTradeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotBlank;
import java.util.ArrayList;
import java.util.List;

/**
 * @Auther: Wu kong
 * @Date: 2023/4/17 22:07
 * @Description:
 */
@Api(tags= "公共职位接口(6-15新增工作区域地址)")
@RestController
@RequestMapping("/webApi/common")
public class CommonPositionController extends BaseController {

    @Autowired
    private ISysRegionService sysRegionService;

    @Autowired
    private IRecruitPositionService recruitPositionService;

    @Autowired
    private ISysServiceTradeService sysServiceTradeService;

    @Autowired
    private ISysPopularSearchTermsService sysPopularSearchTermsService;

    @Autowired
    private IRecruitCompanyIndustryService recruitCompanyIndustryService;


    @ApiOperation("获取公司行业")
    @GetMapping("/getCompanyIndustry")
    public TableDataInfo getCompanyIndustry(){
        List<RecruitCompanyIndustry> lists = recruitCompanyIndustryService.selectRecruitCompanyIndustryList(new RecruitCompanyIndustry());
        List<TreeSelect>  list = recruitCompanyIndustryService.buildtCompanyIndustryTreeSelect(lists);
        return getDataTable(list);
    }

    @ApiOperation("获取职位信息")
    @GetMapping("/getPosition")
    public TableDataInfo getPosition(){
        List<RecruitPosition> lists = recruitPositionService.selectRecruitPositionListTwo(new RecruitPosition());
        List<TreeSelect>  list = recruitCompanyIndustryService.buildtPositionTreeSelect(lists);
        for(TreeSelect e : list){
            if (e.getSubLevelModelList().size() == 0) {
                if(e.getLabel().equals("1")) {
                    //二级
                    List<TreeSelect> subLevelModelList = new ArrayList<>();
                    TreeSelect treeSelect = new TreeSelect();
                    treeSelect.setName(e.getName());
                    treeSelect.setCode(e.getCode());
                    treeSelect.setLabel("2");
                    //三级
                    List<TreeSelect> subLevelModelListTwo = new ArrayList<>();
                    TreeSelect treeSelectTwo = new TreeSelect();
                    treeSelectTwo.setName(e.getName());
                    treeSelectTwo.setCode(e.getCode());
                    treeSelectTwo.setLabel("3");
                    subLevelModelListTwo.add(treeSelectTwo);
                    treeSelect.setSubLevelModelList(subLevelModelListTwo);
                    subLevelModelList.add(treeSelect);
                    e.setSubLevelModelList(subLevelModelList);
                }
            }else {
                e.getSubLevelModelList().forEach(ss->{
                    if(ss.getSubLevelModelList().size() == 0){
                        List<TreeSelect> subLevelModelList = new ArrayList<>();
                        TreeSelect treeSelect = new TreeSelect();
                        treeSelect.setName(ss.getName());
                        treeSelect.setCode(ss.getCode());
                        treeSelect.setLabel("3");
                        subLevelModelList.add(treeSelect);
                        ss.setSubLevelModelList(subLevelModelList);
                    }
                });
            }
        }
        return getDataTable(list);
    }

    @ApiOperation("获取职位细分方向")
    @GetMapping("/getJobSegmentation")
    public TableDataInfo getJobSegmentation(@NotBlank(message = "编码不能为空") String positionCode){
        //获取职位信息json
        List<RecruitPosition> lists = recruitPositionService.getJobSegmentation(positionCode);
        return getDataTable(lists);
    }


    @ApiOperation("查询热门职位")
    @GetMapping("/searchTermsPosition")
    public TableDataInfo searchTermsPosition()
    {
        List<SysPopularSearchTerms> list = sysPopularSearchTermsService.selectSysPopularSearchTermsList(new SysPopularSearchTerms());
        return getDataTable(list);
    }


    @ApiOperation("获取一级职位信息")
    @GetMapping("/getSeniorGradePosition")
    public TableDataInfo getSeniorGradePosition(){
        RecruitPosition position = new RecruitPosition();
        position.setGrade(1);
        List<RecruitPosition> lists = recruitPositionService.selectRecruitPositionList(position);
        return getDataTable(lists);
    }


    @ApiOperation("获取下级职位信息 positionCode=id=code, grade 2级，3级")
    @GetMapping("/getSubordinateGradePosition")
    public TableDataInfo getSubordinateGradePosition(@NotBlank(message = "等级不能为空") int grade, String positionCode, String name){
        RecruitPosition position = new RecruitPosition();
        position.setFCode(positionCode);
        position.setGrade(grade);
        position.setName(name);
        List<RecruitPosition> lists = recruitPositionService.selectRecruitPositionList(position);
        return getDataTable(lists);
    }



    @ApiOperation("获取临工服务行业(5-27新增临工服务行业接口) type类型 1下单 2预约")
    @GetMapping("/getServiceTrade")
    public TableDataInfo getServiceTrade(){
        List<SysServiceTrade> lista = new ArrayList<>();
        List<SysServiceTrade> lists = sysServiceTradeService.selectSysServiceTradeList(new SysServiceTrade(){{
            setDisplay("N");
            setFCode("0");
        }});
        if(!lists.isEmpty()){
            lista.addAll(lists);
            lists.forEach(s->{
                SysServiceTrade trade = new SysServiceTrade();
                trade.setFCode(s.getCode());
                trade.setDisplay("N");
                List<SysServiceTrade> listss = sysServiceTradeService.selectSysServiceTradeList(trade);
                if(!lists.isEmpty()) {
                    lista.addAll(listss);
                }
            });
        }
        List<TreeSelectTwo>  list = sysServiceTradeService.buildtServiceTradeTreeSelectTwo(lista);
        return getDataTable(list);
    }


    @ApiOperation("工作区域(零工，企业信息使用)（6-15新增）")
    @GetMapping("/getWorkArea")
    public TableDataInfo getWorkArea(){
        List<SysRegion> lists = sysRegionService.selectSysRegionList(new SysRegion());
        List<TreeSelect> list = sysRegionService.buildtRegionTreeSelect(lists);
        return getDataTable(list);
    }

    /**
     * 查询市区
     * @return
     */
    @ApiOperation("工作区域（6-15新增）")
    @GetMapping("/getUrbanAreaList")
    public AjaxResult getUrbanAreaList()
    {
        List<SysRegion> lists = sysRegionService.selectSysRegionListTwo(new SysRegion());
        List<TreeSelect>  list = sysRegionService.buildtRegionTreeSelect(lists);
        return success(list);
    }

}
