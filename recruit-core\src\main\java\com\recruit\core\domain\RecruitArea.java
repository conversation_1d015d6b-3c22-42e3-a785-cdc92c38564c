package com.recruit.core.domain;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.recruit.common.annotation.Excel;
import com.recruit.common.core.domain.BaseEntity;
import lombok.Data;

/**
 * 工作区域对象 sys_area
 *
 * <AUTHOR>
 * @date 2023-03-19
 */
@Data
public class RecruitArea extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 地区编码 */
    @Excel(name = "地区编码")
    private String areaCode;

    /** 地区名 */
    @Excel(name = "地区名")
    private String areaName;

    /** 城市编码 */
    @Excel(name = "城市编码")
    private String cityCode;

    /** 城市中心点（即：经纬度坐标） */
    @Excel(name = "城市中心点", readConverterExp = "即=：经纬度坐标")
    private String center;


}
