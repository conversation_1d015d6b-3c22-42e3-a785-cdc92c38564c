package com.recruit.web.controller.core.notice;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.recruit.common.annotation.Log;
import com.recruit.common.core.controller.BaseController;
import com.recruit.common.core.domain.AjaxResult;
import com.recruit.common.enums.BusinessType;
import org.springframework.web.multipart.MultipartFile;
import com.recruit.core.domain.RecruitUserSysNotice;
import com.recruit.core.service.IRecruitUserSysNoticeService;
import com.recruit.common.utils.poi.ExcelUtil;
import com.recruit.common.core.page.TableDataInfo;

/**
 * 用户系统通知公告Controller
 *
 * <AUTHOR>
 * @date 2023-05-16
 */
@RestController
@RequestMapping("/core/notice")
public class RecruitUserSysNoticeController extends BaseController
{
    @Autowired
    private IRecruitUserSysNoticeService recruitUserSysNoticeService;

    /**
     * 查询用户系统通知公告列表
     */
    @GetMapping("/list")
    public TableDataInfo list(RecruitUserSysNotice recruitUserSysNotice)
    {
        startPage();
        List<RecruitUserSysNotice> list = recruitUserSysNoticeService.selectRecruitUserSysNoticeList(recruitUserSysNotice);
        return getDataTable(list);
    }

    /**
     * 导出用户系统通知公告列表
     */
    @Log(title = "用户系统通知公告", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RecruitUserSysNotice recruitUserSysNotice)
    {
        List<RecruitUserSysNotice> list = recruitUserSysNoticeService.selectRecruitUserSysNoticeList(recruitUserSysNotice);
        ExcelUtil<RecruitUserSysNotice> util = new ExcelUtil<RecruitUserSysNotice>(RecruitUserSysNotice.class);
        util.exportExcel(response, list, "用户系统通知公告数据");
    }

    /**
     * 获取用户系统通知公告详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(recruitUserSysNoticeService.selectRecruitUserSysNoticeById(id));
    }

    /**
     * 新增用户系统通知公告
     */
    @Log(title = "用户系统通知公告", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RecruitUserSysNotice recruitUserSysNotice)
    {
        return toAjax(recruitUserSysNoticeService.insertRecruitUserSysNotice(recruitUserSysNotice));
    }

    /**
     * 修改用户系统通知公告
     */
    @Log(title = "用户系统通知公告", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RecruitUserSysNotice recruitUserSysNotice)
    {
        return toAjax(recruitUserSysNoticeService.updateRecruitUserSysNotice(recruitUserSysNotice));
    }

    /**
     * 删除用户系统通知公告
     */
    @Log(title = "用户系统通知公告", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(recruitUserSysNoticeService.deleteRecruitUserSysNoticeByIds(ids));
    }


    /**
     * 导入用户系统通知公告
     * @param file
     * @param updateSupport
     * @return
     * @throws Exception
     */
    @Log(title = "用户系统通知公告", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<RecruitUserSysNotice> util = new ExcelUtil<>(RecruitUserSysNotice.class);
        List<RecruitUserSysNotice> lists = util.importExcel(file.getInputStream());
        String message = recruitUserSysNoticeService.importRecruitUserSysNotice(lists, updateSupport);
        return AjaxResult.success(message);
    }


}
