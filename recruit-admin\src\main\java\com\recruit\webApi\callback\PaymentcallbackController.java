package com.recruit.webApi.callback;


import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.recruit.common.utils.DateUtils;
import com.recruit.common.utils.JsonTool;
import com.recruit.common.utils.StringUtils;
import com.recruit.core.domain.*;
import com.recruit.core.mapper.RecruitUserConsumeRecordMapper;
import com.recruit.core.mapper.RecruitUserInfoMapper;
import com.recruit.core.mapper.RecruitUserOrderMapper;
import com.recruit.core.mapper.SysPayChannelMapper;
import com.recruit.core.service.ITemporaryServiceUserOrderService;
import com.recruit.core.wx.core.kit.AesUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;


@Api(tags= "支付回调")
@Slf4j
@RestController
@RequestMapping("/webApi/payment")
public class PaymentcallbackController {

    @Autowired
    private SysPayChannelMapper sysPayChannelMapper;

    @Autowired
    private RecruitUserInfoMapper recruitUserInfoMapper;

    @Autowired
    private RecruitUserOrderMapper recruitUserOrderMapper;

    @Autowired
    private RecruitUserConsumeRecordMapper recruitUserConsumeRecordMapper;

    @Autowired
    private ITemporaryServiceUserOrderService temporaryServiceUserOrderService;


    @ApiOperation("建行回调接口")
    @RequestMapping("/jhCallback")
    public String jhCallback(@RequestBody String request)
    {
        log.info("建行回调参数{}", request);
        Map<String, String> map = decryptNotify(request);
        if(map != null) {
            RecruitUserOrder userOrder = recruitUserOrderMapper.selectRecruitUserOrderBybizPayNo(map.get("ORDERID"));
            if(userOrder != null){
                if(StringUtils.equals(map.get("SUCCESS"), "Y")){
                    userOrder.setOrderStatus("SUCCESS");
                    //订单信息
                    userOrder.setOrderMsg("成功");
                    //回调信息
                    userOrder.setCallbackContent(request);
                    //回调时间
                    userOrder.setCallbackTime(DateUtils.getNowDate());
                    //充值金额
                    if(StringUtils.equals(userOrder.getOrderType(), "1")) {
                        //订单中账户值如果为1，则不给充值
                        if (!StringUtils.equals(userOrder.getRechargeAccount(), "1")) {
                            RecruitUserInfo userInfo = recruitUserInfoMapper.selectRecruitUserInfoById(userOrder.getUserId());
                            //账户金额
                            BigDecimal accountAmount = new BigDecimal("0");
                            if (userInfo.getAccountAmount() != null) {
                                accountAmount = userInfo.getAccountAmount();
                            }
                            userInfo.setAccountAmount(accountAmount.add(userOrder.getOrderAmount()));

                            BigDecimal accountIntegral = new BigDecimal("0");
                            if (userInfo.getAccountIntegral() != null && !StringUtils.equals(userInfo.getAccountIntegral(), "")) {
                                accountIntegral = new BigDecimal(userInfo.getAccountIntegral());
                            }
                            userInfo.setAccountIntegral(String.valueOf(accountIntegral.add(new BigDecimal(userOrder.getOderCashbackIntegral()))));
                            int ss = recruitUserInfoMapper.updateRecruitUserInfo(userInfo);
                            //增加用户消费记录
                            RecruitUserConsumeRecord userConsumeRecord = new RecruitUserConsumeRecord();
                            userConsumeRecord.setUserId(userInfo.getId());
                            userConsumeRecord.setAmount(userOrder.getOrderAmount());
                            userConsumeRecord.setIntegral(userOrder.getOderCashbackIntegral());
                            userConsumeRecord.setAccess("1");
                            userConsumeRecord.setDescribe("充值金额及赠送积分");
                            recruitUserConsumeRecordMapper.insertRecruitUserConsumeRecord(userConsumeRecord);
                            //如果新增成功则下次不允许再次增加消防记录
                            if (ss > 0) {
                                userOrder.setRechargeAccount("1");
                            }
                        }
                    }else if(StringUtils.equals(userOrder.getOrderType(), "2")){

                        //查询订单信息
                        TemporaryServiceUserOrder serviceUserOrder = temporaryServiceUserOrderService.selectTemporaryServiceUserOrderByBizPayNo(map.get("ORDERID"));
                        if(serviceUserOrder == null){
                            throw new RuntimeException("订单信息不存在！");
                        }
                        //订单状态 0预约，1下单，2预约确认，3待支付，4待服务，5订单完成，6取消订单，7退款
                        serviceUserOrder.setOrderStatus("4");
                        serviceUserOrder.setPayStatus("2");
                        temporaryServiceUserOrderService.updateTemporaryServiceUserOrderFour(serviceUserOrder);
                    }else if(StringUtils.equals(userOrder.getOrderType(), "3")){

                    }
                    recruitUserOrderMapper.updateRecruitUserOrder(userOrder);
                }
            }
        }
        return "回调成功";
    }

    public static Map<String, String> decryptNotify(String notify){
        //开始解密
        try {
            //将返回报文转码,
            String notifys = URLDecoder.decode(notify, "UTF-8");
            //将报文中&后的数据给去掉
            String[] str = notifys.split("&");
            Map<String, String> jsonMap  = new HashMap<>();
            for(String s : str){
                String[] ss = s.split("=");
                if(ss.length > 1) {
                    jsonMap.put(ss[0], ss[1]);
                }
            }
            return jsonMap;
        }catch (Exception e){
            log.info("银盛支付回调解密:{}", e);
        }
        return null;
    }


    @ApiOperation("微信回调接口")
    @RequestMapping("/wxCallback")
    public ResponseEntity<Map<String,Object>> wxCallback(@RequestBody Map<String,Object> body)
    {
        SysPayChannel payChannel = sysPayChannelMapper.selectSysPayChannel("1");
        try {
            MapUtil.getStr(body, "event_type");
            String event_type=MapUtil.getStr(body, "event_type");
            if ("TRANSACTION.SUCCESS".equals(event_type)){
                String ob= JsonTool.mapToJson(body.get("resource"));
                Map<String,Object> resource= JsonTool.jsonStringToMapObjectStr(ob);
                String associatedData = MapUtil.getStr(resource,"associated_data");
                String nonce = MapUtil.getStr(resource,"nonce");
                String cipherText = MapUtil.getStr(resource,"ciphertext");
                AesUtil aesUtil = new AesUtil(payChannel.getMchKey().getBytes(StandardCharsets.UTF_8));
                // 平台证书密文解密数据
                String data = aesUtil.decryptToString(
                        associatedData.getBytes(StandardCharsets.UTF_8),
                        nonce.getBytes(StandardCharsets.UTF_8),
                        cipherText
                );
                log.info("微信回调{}", data);
                JSONObject jsonObject = JSON.parseObject(data);
                RecruitUserOrder userOrder = recruitUserOrderMapper.selectRecruitUserOrderBybizPayNo(String.valueOf(jsonObject.get("out_trade_no")));
                //订单状态
                userOrder.setOrderStatus("SUCCESS");
                //订单信息
                userOrder.setOrderMsg(String.valueOf(jsonObject.get("trade_state_desc")));
                //回调信息
                userOrder.setCallbackContent(data);
                //回调时间
                userOrder.setCallbackTime(DateUtils.getNowDate());
                //充值金额
                if(StringUtils.equals(userOrder.getOrderType(), "1")) {
                    //订单中账户值如果为1，则不给充值
                    if (!StringUtils.equals(userOrder.getRechargeAccount(), "1")) {
                        RecruitUserInfo userInfo = recruitUserInfoMapper.selectRecruitUserInfoById(userOrder.getUserId());
                        //账户金额
                        BigDecimal accountAmount = new BigDecimal("0");
                        if (userInfo.getAccountAmount() != null) {
                            accountAmount = userInfo.getAccountAmount();
                        }
                        userInfo.setAccountAmount(accountAmount.add(userOrder.getOrderAmount()));

                        BigDecimal accountIntegral = new BigDecimal("0");
                        if (userInfo.getAccountIntegral() != null && !StringUtils.equals(userInfo.getAccountIntegral(), "")) {
                            accountIntegral = new BigDecimal(userInfo.getAccountIntegral());
                        }
                        userInfo.setAccountIntegral(String.valueOf(accountIntegral.add(new BigDecimal(userOrder.getOderCashbackIntegral()))));
                        int ss = recruitUserInfoMapper.updateRecruitUserInfo(userInfo);
                        //增加用户消费记录
                        RecruitUserConsumeRecord userConsumeRecord = new RecruitUserConsumeRecord();
                        userConsumeRecord.setUserId(userInfo.getId());
                        userConsumeRecord.setAmount(userOrder.getOrderAmount());
                        userConsumeRecord.setIntegral(userOrder.getOderCashbackIntegral());
                        userConsumeRecord.setAccess("1");
                        userConsumeRecord.setDescribe("充值金额及赠送积分");
                        recruitUserConsumeRecordMapper.insertRecruitUserConsumeRecord(userConsumeRecord);
                        //如果新增成功则下次不允许再次增加消防记录
                        if (ss > 0) {
                            userOrder.setRechargeAccount("1");
                        }
                    }
                }else if(StringUtils.equals(userOrder.getOrderType(), "2")){

                    //查询订单信息
                    TemporaryServiceUserOrder serviceUserOrder = temporaryServiceUserOrderService.selectTemporaryServiceUserOrderByBizPayNo(String.valueOf(jsonObject.get("out_trade_no")));
                    if(serviceUserOrder == null){
                        throw new RuntimeException("订单信息不存在！");
                    }
                    //订单状态 0预约，1下单，2预约确认，3待支付，4待服务，5订单完成，6取消订单，7退款
                    serviceUserOrder.setOrderStatus("4");
                    serviceUserOrder.setPayStatus("2");
                    temporaryServiceUserOrderService.updateTemporaryServiceUserOrderFour(serviceUserOrder);
                }else if(StringUtils.equals(userOrder.getOrderType(), "3")){

                }

                recruitUserOrderMapper.updateRecruitUserOrder(userOrder);
                Map<String,Object> back=new HashMap<>();
                back.put("code",200);
                back.put("message",null);
                return new ResponseEntity<Map<String,Object>>(back, HttpStatus.OK);

            }

        } catch (Exception e) {
            e.printStackTrace();
            Map<String,Object> back=new HashMap<>();
            back.put("code",500);
            back.put("message",e.getMessage());
            return new ResponseEntity<Map<String,Object>>(back, HttpStatus.INTERNAL_SERVER_ERROR);
        }
        Map<String,Object> back=new HashMap<>();
        back.put("code",400);
        back.put("message","未知的异常回调");
        return new ResponseEntity<Map<String,Object>>(back, HttpStatus.BAD_REQUEST);
    }
}
