package com.recruit.core.domain.request.im;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Auther: <PERSON> kong
 * @Date: 2023/4/7 22:34
 * @Description:
 */
@Data
@ApiModel("同步candidate1")
public class CandidateRequest {

    @ApiModelProperty("接收者id")
    private Long uid;

    @ApiModelProperty("发送内容")
    private String candidate;

}
