package com.recruit.core.domain.request.position;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @Auther: <PERSON> kong
 * @Date: 2023/5/25 10:33
 * @Description:
 */

@Data
@ApiModel("地图找工作请求实体")
public class MapJobSearchRequest {


    @NotBlank(message = "经度不能为空")
    @ApiModelProperty("经度")
    private String longitude;

    @NotBlank(message = "纬度不能为空")
    @ApiModelProperty("纬度")
    private String latitude;

    @NotBlank(message = "直线距离不能为空")
    @ApiModelProperty("直线距离")
    private String linearDistance;


    @ApiModelProperty("工作经验")
    private String workExperience;

    @ApiModelProperty("最低学历")
    private String minimumEducation;

    @ApiModelProperty("最高薪资")
    private String maximumSalary;

    @ApiModelProperty("最低薪资")
    private String minimumWage;

    @ApiModelProperty("搜索值")
    private String searchValue;

    @ApiModelProperty("职位")
    private String position;
}
