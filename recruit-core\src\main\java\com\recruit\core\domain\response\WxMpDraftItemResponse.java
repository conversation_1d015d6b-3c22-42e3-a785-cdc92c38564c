package com.recruit.core.domain.response;

import lombok.Data;
import me.chanjar.weixin.common.util.json.WxGsonBuilder;
import me.chanjar.weixin.mp.bean.draft.WxMpDraftItem;

import java.io.Serializable;

@Data
public class WxMpDraftItemResponse implements Serializable {

    private static final long serialVersionUID = 214696458030935146L;

    private String mediaId;

    private WxMpDraftInfoResponse content;

    private Long updateTime;

    //状态0未发布，1已发布
    private String status;

    //推文类型
    private String tweetType;

    //模板类型
    private String templateType;

    //企业id
    private String enterpriseInfoId;

    //职位id
    private String positionInfoId;

    //职位id
    private Long[] positionInfoIds;

    //企业id
    private Long[] enterpriseInfoIds;


    public static WxMpDraftItemResponse fromJson(String json) {
        return (WxMpDraftItemResponse) WxGsonBuilder.create().fromJson(json, WxMpDraftItemResponse.class);
    }

    public WxMpDraftItemResponse() {
    }

    public String getMediaId() {
        return this.mediaId;
    }

    public WxMpDraftInfoResponse getContent() {
        return this.content;
    }

    public Long getUpdateTime() {
        return this.updateTime;
    }

    public void setMediaId(String mediaId) {
        this.mediaId = mediaId;
    }

    public void setContent(WxMpDraftInfoResponse content) {
        this.content = content;
    }

    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getTweetType() {
        return tweetType;
    }

    public void setTweetType(String tweetType) {
        this.tweetType = tweetType;
    }

    public String getTemplateType() {
        return templateType;
    }

    public void setTemplateType(String templateType) {
        this.templateType = templateType;
    }

    public String getEnterpriseInfoId() {
        return enterpriseInfoId;
    }

    public void setEnterpriseInfoId(String enterpriseInfoId) {
        this.enterpriseInfoId = enterpriseInfoId;
    }

    public String getPositionInfoId() {
        return positionInfoId;
    }

    public void setPositionInfoId(String positionInfoId) {
        this.positionInfoId = positionInfoId;
    }

    public Long[] getPositionInfoIds() {
        return positionInfoIds;
    }

    public void setPositionInfoIds(Long[] positionInfoIds) {
        this.positionInfoIds = positionInfoIds;
    }

    public Long[] getEnterpriseInfoIds() {
        return enterpriseInfoIds;
    }

    public void setEnterpriseInfoIds(Long[] enterpriseInfoIds) {
        this.enterpriseInfoIds = enterpriseInfoIds;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        } else if (!(o instanceof WxMpDraftItemResponse)) {
            return false;
        } else {
            WxMpDraftItemResponse other = (WxMpDraftItemResponse)o;
            if (!other.canEqual(this)) {
                return false;
            } else {
                label47: {
                    Object this$mediaId = this.getMediaId();
                    Object other$mediaId = other.getMediaId();
                    if (this$mediaId == null) {
                        if (other$mediaId == null) {
                            break label47;
                        }
                    } else if (this$mediaId.equals(other$mediaId)) {
                        break label47;
                    }

                    return false;
                }

                Object this$content = this.getContent();
                Object other$content = other.getContent();
                if (this$content == null) {
                    if (other$content != null) {
                        return false;
                    }
                } else if (!this$content.equals(other$content)) {
                    return false;
                }

                Object this$updateTime = this.getUpdateTime();
                Object other$updateTime = other.getUpdateTime();
                if (this$updateTime == null) {
                    if (other$updateTime != null) {
                        return false;
                    }
                } else if (!this$updateTime.equals(other$updateTime)) {
                    return false;
                }

                return true;
            }
        }
    }

    protected boolean canEqual(Object other) {
        return other instanceof WxMpDraftItem;
    }

    public int hashCode() {
        boolean PRIME = true;
        int result = 1;
        Object $mediaId = this.getMediaId();
        result = result * 59 + ($mediaId == null ? 43 : $mediaId.hashCode());
        Object $content = this.getContent();
        result = result * 59 + ($content == null ? 43 : $content.hashCode());
        Object $updateTime = this.getUpdateTime();
        result = result * 59 + ($updateTime == null ? 43 : $updateTime.hashCode());
        return result;
    }

    public String toString() {
        return "WxMpDraftItem(mediaId=" + this.getMediaId() + ", content=" + this.getContent() + ", updateTime=" + this.getUpdateTime() + ")";
    }
}
