package com.recruit.common.client;


import com.recruit.common.im.GroupMessageInfo;
import com.recruit.common.im.PrivateMessageInfo;
import com.recruit.common.im.WebSocketMessageInfo;
import com.recruit.common.sender.IMSender;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;

import java.util.List;
import java.util.Map;

@Configuration
public class IMClient {

    @Autowired
    private IMSender imSender;

    /**
     * 发送私聊消息
     *
     * @param recvId 接收用户id
     * @param messageInfo 消息体，将转成json发送到客户端
     */
    public void sendPrivateMessage(Long recvId, PrivateMessageInfo... messageInfo){
        imSender.sendPrivateMessage(recvId,messageInfo);
    }

    /**
     * 发送群聊消息
     *
     * @param recvIds 群聊用户id列表
     * @param messageInfo 消息体，将转成json发送到客户端
     */
    public void sendGroupMessage(List<Long> recvIds, GroupMessageInfo... messageInfo){
        imSender.sendGroupMessage(recvIds,messageInfo);
    }


    /**
     * 发送私聊消息
     *
     * @param webSocketId 接收用户id
     * @param map 消息体，将转成json发送到客户端
     */
    public void sendWebSocketMessage(String webSocketId, WebSocketMessageInfo... messageInfo){
        imSender.sendWebSocketMessage(webSocketId, messageInfo);
    }


}
