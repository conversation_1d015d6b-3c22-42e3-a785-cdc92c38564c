package com.recruit.core.domain;

import com.recruit.common.annotation.Excel;
import com.recruit.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 专业代码对象 recruit_talent_education_code
 *
 * <AUTHOR>
 * @date 2024-07-26
 */
@Data
@ApiModel("专业代码")
public class RecruitTalentEducationCode extends BaseEntity
{
    private static final long serialVersionUID = 1L;


    private Long id;


    @Excel(name = "专业类")
    @ApiModelProperty("专业类")
    private String speciality;


    @Excel(name = "专业代码")
    @ApiModelProperty("专业代码")
    private String specialityCode;


    @Excel(name = "专业名称")
    @ApiModelProperty("专业名称")
    private String specialityName;


    @Excel(name = "学位")
    @ApiModelProperty("学位")
    private String diploma;


    @Excel(name = "类型1本科2研究生")
    @ApiModelProperty("类型1本科2研究生")
    private String type;


}
