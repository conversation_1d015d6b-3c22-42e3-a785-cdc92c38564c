package com.recruit.core.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.recruit.common.annotation.Excel;
import com.recruit.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 简历基本信息对象 recruit_resume_basic_info
 *
 * <AUTHOR>
 * @date 2023-03-21
 */
@Data
@ApiModel("简历基本信息")
public class RecruitResumeBasicInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 用户id */
    @Excel(name = "用户id")
    @ApiModelProperty("用户id")
    private Long userId;

    /** 附件简历 */
    @Excel(name = "附件简历")
    @ApiModelProperty("附件简历")
    private String attachmentResumeUrl;

    /** 工作经验 */
    @Excel(name = "工作经验")
    @ApiModelProperty("工作经验")
    private String workExperience;

    @Excel(name = "工作经验名称")
    @ApiModelProperty("工作经验名称")
    private String workExperienceName;

    /** 最高学历 */
    @Excel(name = "最高学历")
    @ApiModelProperty("最高学历")
    private String education;

    @Excel(name = "最高学历名称")
    @ApiModelProperty("最高学历名称")
    private String educationName;

    /** 所在城市 */
    @Excel(name = "所在城市")
    @ApiModelProperty("所在城市")
    private String city;

    @ApiModelProperty("所在城市")
    private List<String> citys;

    @ApiModelProperty("所在城市")
    private String areaName;

    @ApiModelProperty("附件简历名称")
    private String resumeName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("附件简历更新时间")
    private Date resumeUpdateTime;

    @ApiModelProperty("隐藏简历")
    private String hideResume;

}
