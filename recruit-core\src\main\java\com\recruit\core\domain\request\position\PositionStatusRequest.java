package com.recruit.core.domain.request.position;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @Auther: <PERSON> kong
 * @Date: 2023/3/26 18:08
 * @Description:
 */
@Data
@ApiModel("设置状态2")
public class PositionStatusRequest {

    @ApiModelProperty("主键id")
    private Long id;


    @NotBlank(message = "职位状态不能为空")
    @ApiModelProperty("职位状态")
    private String positionStatus;
}
