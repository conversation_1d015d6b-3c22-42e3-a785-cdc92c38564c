package com.recruit.webApi;

import com.recruit.common.core.controller.BaseController;
import com.recruit.common.core.domain.AjaxResult;
import com.recruit.common.core.page.TableDataInfo;
import com.recruit.common.utils.DateUtils;
import com.recruit.common.utils.DictUtils;
import com.recruit.common.utils.StringUtils;
import com.recruit.core.domain.RecruitPositionInfo;
import com.recruit.core.domain.RecruitUserInfo;
import com.recruit.core.domain.RecruitUserProp;
import com.recruit.core.domain.SysProp;
import com.recruit.core.domain.request.position.JobApplicantPositionRequest;
import com.recruit.core.domain.request.position.PositionStatusRequest;
import com.recruit.core.domain.request.prop.PropUsageRequest;
import com.recruit.core.service.IRecruitUserInfoService;
import com.recruit.core.service.IRecruitUserPropService;
import com.recruit.core.service.ISysPropService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.List;

/**
 * @Auther: Wu kong
 * @Date: 2023/5/22 17:42
 * @Description:
 */
@Slf4j
@Api(tags= "(5-22)道具接口")
@RestController
@RequestMapping("/web/api/prop")
public class PropController extends BaseController {

    @Autowired
    private ISysPropService sysPropService;

    @Autowired
    private IRecruitUserInfoService recruitUserInfoService;

    @Autowired
    private IRecruitUserPropService recruitUserPropService;


    @ApiOperation("查询道具列表")
    @GetMapping(value = "/getPropsList", produces = {"application/json"})
    @ApiResponses({
            @ApiResponse(code = 200, message = "返回实体", response = SysProp.class)
    })
    public TableDataInfo getPropsList() {

        RecruitUserInfo userInfo = recruitUserInfoService.selectRecruitUserInfoById(getUserId());
        SysProp prop = new SysProp();
        prop.setChatType(userInfo.getType());
        List<SysProp> list = sysPropService.selectSysPropList(prop);
        list.forEach(e->{
            e.setPropTypeName(DictUtils.getDictLabel("prop_type", e.getPropType()));
        });
        return getDataTable(list);
    }


    @ApiOperation("查询道具详情")
    @GetMapping(value = "/getPropsDetails", produces = {"application/json"})
    @ApiResponses({
            @ApiResponse(code = 200, message = "返回实体", response = SysProp.class)
    })
    public AjaxResult getPropsDetails(@NotNull(message = "id不能为空") @RequestParam Long id)
    {
        SysProp prop = sysPropService.selectSysPropById(id);
        if( prop.getSysPropPriceList() != null) {
            prop.getSysPropPriceList().forEach(s -> {
                if (s.getPropAmount() != null) {
                    if (s.getDiscount() != null && !StringUtils.equals(s.getDiscount(), "0")) {
                        BigDecimal ss = new BigDecimal(s.getDiscount()).divide(new BigDecimal(10));
                        s.setDiscountAmount(s.getPropAmount().multiply(ss));
                    }

                }
                if (s.getPropIntegral() != null && !StringUtils.equals(s.getPropIntegral(), "")) {
                    if (s.getDiscount() != null && !StringUtils.equals(s.getDiscount(), "0")) {
                        BigDecimal ss = new BigDecimal(s.getDiscount()).divide(new BigDecimal(10));
                        BigDecimal aa = new BigDecimal(s.getPropIntegral());
                        s.setDiscountIntegral(String.valueOf(aa.multiply(ss)));
                    }
                }
            });
        }
        return success(prop);
    }


    @ApiOperation("道具使用, 急聘须传职位id，置顶则不需要传")
    @PostMapping("/propUsage")
    public AjaxResult propUsage(@RequestBody @Validated PropUsageRequest request){

        RecruitUserProp userProp = recruitUserPropService.selectRecruitUserPropById(request.getUserPropId());
        if(StringUtils.equals(userProp.getOnState(), "3")){
            throw new RuntimeException("该道具已到期！");
        }else if(StringUtils.equals(userProp.getOnState(), "1")){
            throw new RuntimeException("该道具正使用中！");
        }else if(StringUtils.equals(userProp.getOnState(), "2")){
            throw new RuntimeException("该道具已使用！");
        }else {
            RecruitUserProp userProps = new RecruitUserProp();
            userProps.setUserId(getUserId());
            userProps.setPropType(userProp.getPropType());
            userProps.setOnState("1");
            List<RecruitUserProp> lists = recruitUserPropService.selectRecruitUserPropList(userProps);
            if(lists.size() > 0){
                throw new RuntimeException("已有相同道具在使用，请到期后再使用！");
            }
            RecruitUserProp recruitUserProp = new RecruitUserProp();
            recruitUserProp.setId(request.getUserPropId());
            recruitUserProp.setPositionInfoId(request.getPositionInfoId());
            recruitUserProp.setOnState("1");
            recruitUserProp.setUsageTime(DateUtils.getCurrentTimePlusDay(new Date(), Math.toIntExact(userProp.getSubExpirationNum())));
            return success(recruitUserPropService.updateRecruitUserPropTwo(recruitUserProp));
        }
    }

}
