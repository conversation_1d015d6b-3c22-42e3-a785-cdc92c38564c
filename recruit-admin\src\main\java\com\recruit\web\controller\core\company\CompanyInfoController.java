package com.recruit.web.controller.core.company;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.recruit.common.annotation.Log;
import com.recruit.common.core.controller.BaseController;
import com.recruit.common.core.domain.AjaxResult;
import com.recruit.common.enums.BusinessType;
import com.recruit.core.domain.CompanyInfo;
import com.recruit.core.service.ICompanyInfoService;
import com.recruit.common.utils.poi.ExcelUtil;
import com.recruit.common.core.page.TableDataInfo;

/**
 * 公司信息Controller
 *
 * <AUTHOR>
 * @date 2023-04-11
 */
@RestController
@RequestMapping("/core/companyInfo")
public class CompanyInfoController extends BaseController
{
    @Autowired
    private ICompanyInfoService companyInfoService;

    /**
     * 查询公司信息列表
     */
    @PreAuthorize("@ss.hasPermi('core:companyInfo:list')")
    @GetMapping("/list")
    public TableDataInfo list(CompanyInfo companyInfo)
    {
        startPage();
        List<CompanyInfo> list = companyInfoService.selectCompanyInfoList(companyInfo);
        return getDataTable(list);
    }

    /**
     * 导出公司信息列表
     */
    @PreAuthorize("@ss.hasPermi('core:companyInfo:export')")
    @Log(title = "公司信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CompanyInfo companyInfo)
    {
        List<CompanyInfo> list = companyInfoService.selectCompanyInfoList(companyInfo);
        ExcelUtil<CompanyInfo> util = new ExcelUtil<CompanyInfo>(CompanyInfo.class);
        util.exportExcel(response, list, "公司信息数据");
    }

    /**
     * 获取公司信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('core:companyInfo:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(companyInfoService.selectCompanyInfoById(id));
    }

    /**
     * 新增公司信息
     */
    @PreAuthorize("@ss.hasPermi('core:companyInfo:add')")
    @Log(title = "公司信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CompanyInfo companyInfo)
    {
        return toAjax(companyInfoService.insertCompanyInfo(companyInfo));
    }

    /**
     * 修改公司信息
     */
    @PreAuthorize("@ss.hasPermi('core:companyInfo:edit')")
    @Log(title = "公司信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CompanyInfo companyInfo)
    {
        return toAjax(companyInfoService.updateCompanyInfo(companyInfo));
    }

    /**
     * 删除公司信息
     */
    @PreAuthorize("@ss.hasPermi('core:companyInfo:remove')")
    @Log(title = "公司信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(companyInfoService.deleteCompanyInfoByIds(ids));
    }
}
