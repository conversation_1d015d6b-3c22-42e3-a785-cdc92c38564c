package com.recruit.web.controller.core.merchant;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.recruit.common.core.domain.entity.SysServiceTrade;
import com.recruit.core.service.ISysServiceTradeService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.recruit.common.annotation.Log;
import com.recruit.common.core.controller.BaseController;
import com.recruit.common.core.domain.AjaxResult;
import com.recruit.common.enums.BusinessType;
import org.springframework.web.multipart.MultipartFile;
import com.recruit.core.domain.TemporaryServiceGoods;
import com.recruit.core.service.ITemporaryServiceGoodsService;
import com.recruit.common.utils.poi.ExcelUtil;
import com.recruit.common.core.page.TableDataInfo;

/**
 * 服务商品Controller
 *
 * <AUTHOR>
 * @date 2023-06-01
 */
@RestController
@RequestMapping("/core/serviceGoods")
public class TemporaryServiceGoodsController extends BaseController
{
    @Autowired
    private ISysServiceTradeService sysServiceTradeService;

    @Autowired
    private ITemporaryServiceGoodsService temporaryServiceGoodsService;

    /**
     * 查询服务商品列表
     */
    @PreAuthorize("@ss.hasPermi('core:serviceGoods:list')")
    @GetMapping("/list")
    public TableDataInfo list(TemporaryServiceGoods temporaryServiceGoods)
    {
        if(temporaryServiceGoods.getServiceTrade() == null){
            temporaryServiceGoods.setServiceTrade("100000");
        }
        startPage();
        List<TemporaryServiceGoods> list = temporaryServiceGoodsService.selectTemporaryServiceGoodsList(temporaryServiceGoods);
        return getDataTable(list);
    }

    /**
     * 导出服务商品列表
     */
    @PreAuthorize("@ss.hasPermi('core:serviceGoods:export')")
    @Log(title = "服务商品", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TemporaryServiceGoods temporaryServiceGoods)
    {
        List<TemporaryServiceGoods> list = temporaryServiceGoodsService.selectTemporaryServiceGoodsList(temporaryServiceGoods);
        ExcelUtil<TemporaryServiceGoods> util = new ExcelUtil<TemporaryServiceGoods>(TemporaryServiceGoods.class);
        util.exportExcel(response, list, "服务商品数据");
    }

    /**
     * 获取服务商品详细信息
     */
    @PreAuthorize("@ss.hasPermi('core:serviceGoods:query')")
    @GetMapping(value = "/{serviceTrade}")
    public AjaxResult getInfo(@PathVariable("serviceTrade") String serviceTrade)
    {
        TemporaryServiceGoods serviceGoods = temporaryServiceGoodsService.selectTemporaryServiceGoodsByServiceTrade(serviceTrade);
        if(serviceGoods != null){
            return success(serviceGoods);
        }else {
            SysServiceTrade serviceTrades = sysServiceTradeService.selectSysServiceTradeById(Long.valueOf(serviceTrade));
            TemporaryServiceGoods serviceGoodss = new TemporaryServiceGoods();
            serviceGoodss.setServiceTrade(serviceTrade);
            serviceGoodss.setServiceTradeName(serviceTrades.getName());
            return success(serviceGoodss);
        }
    }

    /**
     * 新增服务商品
     */
    @PreAuthorize("@ss.hasPermi('core:serviceGoods:add')")
    @Log(title = "服务商品", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TemporaryServiceGoods temporaryServiceGoods)
    {
        return toAjax(temporaryServiceGoodsService.insertTemporaryServiceGoods(temporaryServiceGoods));
    }

    /**
     * 修改服务商品
     */
    @PreAuthorize("@ss.hasPermi('core:serviceGoods:edit')")
    @Log(title = "服务商品", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TemporaryServiceGoods temporaryServiceGoods)
    {
        return toAjax(temporaryServiceGoodsService.updateTemporaryServiceGoods(temporaryServiceGoods));
    }

    /**
     * 删除服务商品
     */
    @PreAuthorize("@ss.hasPermi('core:serviceGoods:remove')")
    @Log(title = "服务商品", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(temporaryServiceGoodsService.deleteTemporaryServiceGoodsByIds(ids));
    }


    /**
     * 导入服务商品
     * @param file
     * @param updateSupport
     * @return
     * @throws Exception
     */
    @PreAuthorize("@ss.hasPermi('core:serviceGoods:import')")
    @Log(title = "服务商品", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<TemporaryServiceGoods> util = new ExcelUtil<>(TemporaryServiceGoods.class);
        List<TemporaryServiceGoods> lists = util.importExcel(file.getInputStream());
        String message = temporaryServiceGoodsService.importTemporaryServiceGoods(lists, updateSupport);
        return AjaxResult.success(message);
    }


}
