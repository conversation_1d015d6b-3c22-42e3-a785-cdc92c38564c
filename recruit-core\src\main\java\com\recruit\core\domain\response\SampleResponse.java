package com.recruit.core.domain.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Auther: <PERSON> kong
 * @Date: 2023/3/24 17:55
 * @Description:
 */
@Data
@ApiModel("营业执照校验信息返回2")
public class SampleResponse {

    @ApiModelProperty("识别结果字符串")
    private String words;

    @ApiModelProperty("位置数组（坐标0点为左上角）")
    private String location;
}
