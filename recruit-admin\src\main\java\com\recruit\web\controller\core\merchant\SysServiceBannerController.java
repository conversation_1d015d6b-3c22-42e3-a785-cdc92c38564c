package com.recruit.web.controller.core.merchant;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.recruit.common.annotation.Log;
import com.recruit.common.core.controller.BaseController;
import com.recruit.common.core.domain.AjaxResult;
import com.recruit.common.enums.BusinessType;
import org.springframework.web.multipart.MultipartFile;
import com.recruit.core.domain.SysServiceBanner;
import com.recruit.core.service.ISysServiceBannerService;
import com.recruit.common.utils.poi.ExcelUtil;
import com.recruit.common.core.page.TableDataInfo;

/**
 * 服务bannerController
 *
 * <AUTHOR>
 * @date 2023-06-01
 */
@RestController
@RequestMapping("/core/serviceBanner")
public class SysServiceBannerController extends BaseController
{
    @Autowired
    private ISysServiceBannerService sysServiceBannerService;

    /**
     * 查询服务banner列表
     */
    @PreAuthorize("@ss.hasPermi('core:serviceBanner:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysServiceBanner sysServiceBanner)
    {
        startPage();
        List<SysServiceBanner> list = sysServiceBannerService.selectSysServiceBannerList(sysServiceBanner);
        return getDataTable(list);
    }

    /**
     * 导出服务banner列表
     */
    @PreAuthorize("@ss.hasPermi('core:serviceBanner:export')")
    @Log(title = "服务banner", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysServiceBanner sysServiceBanner)
    {
        List<SysServiceBanner> list = sysServiceBannerService.selectSysServiceBannerList(sysServiceBanner);
        ExcelUtil<SysServiceBanner> util = new ExcelUtil<SysServiceBanner>(SysServiceBanner.class);
        util.exportExcel(response, list, "服务banner数据");
    }

    /**
     * 获取服务banner详细信息
     */
    @PreAuthorize("@ss.hasPermi('core:serviceBanner:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(sysServiceBannerService.selectSysServiceBannerById(id));
    }

    /**
     * 新增服务banner
     */
    @PreAuthorize("@ss.hasPermi('core:serviceBanner:add')")
    @Log(title = "服务banner", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SysServiceBanner sysServiceBanner)
    {
        return toAjax(sysServiceBannerService.insertSysServiceBanner(sysServiceBanner));
    }

    /**
     * 修改服务banner
     */
    @PreAuthorize("@ss.hasPermi('core:serviceBanner:edit')")
    @Log(title = "服务banner", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SysServiceBanner sysServiceBanner)
    {
        return toAjax(sysServiceBannerService.updateSysServiceBanner(sysServiceBanner));
    }

    /**
     * 删除服务banner
     */
    @PreAuthorize("@ss.hasPermi('core:serviceBanner:remove')")
    @Log(title = "服务banner", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(sysServiceBannerService.deleteSysServiceBannerByIds(ids));
    }


    /**
     * 导入服务banner
     * @param file
     * @param updateSupport
     * @return
     * @throws Exception
     */
    @PreAuthorize("@ss.hasPermi('core:serviceBanner:import')")
    @Log(title = "服务banner", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<SysServiceBanner> util = new ExcelUtil<>(SysServiceBanner.class);
        List<SysServiceBanner> lists = util.importExcel(file.getInputStream());
        String message = sysServiceBannerService.importSysServiceBanner(lists, updateSupport);
        return AjaxResult.success(message);
    }


    @PreAuthorize("@ss.hasPermi('core:serviceBanner:edit')")
    @Log(title = "服务banner", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody SysServiceBanner sysServiceBanner)
    {
        sysServiceBanner.setUpdateBy(getUsername());
        return toAjax(sysServiceBannerService.updateSysServiceBanner(sysServiceBanner));
    }


}
