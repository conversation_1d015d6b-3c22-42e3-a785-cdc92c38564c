package com.recruit.web.controller.core.resume;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.recruit.common.annotation.Log;
import com.recruit.common.core.controller.BaseController;
import com.recruit.common.core.domain.AjaxResult;
import com.recruit.common.enums.BusinessType;
import com.recruit.core.domain.RecruitResumeOtherInfo;
import com.recruit.core.service.IRecruitResumeOtherInfoService;
import com.recruit.common.utils.poi.ExcelUtil;
import com.recruit.common.core.page.TableDataInfo;

/**
 * 简历其它信息Controller
 *
 * <AUTHOR>
 * @date 2023-03-19
 */
@RestController
@RequestMapping("/core/resumeOtherInfo")
public class RecruitResumeOtherInfoController extends BaseController
{
    @Autowired
    private IRecruitResumeOtherInfoService recruitResumeOtherInfoService;

    /**
     * 查询简历其它信息列表
     */
    @PreAuthorize("@ss.hasPermi('core:resumeOtherInfo:list')")
    @GetMapping("/list")
    public TableDataInfo list(RecruitResumeOtherInfo recruitResumeOtherInfo)
    {
        startPage();
        List<RecruitResumeOtherInfo> list = recruitResumeOtherInfoService.selectRecruitResumeOtherInfoList(recruitResumeOtherInfo);
        return getDataTable(list);
    }

    /**
     * 导出简历其它信息列表
     */
    @PreAuthorize("@ss.hasPermi('core:resumeOtherInfo:export')")
    @Log(title = "简历其它信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RecruitResumeOtherInfo recruitResumeOtherInfo)
    {
        List<RecruitResumeOtherInfo> list = recruitResumeOtherInfoService.selectRecruitResumeOtherInfoList(recruitResumeOtherInfo);
        ExcelUtil<RecruitResumeOtherInfo> util = new ExcelUtil<RecruitResumeOtherInfo>(RecruitResumeOtherInfo.class);
        util.exportExcel(response, list, "简历其它信息数据");
    }

    /**
     * 获取简历其它信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('core:resumeOtherInfo:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(recruitResumeOtherInfoService.selectRecruitResumeOtherInfoById(id));
    }

    /**
     * 新增简历其它信息
     */
    @PreAuthorize("@ss.hasPermi('core:resumeOtherInfo:add')")
    @Log(title = "简历其它信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RecruitResumeOtherInfo recruitResumeOtherInfo)
    {
        return toAjax(recruitResumeOtherInfoService.insertRecruitResumeOtherInfo(recruitResumeOtherInfo));
    }

    /**
     * 修改简历其它信息
     */
    @PreAuthorize("@ss.hasPermi('core:resumeOtherInfo:edit')")
    @Log(title = "简历其它信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RecruitResumeOtherInfo recruitResumeOtherInfo)
    {
        return toAjax(recruitResumeOtherInfoService.updateRecruitResumeOtherInfo(recruitResumeOtherInfo));
    }

    /**
     * 删除简历其它信息
     */
    @PreAuthorize("@ss.hasPermi('core:resumeOtherInfo:remove')")
    @Log(title = "简历其它信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(recruitResumeOtherInfoService.deleteRecruitResumeOtherInfoByIds(ids));
    }
}
