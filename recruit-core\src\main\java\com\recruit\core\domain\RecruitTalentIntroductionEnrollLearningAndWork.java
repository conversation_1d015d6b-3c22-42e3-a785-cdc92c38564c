package com.recruit.core.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.recruit.common.annotation.Excel;
import com.recruit.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 人才引进报名学习工作对象 recruit_talent_introduction_enroll_learning_and_work
 *
 * <AUTHOR>
 * @date 2024-03-15
 */
@Data
@ApiModel("人才引进报名学习工作")
public class RecruitTalentIntroductionEnrollLearningAndWork extends BaseEntity
{
    private static final long serialVersionUID = 1L;


    private Long id;


    @ApiModelProperty("人才引进报名id")
    private Long talentIntroductionEnrollId;

    @ApiModelProperty("序号")
    private Integer index;

    @Excel(name = "序号", cellType = Excel.ColumnType.NUMERIC)
    @ApiModelProperty("序号")
    private String sequence;


    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "开始时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("开始时间")
    private Date startDate;


    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "结束时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("结束时间")
    private Date endDate;


    @Excel(name = "学校或工作单位")
    @ApiModelProperty("学校或工作单位")
    private String schoolOrWorkplace;


    @Excel(name = "职务")
    @ApiModelProperty("职务")
    private String duties;


}
