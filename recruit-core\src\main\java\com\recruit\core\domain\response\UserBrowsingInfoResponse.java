package com.recruit.core.domain.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @Auther: <PERSON> kong
 * @Date: 2023/4/12 14:17
 * @Description:
 */
@Data
@ApiModel("招聘查看用户浏览信息")
public class UserBrowsingInfoResponse {

    private Long id;

    @ApiModelProperty("发布人id")
    private Long publisherId;

    @ApiModelProperty("用户id")
    private Long userId;

    @ApiModelProperty("类型 1 求职者，2招聘者")
    private String type;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @ApiModelProperty("头像")
    private String headSculpture;

    @ApiModelProperty("姓名")
    private String userName;

    @ApiModelProperty("工作经验")
    private String workExperience;

    @ApiModelProperty("工作经验名称")
    private String workExperienceName;

    @ApiModelProperty("最高学历")
    private String education;

    @ApiModelProperty("最高学历名称")
    private String educationName;

    @ApiModelProperty("期望岗位")
    private String expectedPosition;

    @ApiModelProperty("期望岗位名称")
    private String expectedPositionName;

    @ApiModelProperty("自我评价")
    private String selfEvaluation;

    @ApiModelProperty("最高薪资")
    private String maximumSalary;

    @ApiModelProperty("最低薪资")
    private String minimumWage;

}
