package com.recruit.core.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.recruit.common.annotation.Excel;
import com.recruit.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 订单评价对象 temporary_order_evaluate
 *
 * <AUTHOR>
 * @date 2023-06-03
 */
@Data
@ApiModel("订单评价")
public class TemporaryOrderEvaluate extends BaseEntity
{
    private static final long serialVersionUID = 1L;


    private Long id;

    @ApiModelProperty("商户id")
    private Long merchantInfoId;


    @Excel(name = "用户订单id")
    @ApiModelProperty("用户订单id")
    private Long serviceUserOrderId;


    @Excel(name = "下单人")
    @ApiModelProperty("下单人")
    private Long placeOrderUserId;


    @Excel(name = "评价内容")
    @ApiModelProperty("评价内容")
    private String evaluationContent;


    @Excel(name = "评价星级 1-5")
    @ApiModelProperty("评价星级 1-5")
    private String evaluateStar;

    @ApiModelProperty("联系人")
    private String liaisonNan;

    @ApiModelProperty("联系人手机号")
    private String placeOrderPhone;

    @ApiModelProperty("服务类别")
    private String serviceGoodsName;

    @ApiModelProperty("服务名称")
    private String serviceGoodsPriceName;

    @ApiModelProperty("商户名称")
    private String serviceName;


    @ApiModelProperty("服务商联系人")
    private String userName;


    @ApiModelProperty("联系手机号")
    private String phone;


    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date orderTime;
}
