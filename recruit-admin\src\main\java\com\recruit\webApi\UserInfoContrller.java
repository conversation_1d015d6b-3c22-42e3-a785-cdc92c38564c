package com.recruit.webApi;

import com.recruit.common.constant.HttpStatus;
import com.recruit.common.constant.UserConstants;
import com.recruit.common.core.controller.BaseController;
import com.recruit.common.core.domain.AjaxResult;
import com.recruit.common.utils.DateUtils;
import com.recruit.common.utils.DesensitizeUtil;
import com.recruit.common.utils.EmailUtil;
import com.recruit.common.utils.IdCardUtil;
import com.recruit.common.utils.StringUtils;
import com.recruit.core.domain.RecruitUserInfo;
import com.recruit.core.domain.request.common.AuthorizationBindingRequest;
import com.recruit.core.domain.request.common.CertificationRequest;
import com.recruit.core.domain.request.common.HeadSculptureRequest;
import com.recruit.core.domain.request.common.IdentityRequest;
import com.recruit.core.domain.request.common.MailboxRequest;
import com.recruit.core.domain.request.common.ModifyRequest;
import com.recruit.core.domain.request.common.PasswordRequest;
import com.recruit.core.domain.request.common.WeChatRequest;
import com.recruit.core.domain.response.ContactsInfoResponse;
import com.recruit.core.service.IRecruitUserInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotBlank;

/**
 * @Auther: Wu kong
 * @Date: 2023/3/19 9:00
 * @Description:
 */
@Api(tags= "用户基本信息管理")
@Slf4j
@RestController
@RequestMapping("/web/api/user")
public class UserInfoContrller extends BaseController {

    @Autowired
    private IRecruitUserInfoService recruitUserInfoService;


    @ApiOperation("切换身份 1：求职者 2：招聘者, 3商户")
    @PostMapping("/selectIdentity")
    public AjaxResult selectIdentity(@RequestBody @Validated IdentityRequest request) {
        return toAjax(recruitUserInfoService.selectIdentity(request));
    }

    @ApiOperation("修改手机号")
    @PostMapping("/updatePhone")
    public AjaxResult updatePhone(@RequestBody @Validated ModifyRequest request) {
        return toAjax(recruitUserInfoService.updatePhone(request));
    }

    @ApiOperation("修改头像")
    @PostMapping("/updateHeadSculpture")
    public AjaxResult updateHeadSculpture(@RequestBody @Validated HeadSculptureRequest request) {
        return toAjax(recruitUserInfoService.updateHeadSculpture(request));
    }

    @ApiOperation("设置密码及修改密码")
    @PostMapping("/setPasswordAndUpdatePassword")
    public AjaxResult setPasswordAndUpdatePassword(@RequestBody @Validated PasswordRequest request) {
        return recruitUserInfoService.setPasswordAndUpdatePassword(request);
    }

    @ApiOperation("设置邮箱")
    @PostMapping("/setMailbox")
    public AjaxResult setMailbox(@RequestBody @Validated MailboxRequest request) {
        //校验邮箱
        if(request.getMailbox() != null && !StringUtils.equals(request.getMailbox(), "")) {
            if (!EmailUtil.emailFormat(request.getMailbox())) {
                return AjaxResult.error(HttpStatus.UNSUPPORTED_TYPE, "邮箱格式不正确");
            }
        }
        return toAjax(recruitUserInfoService.setMailbox(request));
    }


    @ApiOperation("设置微信号")
    @PostMapping("/setWeChat")
    public AjaxResult setWeChat(@RequestBody @Validated WeChatRequest request) {
        return toAjax(recruitUserInfoService.updateWechat(request));
    }

    @ApiOperation("获取用户信息")
    @GetMapping(value = "/getUserInfo", produces = {"application/json"})
    @ApiResponses({
            @ApiResponse(code = 200, message = "返回实体", response = RecruitUserInfo.class)
    })
    public AjaxResult getUserInfo()
    {
        try {
            return success(recruitUserInfoService.selectRecruitUserInfoById(getUserId()));
        }catch (Exception e){
            log.error("获取用户id失败，该用户未登录，可忽略该信息");
        }
        return error();
    }


    @ApiOperation("实名认证前校验是否绑定其它账号，如果绑定返回 true,未绑定返回 false,如果存在则需要用户确定是否继续绑定")
    @PostMapping(value = "/verifyRealNameAuthentication")
    public AjaxResult verifyRealNameAuthentication(@RequestBody @Validated CertificationRequest request)
    {
        RecruitUserInfo userInfo = new RecruitUserInfo();
        userInfo.setIdCard(request.getIdCard());
        AjaxResult ajax = AjaxResult.success();
        if (!recruitUserInfoService.checkIdCardUnique2(userInfo))
        {
            ajax.put("bindingInfo", true);
            return ajax;
        }else {
            ajax.put("bindingInfo", false);
            return ajax;
        }
    }

    @ApiOperation("实名认证")
    @PostMapping(value = "/certification")
    public AjaxResult certification(@RequestBody @Validated CertificationRequest request)
    {
        if(request.getIdCard() == null && StringUtils.equals(request.getIdCard(), "")){
            return AjaxResult.error(HttpStatus.UNSUPPORTED_TYPE, "身份证号不能为空！");
        }
        //情况之前绑定身份号信息
        recruitUserInfoService.emptyingUsersIdCardUserInfo(request.getIdCard());

        //校验身份证号
        String identityCard = IdCardUtil.IdentityCardVerification(request.getIdCard());
        if(!StringUtils.equals(identityCard, "correct")){
            return AjaxResult.error(HttpStatus.UNSUPPORTED_TYPE, identityCard);
        }
        RecruitUserInfo recruitUserInfo = new RecruitUserInfo();
        recruitUserInfo.setId(getUserId());
        recruitUserInfo.setAuthenticationName(request.getUserName());
        recruitUserInfo.setUserName(request.getUserName());
        recruitUserInfo.setIdCard(request.getIdCard());
        //性别
        recruitUserInfo.setSex(IdCardUtil.getSex(recruitUserInfo.getIdCard()));
        //出生年月
        recruitUserInfo.setDateOfBirth(DateUtils.dateTime(DateUtils.YYYY_MM_DD, IdCardUtil.getBirthday(recruitUserInfo.getIdCard())));

        return toAjax(recruitUserInfoService.updateRecruitUserInfo(recruitUserInfo));
    }


    @ApiOperation("微信授权绑定")
    @PostMapping(value = "/WxAuthorizationBinding")
    public AjaxResult WxAuthorizationBinding(@RequestBody @Validated AuthorizationBindingRequest request)
    {
        RecruitUserInfo recruitUserInfo = new RecruitUserInfo();
        recruitUserInfo.setId(getUserId());
        recruitUserInfo.setOpenId(request.getOpenId());
        return toAjax(recruitUserInfoService.updateRecruitUserInfo(recruitUserInfo));
    }


    @ApiOperation("校验是否用户是否实名认证， 0未认证，1已认证")
    @GetMapping("/verifyRealName")
    public AjaxResult verifyRealName() {
        RecruitUserInfo recruitUserInfo = recruitUserInfoService.selectRecruitUserInfoById(getUserId());
        AjaxResult ajax = AjaxResult.success();
        if(recruitUserInfo.getIdCard() != null && !StringUtils.equals(recruitUserInfo.getIdCard(), "")){
            ajax.put("isRealName", "1");
        }else {
            ajax.put("isRealName", "0");
        }
        return ajax;
    }


    @ApiOperation("根据用户id获取用户信息")
    @GetMapping(value = "/getUserInfoByUserId", produces = {"application/json"})
    @ApiResponses({
            @ApiResponse(code = 200, message = "返回实体", response = RecruitUserInfo.class)
    })
    public AjaxResult getUserInfoByUserId(@NotBlank(message = "用户id不能为空") Long userId)
    {
        return success(recruitUserInfoService.selectRecruitUserInfoById(userId));
    }


    @ApiOperation("微信订阅")
    @PostMapping("/weChatSubscribeTo")
    public AjaxResult weChatSubscribeTo() {
        return toAjax(recruitUserInfoService.weChatSubscribeTo());
    }


    @ApiOperation("查看微信订阅")
    @GetMapping("/getWeChatSubscribeTo")
    public AjaxResult getWeChatSubscribeTo() {
        RecruitUserInfo userInfo = recruitUserInfoService.selectRecruitUserInfoById(getUserId());
        AjaxResult ajax = AjaxResult.success();
        if(userInfo != null){
            if(userInfo.getSubscriptionStatus() != null){
                if(userInfo.getSubscriptionStatus() == 1){
                    ajax.put("subscriptionStatus", true);
                }else {
                    ajax.put("subscriptionStatus", false);
                }
            }else {
                ajax.put("subscriptionStatus", false);
            }
        }else {
            ajax.put("subscriptionStatus", false);
        }
        return success(ajax);
    }

}
