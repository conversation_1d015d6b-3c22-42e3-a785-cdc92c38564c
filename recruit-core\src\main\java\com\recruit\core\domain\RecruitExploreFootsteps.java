package com.recruit.core.domain;

import com.recruit.common.annotation.Excel;
import com.recruit.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 浏览足迹对象 recruit_explore_footsteps
 *
 * <AUTHOR>
 * @date 2023-04-12
 */
@Data
@ApiModel("浏览足迹")
public class RecruitExploreFootsteps extends BaseEntity
{
    private static final long serialVersionUID = 1L;


    private Long id;


    @Excel(name = "职位id")
    @ApiModelProperty("职位id")
    private Long positionInfoId;


    @Excel(name = "企业id")
    @ApiModelProperty("企业id")
    private Long enterpriseId;

    @ApiModelProperty("发布人id")
    private Long publisherId;


    @Excel(name = "用户id")
    @ApiModelProperty("用户id")
    private Long userId;


    @ApiModelProperty("类型 1 求职者，2招聘者")
    private String type;




}
