package com.recruit.core.domain.request.merchant;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;

/**
 * @Auther: <PERSON> kong
 * @Date: 2023/6/3 17:40
 * @Description:
 */
@Data
public class UserPlacesAnOrderRequest {

    @ApiModelProperty("下单人手机号")
    private String placeOrderPhone;

    @ApiModelProperty("商品id")
    private Long serviceGoodsId;

    @ApiModelProperty("商品价格id")
    private Long serviceGoodsPriceId;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("预约日期")
    private Date arrivalTime;

    @ApiModelProperty("到门时间")
    private String toDoorTime;

    @ApiModelProperty("支付渠道 1微信，2支付宝")
    private String payChannel;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("类型1下单，2预约")
    private String type;
}
