package com.recruit.web.controller.core.merchant;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.regex.Pattern;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import com.recruit.common.constant.HttpStatus;
import com.recruit.common.notify.NotifyService;
import com.recruit.common.utils.PhoneUtil;
import com.recruit.common.utils.StringUtils;
import com.recruit.core.domain.*;
import com.recruit.core.service.IRecruitUserInfoService;
import com.recruit.core.service.ISysServiceTradeService;
import com.recruit.core.service.ISysSmsSendingRecordService;
import com.recruit.core.service.ISysSmsTemplateService;
import com.recruit.core.service.ITemporaryServiceUserOrderService;
import com.recruit.system.service.ISysConfigService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.recruit.common.annotation.Log;
import com.recruit.common.core.controller.BaseController;
import com.recruit.common.core.domain.AjaxResult;
import com.recruit.common.enums.BusinessType;
import org.springframework.web.multipart.MultipartFile;
import com.recruit.core.service.IMerchantInfoEntryService;
import com.recruit.common.utils.poi.ExcelUtil;
import com.recruit.common.core.page.TableDataInfo;

/**
 * 服务申请入驻Controller
 *
 * <AUTHOR>
 * @date 2023-05-27
 */
@RestController
@RequestMapping("/core/merchantInfo")
public class MerchantInfoController extends BaseController
{
    @Autowired
    private ISysConfigService configService;

    @Resource
    private NotifyService notifyService;

    @Autowired
    private ISysSmsTemplateService sysSmsTemplateService;

    @Autowired
    private ISysSmsSendingRecordService sysSmsSendingRecordService;

    @Autowired
    private IRecruitUserInfoService recruitUserInfoService;

    @Autowired
    private ISysServiceTradeService sysServiceTradeService;

    @Autowired
    private IMerchantInfoEntryService merchantInfoEntryService;

    @Autowired
    private ITemporaryServiceUserOrderService temporaryServiceUserOrderService;

    /**
     * 查询服务申请入驻列表
     */
    @PreAuthorize("@ss.hasPermi('core:merchantInfo:list')")
    @GetMapping("/list")
    public TableDataInfo list(MerchantInfoEntry merchantInfo)
    {
        startPage();
        List<MerchantInfoEntry> list = merchantInfoEntryService.selectMerchantInfoEntryList(merchantInfo);
        list.forEach(e->{
            String[] serviceTrade = e.getServiceTrade().split(",");
            StringBuilder serviceTradeName = new StringBuilder();
            for(String s : serviceTrade){
                if(StringUtils.equals(serviceTradeName, "")){
                    serviceTradeName.append(sysServiceTradeService.getMap(s));
                }else {
                    serviceTradeName.append(",").append(sysServiceTradeService.getMap(s));
                }
            }
            e.setServiceTradeName(String.valueOf(serviceTradeName));

            if(e.getAccountAmount() == null){
                e.setAccountAmount(new BigDecimal(0));
            }
            if(e.getEarnestMoney() == null){
                e.setEarnestMoney(new BigDecimal(0));
            }
        });
        return getDataTable(list);
    }

    /**
     * 导出服务申请入驻列表
     */
    @PreAuthorize("@ss.hasPermi('core:merchantInfo:export')")
    @Log(title = "服务申请入驻", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MerchantInfoEntry merchantInfo)
    {
        List<MerchantInfoEntry> list = merchantInfoEntryService.selectMerchantInfoEntryList(merchantInfo);
        ExcelUtil<MerchantInfoEntry> util = new ExcelUtil<MerchantInfoEntry>(MerchantInfoEntry.class);
        util.exportExcel(response, list, "商户信息数据");
    }

    /**
     * 导出商户信息模板
     */
    @Log(title = "导出商户信息", businessType = BusinessType.EXPORT)
    @GetMapping("/importTemplate")
    public AjaxResult importTemplate()
    {
        ExcelUtil<MerchantInfoEntry> util = new ExcelUtil<>(MerchantInfoEntry.class);
        return util.importTemplateExcel("商户信息模版");
    }

    /**
     * 获取服务申请入驻详细信息
     */
    @PreAuthorize("@ss.hasPermi('core:merchantInfo:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        MerchantInfoEntry merchantInfo = merchantInfoEntryService.selectMerchantInfoEntryById(id);
        if(merchantInfo.getServiceTrade() != null && !StringUtils.equals(merchantInfo.getServiceTrade(), "")){
            String[] serviceTrade = merchantInfo.getServiceTrade().split(",");
            List<String> serviceTrades = new ArrayList<>(Arrays.asList(serviceTrade));
            merchantInfo.setServiceTrades(serviceTrades);
        }
        if(merchantInfo.getRegion() != null && !StringUtils.equals(merchantInfo.getRegion(), "")){
            List<String> regions = new ArrayList<>(Arrays.asList(merchantInfo.getRegion().split(",")));
            merchantInfo.setRegions(regions);
        }
        return success(merchantInfo);
    }

    /**
     * 新增服务申请入驻
     */
    @PreAuthorize("@ss.hasPermi('core:merchantInfo:add')")
    @Log(title = "服务申请入驻", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MerchantInfoEntry merchantInfo)
    {
        //判断用户id是否是数字
        RecruitUserInfo userInfo = new RecruitUserInfo();
        if(merchantInfo.getUserId() != 0){
            userInfo = recruitUserInfoService.selectRecruitUserInfoById(merchantInfo.getUserId());
        }else {
            RecruitUserInfo userInfos = recruitUserInfoService.getPhoneUserInfo(merchantInfo.getPhone());
            if(userInfos != null){
                if(!merchantInfo.getUserName().equals(userInfos.getUserName())){
                    throw new RuntimeException("手机号已存在《"+userInfos.getUserName()+"》用户中！");
                }
            }
            userInfo.setUserName(merchantInfo.getUserName());
            userInfo.setNickname(merchantInfo.getUserName());
            userInfo.setPhone(merchantInfo.getPhone());
            userInfo.setType("0");
            userInfo.setStatus(0);
            userInfo.setSex("0");
            userInfo.setIdCard(merchantInfo.getIdCard());
            userInfo.setHeadSculpture(configService.selectConfigByKey("shop_default_avatar"));
            userInfo.setNewUser(0);
            recruitUserInfoService.insertRecruitUserInfo(userInfo);
        }
        merchantInfo.setUserId(userInfo.getId());
        merchantInfo.setUserName(userInfo.getUserName());
        merchantInfo.setMarginStatus("0");
        //校验手机号
        String verifyPhone = PhoneUtil.verifyPhone(merchantInfo.getPhone());
        if(!StringUtils.equals(verifyPhone, "success")){
            return AjaxResult.error(HttpStatus.UNSUPPORTED_TYPE, verifyPhone);
        }
        if (merchantInfo.getServiceTrades() != null) {
            StringBuilder serviceTrade = new StringBuilder();
            merchantInfo.getServiceTrades().forEach(e->{
                if(StringUtils.equals(serviceTrade, "")){
                    serviceTrade.append(e);
                }else {
                    serviceTrade.append(",").append(e);
                }
            });
            merchantInfo.setServiceTrade(String.valueOf(serviceTrade));
        }
        if (merchantInfo.getRegions() != null) {
            StringBuilder region = new StringBuilder();
            merchantInfo.getRegions().forEach(e->{
                if(StringUtils.equals(region, "")){
                    region.append(e);
                }else {
                    region.append(",").append(e);
                }
            });
            merchantInfo.setRegion(String.valueOf(region));
        }
        merchantInfo.setAuditTime(new Date());
        int ss = merchantInfoEntryService.insertMerchantInfoEntry(merchantInfo);
        if(ss > 0){
            if(merchantInfo.getApplicationStatus().equals("1")){
                if(merchantInfo.getPhone() != null && !StringUtils.equals(merchantInfo.getPhone(), "")){
                    sendOddJobMessage(merchantInfo.getPhone(), Long.valueOf(merchantInfo.getUserId()));
                }else {
                    throw new RuntimeException("联系手机号不能为空！");
                }
            }
        }
        return toAjax(ss);
    }

    /**
     * 修改服务申请入驻
     */
    @PreAuthorize("@ss.hasPermi('core:merchantInfo:edit')")
    @Log(title = "服务申请入驻", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MerchantInfoEntry merchantInfo)
    {
        //判断用户id是否是数字
        RecruitUserInfo userInfo = new RecruitUserInfo();
        if(merchantInfo.getUserId() != 0){
            userInfo = recruitUserInfoService.selectRecruitUserInfoById(merchantInfo.getUserId());
        }else {
            RecruitUserInfo userInfos = recruitUserInfoService.getPhoneUserInfo(merchantInfo.getPhone());
            if(userInfos != null){
                if(!merchantInfo.getUserName().equals(userInfos.getUserName())){
                    throw new RuntimeException("手机号已存在《"+userInfos.getUserName()+"》用户中！");
                }
            }
            userInfo.setUserName(merchantInfo.getUserName());
            userInfo.setNickname(merchantInfo.getUserName());
            userInfo.setPhone(merchantInfo.getPhone());
            userInfo.setType("0");
            userInfo.setStatus(0);
            userInfo.setSex("0");
            userInfo.setIdCard(merchantInfo.getIdCard());
            userInfo.setHeadSculpture(configService.selectConfigByKey("shop_default_avatar"));
            userInfo.setNewUser(0);
            recruitUserInfoService.insertRecruitUserInfo(userInfo);
        }
        merchantInfo.setUserId(userInfo.getId());
        merchantInfo.setUserName(userInfo.getUserName());
        //校验手机号
        String verifyPhone = PhoneUtil.verifyPhone(merchantInfo.getPhone());
        if(!StringUtils.equals(verifyPhone, "success")){
            return AjaxResult.error(HttpStatus.UNSUPPORTED_TYPE, verifyPhone);
        }
        if (merchantInfo.getServiceTrades() != null) {
            StringBuilder serviceTrade = new StringBuilder();
            merchantInfo.getServiceTrades().forEach(e->{
                if(StringUtils.equals(serviceTrade, "")){
                    serviceTrade.append(e);
                }else {
                    serviceTrade.append(",").append(e);
                }
            });
            merchantInfo.setServiceTrade(String.valueOf(serviceTrade));
        }
        if (merchantInfo.getRegions() != null) {
            StringBuilder region = new StringBuilder();
            merchantInfo.getRegions().forEach(e->{
                if(StringUtils.equals(region, "")){
                    region.append(e);
                }else {
                    region.append(",").append(e);
                }
            });
            merchantInfo.setRegion(String.valueOf(region));
        }
        merchantInfo.setAuditBy(getUsername());
        merchantInfo.setAuditTime(new Date());
        int ss = merchantInfoEntryService.updateMerchantInfoEntry(merchantInfo);
        if(ss > 0){
            if(merchantInfo.getApplicationStatus().equals("1")){
                if(merchantInfo.getPhone() != null && !StringUtils.equals(merchantInfo.getPhone(), "")){
                    sendOddJobMessage(merchantInfo.getPhone(), Long.valueOf(merchantInfo.getUserId()));
                }else {
                    throw new RuntimeException("联系手机号不能为空！");
                }
            }
        }
        return toAjax(ss);
    }

    /**
     * 推送零工认证消息
     */
    private void sendOddJobMessage(String phone, Long userId){
        SysSmsTemplate smsTemplate = sysSmsTemplateService.selectSmsTemplateBySendType("7");
        if(smsTemplate != null){
            notifyService.notifySms(phone, smsTemplate.getTemplateContent(), String.valueOf(smsTemplate.getTemplateId()));

            //记录 零工认证消息 记录
            SysSmsSendingRecord smsSendingRecord = new SysSmsSendingRecord();
            smsSendingRecord.setUserId(userId);
            smsSendingRecord.setPhone(phone);
            smsSendingRecord.setNoticeTitle(smsTemplate.getTemplateName());
            smsSendingRecord.setNoticeType("1");
            smsSendingRecord.setNoticeContent(smsTemplate.getTemplateContent());
            smsSendingRecord.setSentType("1");
            smsSendingRecord.setSmsTemplateId(smsTemplate.getId());
            smsSendingRecord.setSendTime(new Date());
            sysSmsSendingRecordService.insertSysSmsSendingRecord(smsSendingRecord);
        }
    }

    /**
     * 删除服务申请入驻
     */
    @PreAuthorize("@ss.hasPermi('core:merchantInfo:remove')")
    @Log(title = "服务申请入驻", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(merchantInfoEntryService.deleteMerchantInfoEntryByIds(ids));
    }


    /**
     * 导入服务申请入驻
     * @param file
     * @param updateSupport
     * @return
     * @throws Exception
     */
    @PreAuthorize("@ss.hasPermi('core:merchantInfo:import')")
    @Log(title = "服务申请入驻", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<MerchantInfoEntry> util = new ExcelUtil<>(MerchantInfoEntry.class);
        List<MerchantInfoEntry> lists = util.importExcel(file.getInputStream());
        String message = merchantInfoEntryService.importMerchantInfoEntry(lists, updateSupport);
        return AjaxResult.success(message);
    }


    @GetMapping("/merchantInfoListAll")
    public TableDataInfo merchantInfoListAll(MerchantInfoEntry merchantInfo)
    {
        List<MerchantInfoEntry> list = merchantInfoEntryService.selectMerchantInfoEntryList(merchantInfo);
        return getDataTable(list);
    }

    /**
     * 根据地图找服务
     * @param merchantInfo
     * @return
     */
    @GetMapping("/getMerchantInfoList")
    public TableDataInfo getMerchantInfoList(MerchantInfoEntry merchantInfo)
    {
        switch (merchantInfo.getLinearDistance()){
            case "0":
                merchantInfo.setLinearDistance(null);
                break;
            case "1":
                merchantInfo.setLinearDistance("1000");
                break;
            case "2":
                merchantInfo.setLinearDistance("3000");
                break;
            case "3":
                merchantInfo.setLinearDistance("5000");
                break;
            case "4":
                merchantInfo.setLinearDistance("7000");
                break;
            case "5":
                merchantInfo.setLinearDistance("10000");
                break;
        }
        TemporaryServiceUserOrder serviceUserOrder = temporaryServiceUserOrderService.selectTemporaryServiceUserOrderById(merchantInfo.getId());
        if(serviceUserOrder == null){
            throw new RuntimeException("该订单信息不存在！");
        }

        //经度
        merchantInfo.setLongitude(serviceUserOrder.getLongitude());
        //纬度
        merchantInfo.setLatitude(serviceUserOrder.getLatitude());
        //服务行业编码
        merchantInfo.setServiceTrade(serviceUserOrder.getServiceTrade());
        //申请状态
        merchantInfo.setApplicationStatus("1");
        List<MerchantInfoEntry> list = merchantInfoEntryService.getMerchantInfoList(merchantInfo);
        return getDataTable(list);
    }

}
