package com.recruit.web.controller.core.resume;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.recruit.common.annotation.Log;
import com.recruit.common.core.controller.BaseController;
import com.recruit.common.core.domain.AjaxResult;
import com.recruit.common.enums.BusinessType;
import com.recruit.core.domain.RecruitResumeEducation;
import com.recruit.core.service.IRecruitResumeEducationService;
import com.recruit.common.utils.poi.ExcelUtil;
import com.recruit.common.core.page.TableDataInfo;

/**
 * 简历教育经历Controller
 *
 * <AUTHOR>
 * @date 2023-03-19
 */
@RestController
@RequestMapping("/core/resumeEducation")
public class RecruitResumeEducationController extends BaseController
{
    @Autowired
    private IRecruitResumeEducationService recruitResumeEducationService;

    /**
     * 查询简历教育经历列表
     */
    @PreAuthorize("@ss.hasPermi('core:resumeEducation:list')")
    @GetMapping("/list")
    public TableDataInfo list(RecruitResumeEducation recruitResumeEducation)
    {
        startPage();
        List<RecruitResumeEducation> list = recruitResumeEducationService.selectRecruitResumeEducationList(recruitResumeEducation);
        return getDataTable(list);
    }

    /**
     * 导出简历教育经历列表
     */
    @PreAuthorize("@ss.hasPermi('core:resumeEducation:export')")
    @Log(title = "简历教育经历", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RecruitResumeEducation recruitResumeEducation)
    {
        List<RecruitResumeEducation> list = recruitResumeEducationService.selectRecruitResumeEducationList(recruitResumeEducation);
        ExcelUtil<RecruitResumeEducation> util = new ExcelUtil<RecruitResumeEducation>(RecruitResumeEducation.class);
        util.exportExcel(response, list, "简历教育经历数据");
    }

    /**
     * 获取简历教育经历详细信息
     */
    @PreAuthorize("@ss.hasPermi('core:resumeEducation:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(recruitResumeEducationService.selectRecruitResumeEducationById(id));
    }

    /**
     * 新增简历教育经历
     */
    @PreAuthorize("@ss.hasPermi('core:resumeEducation:add')")
    @Log(title = "简历教育经历", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RecruitResumeEducation recruitResumeEducation)
    {
        return toAjax(recruitResumeEducationService.insertRecruitResumeEducation(recruitResumeEducation));
    }

    /**
     * 修改简历教育经历
     */
    @PreAuthorize("@ss.hasPermi('core:resumeEducation:edit')")
    @Log(title = "简历教育经历", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RecruitResumeEducation recruitResumeEducation)
    {
        return toAjax(recruitResumeEducationService.updateRecruitResumeEducation(recruitResumeEducation));
    }

    /**
     * 删除简历教育经历
     */
    @PreAuthorize("@ss.hasPermi('core:resumeEducation:remove')")
    @Log(title = "简历教育经历", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(recruitResumeEducationService.deleteRecruitResumeEducationByIds(ids));
    }
}
