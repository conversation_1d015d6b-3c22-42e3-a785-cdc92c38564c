package com.recruit.core.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.recruit.common.annotation.Excel;
import com.recruit.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 网络招聘会对象 recruit_online_job_fairs
 *
 * <AUTHOR>
 * @date 2023-04-21
 */
@Data
@ApiModel("网络招聘会")
public class RecruitPopularEnterprises extends BaseEntity
{
    private static final long serialVersionUID = 1L;
    private Long id;
    @Excel(name = "企业id")
    @ApiModelProperty("企业id")
    private Long enterpriseId;

    @ApiModelProperty("网络招聘会id")
    private Long onlineJobFairsId;
    @ApiModelProperty("企业名称")
    private String enterpriseName;
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "开始时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("开始时间")
    private Date startTime;
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "结束时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("结束时间")
    private Date endTime;
    @Excel(name = "状态，1发布中，0发布结束记录保留")
    @ApiModelProperty("状态，1发布中，0发布结束记录保留")
    private String state;
    @Excel(name = "排序")
    @ApiModelProperty("排序")
    private Integer sort;
    @Excel(name = "1网络招聘会，2热门企业")
    @ApiModelProperty("1网络招聘会，2热门企业")
    private String type;





    @ApiModelProperty("所在地区")
    private String region;

    @ApiModelProperty("工作经验")
    private String workExperience;

    @ApiModelProperty("最低学历")
    private String minimumEducation;

    @ApiModelProperty("性别要求")
    private String genderRequirements;

    @ApiModelProperty("企业性质")
    private String enterpriseNature;

    @ApiModelProperty("企业规模")
    private String scale;

    @ApiModelProperty("最高薪资")
    private String maximumSalary;

    @ApiModelProperty("最低薪资")
    private String minimumWage;

    @ApiModelProperty("职位")
    private String position;


    private Integer grade;

}
