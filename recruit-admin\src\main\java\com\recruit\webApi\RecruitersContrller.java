package com.recruit.webApi;

import com.recruit.common.core.controller.BaseController;
import com.recruit.common.core.domain.AjaxResult;
import com.recruit.common.core.page.TableDataInfo;
import com.recruit.common.utils.DesensitizeUtil;
import com.recruit.common.utils.DictUtils;
import com.recruit.common.utils.StringUtils;
import com.recruit.core.domain.RecruitEnterpriseBusinessInfo;
import com.recruit.core.domain.RecruitEnterpriseInfo;
import com.recruit.core.domain.RecruitEnterpriseUsersRel;
import com.recruit.core.domain.RecruitPositionInfo;
import com.recruit.core.domain.request.enterprise.SetUpMapRequest;
import com.recruit.core.domain.response.ContactsResponse;
import com.recruit.core.domain.request.enterprise.EnterpriseBasicsInfoRequest;
import com.recruit.core.domain.request.enterprise.EnterpriseQualificationRequest;
import com.recruit.core.domain.request.enterprise.IndividualQualificationRequest;
import com.recruit.core.domain.request.enterprise.UploadEnvironmentRequest;
import com.recruit.core.domain.response.ContactsInfoResponse;
import com.recruit.core.service.IRecruitEnterpriseBusinessInfoService;
import com.recruit.core.service.IRecruitEnterpriseInfoService;
import com.recruit.core.service.IRecruitEnterpriseUsersRelService;
import com.recruit.core.service.IRecruitUserInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotBlank;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Auther: Wu kong
 * @Date: 2023/3/19 9:06
 * @Description:
 */
@Api(tags= "招聘者基本信息管理(6-6新增《更企业营业执照信息》)")
@RestController
@RequestMapping("/web/api/recruiters")
public class RecruitersContrller  extends BaseController {

    @Autowired
    private IRecruitEnterpriseInfoService recruitEnterpriseInfoService;

    @Autowired
    private IRecruitUserInfoService recruitUserInfoService;

    @Autowired
    private IRecruitEnterpriseUsersRelService recruitEnterpriseUsersRelService;

    @Autowired
    private IRecruitEnterpriseBusinessInfoService recruitEnterpriseBusinessInfoService;

    @ApiOperation("校验是否绑定企业, 0未绑定，1已绑定")
    @GetMapping("/verifyRealName")
    public AjaxResult verifyRealName() {
        AjaxResult ajax = AjaxResult.success();
        int ss = recruitEnterpriseUsersRelService.selectByUserId(getUserId());
        ajax.put("isCertification", ss);
        return ajax;
    }

    @ApiOperation("解除企业绑定")
    @GetMapping("/unbindTheEnterprise")
    public AjaxResult unbindTheEnterprise() {
        return toAjax(recruitEnterpriseUsersRelService.unbindTheEnterprise(getUserId()));
    }

    @ApiOperation("新增联系人信息")
    @PostMapping("/addContacts")
    public AjaxResult addContacts(@RequestBody @Validated ContactsResponse request) {
        return toAjax(recruitUserInfoService.addContacts(request));
    }

    @ApiOperation("校验企业是否存在, 0 不存在，1存在")
    @GetMapping("/verificationEnterprise")
    public AjaxResult verificationEnterprise(@NotBlank(message = "企业名称不能为空") String enterpriseName) {
        return recruitEnterpriseInfoService.verificationEnterprise(enterpriseName);
    }

    @ApiOperation("添加企业基本信息")
    @PostMapping("/addEnterpriseInfo")
    public AjaxResult enterpriseInfo(@RequestBody @Validated EnterpriseBasicsInfoRequest request) {
        return toAjax(recruitEnterpriseInfoService.addEnterpriseInfo(request));
    }

    @ApiOperation("设置地图")
    @PostMapping("/setUpMap")
    public AjaxResult setUpMap(@RequestBody @Validated SetUpMapRequest request) {
        return toAjax(recruitEnterpriseInfoService.setUpMap(request));
    }



    @ApiOperation("添加企业资质认证")
    @PostMapping("/addQualification")
    public AjaxResult addQualification(@RequestBody @Validated EnterpriseQualificationRequest request) {
        return toAjax(recruitEnterpriseBusinessInfoService.addQualification(request));
    }

    @ApiOperation("添加个人企业资质认证")
    @PostMapping("/addIndividualQualification")
    public AjaxResult addIndividualQualification(@RequestBody @Validated IndividualQualificationRequest request) {
        return toAjax(recruitEnterpriseUsersRelService.addIndividualQualification(request));
    }

    @ApiOperation("获取联系人信息")
    @GetMapping(value = "/getContactsInfo", produces = {"application/json"})
    @ApiResponses({
            @ApiResponse(code = 200, message = "返回实体", response = ContactsInfoResponse.class)
    })
    public AjaxResult getContactsInfo() {
        ContactsInfoResponse response = recruitUserInfoService.getContactsInfo();
        if(response != null){
            response.setAuthenticationName(DesensitizeUtil.left(response.getAuthenticationName(), 1));
        }
        return success(response);
    }


    @ApiOperation("获取企业是否存在")
    @GetMapping(value = "/getEnterpriseInfo", produces = {"application/json"})
    @ApiResponses({
            @ApiResponse(code = 200, message = "返回实体", response = RecruitEnterpriseInfo.class)
    })
    public AjaxResult getEnterpriseInfo(@NotBlank(message = "企业名称不能为空") String enterpriseName) {
        return success(recruitEnterpriseInfoService.getEnterpriseInfo(enterpriseName));
    }

    @ApiOperation("获取企业工商信息, 可以当作企业资质认证状态审核 状态 0待审核，1审核通过，2审核不通过，3违规封号")
    @GetMapping(value = "/getQualification", produces = {"application/json"})
    @ApiResponses({
            @ApiResponse(code = 200, message = "返回实体", response = RecruitEnterpriseBusinessInfo.class)
    })
    public AjaxResult getQualification() {
        return success(recruitEnterpriseBusinessInfoService.selectEnterpriseBusinessInfoByUserId(getUserId()));
    }

    @ApiOperation("上传企业环境")
    @PostMapping("/uploadEnvironment")
    public AjaxResult uploadEnvironment(@RequestBody @Validated UploadEnvironmentRequest request) {
        return toAjax(recruitEnterpriseInfoService.uploadEnvironment(request));
    }

    @ApiOperation("查看企业环境")
    @GetMapping("/getEnvironment")
    public AjaxResult getEnvironment() {
        Map<String, String> map = new HashMap<>();
        map.put("photoAlbum", recruitEnterpriseInfoService.getEnvironment());
        return success(map);
    }

    @ApiOperation("查看个人企业资质认证审核状态 绑定状态 0未绑定 1认证中 2绑定成功")
    @GetMapping(value = "/viewPersonalApprovalStatus", produces = {"application/json"})
    @ApiResponses({
            @ApiResponse(code = 200, message = "返回实体", response = RecruitEnterpriseUsersRel.class)
    })
    public AjaxResult viewPersonalApprovalStatus() {
        return success(recruitEnterpriseUsersRelService.viewEnterpriseAuditStatus(getUserId()));
    }

    @ApiOperation("根据企业名称，模糊查询企业列表")
    @GetMapping("/getEnterpriseListByName")
    public TableDataInfo getEnterpriseListByName(@NotBlank(message = "企业名称不能为空") String enterpriseName) {
        RecruitEnterpriseInfo enterpriseInfo = new RecruitEnterpriseInfo();
        enterpriseInfo.setEnterpriseName(enterpriseName);
        List<RecruitEnterpriseInfo> list = recruitEnterpriseInfoService.selectRecruitEnterpriseInfoList(enterpriseInfo);
        return getDataTable(list);
    }


    @ApiOperation("企业查看 企业详情")
    @GetMapping(value = "/getRecruiterEnterpriseDetails", produces = {"application/json"})
    @ApiResponses({
            @ApiResponse(code = 200, message = "返回实体", response = RecruitEnterpriseInfo.class)
    })
    public AjaxResult getRecruiterEnterpriseDetails() {
        RecruitEnterpriseUsersRel enterpriseUsersRel = recruitEnterpriseUsersRelService.selectEnterpriseUsersRelUserId(getUserId());
        if(StringUtils.isNotNull(enterpriseUsersRel)){
            RecruitEnterpriseInfo enterpriseInfo = recruitEnterpriseInfoService.selectRecruitEnterpriseInfoById(enterpriseUsersRel.getEnterpriseId());
            if(enterpriseInfo != null){
                if(enterpriseInfo.getScale() != null && !StringUtils.equals(enterpriseInfo.getScale(), "")) {
                    enterpriseInfo.setScaleName(DictUtils.getDictLabel("scale_enterprises", enterpriseInfo.getScale()));
                    if(enterpriseInfo.getScaleName().equals("不限")){
                        enterpriseInfo.setScaleName("规模不限");
                    }
                }else {
                    enterpriseInfo.setScaleName("规模不限");
                }
                if(enterpriseInfo.getEnterpriseNature() != null && !StringUtils.equals(enterpriseInfo.getEnterpriseNature(), "")) {
                    enterpriseInfo.setEnterpriseNatureName(DictUtils.getDictLabel("enterprise_nature", enterpriseInfo.getEnterpriseNature()));
                }
                if(enterpriseInfo.getFinancingStage() != null && !StringUtils.equals(enterpriseInfo.getFinancingStage(), "")) {
                    enterpriseInfo.setFinancingStageName(DictUtils.getDictLabel("financing_stage", enterpriseInfo.getFinancingStage()));
                    if (enterpriseInfo.getFinancingStageName().equals("不限")) {
                        enterpriseInfo.setFinancingStageName("融资不限");
                    }
                }else {
                    enterpriseInfo.setFinancingStageName("融资不限");
                }

                if(enterpriseInfo.getRegion() != null && !StringUtils.equals(enterpriseInfo.getRegion(), "")){
                    List<String> regions = new ArrayList<>(Arrays.asList(enterpriseInfo.getRegion().split(",")));
                    enterpriseInfo.setRegions(regions);
                }

            }
            return success(enterpriseInfo);
        }else {
            return error("该用户未绑定企业");
        }
    }


    @ApiOperation("(6-6)更改营业执照")
    @PostMapping("/changeOfBusinessLicense")
    public AjaxResult changeOfBusinessLicense(@RequestBody @Validated EnterpriseQualificationRequest request) {
        RecruitEnterpriseBusinessInfo enterpriseBusinessInfo = recruitEnterpriseBusinessInfoService.changeOfBusinessLicense(request);
        if(enterpriseBusinessInfo != null){
            return success(enterpriseBusinessInfo);
        }else {
            return error();
        }
    }


    @ApiOperation("(6-7)确定更改营业执照")
    @PostMapping("/confirmChanges")
    public AjaxResult confirmChanges(@RequestBody @Validated RecruitEnterpriseBusinessInfo request) {
        return toAjax(recruitEnterpriseBusinessInfoService.updateRecruitEnterpriseBusinessInfo(request));
    }

}
