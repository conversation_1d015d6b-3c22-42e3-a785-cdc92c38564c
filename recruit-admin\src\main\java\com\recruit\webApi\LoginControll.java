package com.recruit.webApi;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import cn.binarywang.wx.miniapp.bean.WxMaPhoneNumberInfo;
import com.recruit.common.config.WxMaProperties;
import com.recruit.common.constant.HttpStatus;
import com.recruit.common.core.controller.BaseController;
import com.recruit.common.core.domain.AjaxResult;
import com.recruit.common.core.domain.model.LoginUser;
import com.recruit.common.utils.PhoneUtil;
import com.recruit.common.utils.StringUtils;
import com.recruit.common.utils.WxUtil;
import com.recruit.core.domain.request.common.ForgotPasswordRequest;
import com.recruit.core.domain.request.common.ScanCodeLoginRequest;
import com.recruit.core.domain.request.common.UserLoginBodyRequest;
import com.recruit.core.domain.RecruitUserInfo;
import com.recruit.core.domain.request.common.UserPasswordRequest;
import com.recruit.core.domain.request.common.WeChatBindingRequest;
import com.recruit.core.service.IRecruitUserInfoService;
import com.recruit.core.service.LoginService;
import com.recruit.framework.web.service.TokenService;
import com.recruit.system.service.ISysConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.apache.tomcat.util.codec.binary.Base64;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotBlank;
import java.util.HashMap;
import java.util.Map;


/**
 * @Auther: Wu kong
 * @Date: 2023/3/19 8:59
 * @Description:
 */
@Slf4j
@Api(tags= "登录管理")
@RestController
@RequestMapping("/webApi")
@EnableConfigurationProperties(WxMaProperties.class)
public class LoginControll extends BaseController {

    @Autowired
    private WxMaService wxService;
    @Autowired
    private ISysConfigService configService;
    @Autowired
    private LoginService loginService;
    @Autowired
    private TokenService tokenService;
    @Autowired
    private IRecruitUserInfoService recruitUserInfoService;

    @ApiOperation(value = "查看用户是否注册,微信或手机号校验二选一，返回3表示未注册，0正常，1禁用，2注销", response = Boolean.class)
    @GetMapping("/isRegist")
    public AjaxResult isRegist(String openid, String phone) {
        Map<String, String> map = new HashMap<>();
        if(openid != null && !StringUtils.equals(openid, "")) {
            RecruitUserInfo user = recruitUserInfoService.getOpenidUserInfo(openid);
            if (ObjectUtils.isEmpty(user)) {
                map.put("accountStatus", "3");
                return AjaxResult.success(map);
            } else {
                map.put("accountStatus", String.valueOf(user.getStatus()));
            }
        }else if(phone != null && !StringUtils.equals(phone, "")){
            RecruitUserInfo user = recruitUserInfoService.getPhoneUserInfo(phone);
            if (ObjectUtils.isEmpty(user)) {
                map.put("accountStatus", "3");
                return AjaxResult.success(map);
            } else {
                map.put("accountStatus", String.valueOf(user.getStatus()));
            }
        }else {
            map.put("accountStatus", "3");
        }
        return AjaxResult.success(map);
    }


    @ApiOperation(value = "获取openId", response = WxMaJscode2SessionResult.class)
    @GetMapping("/getOpenId")
    public AjaxResult getOpenId(@NotBlank(message = "授权码不能为空") String code) {
        try {
            Map<String, Object> maps = new HashMap<>();
            WxMaJscode2SessionResult session = this.wxService.getUserService().getSessionInfo(code);
            maps.put("openid",session.getOpenid());
            maps.put("session", session);
            return AjaxResult.success(maps);
        } catch (WxErrorException e) {
            log.error(e.getMessage(), e);
            return AjaxResult.error(e.toString());
        }
    }


    @ApiOperation(value = "获取用户微信绑定手机号", response = WxMaPhoneNumberInfo.class)
    @GetMapping("/getWxPhone")
    public AjaxResult getWxPhone(@NotBlank(message = "授权码不能为空") String code) throws WxErrorException {
        WxMaPhoneNumberInfo phoneNoInfo = this.wxService.getUserService().getNewPhoneNoInfo(code);
        return AjaxResult.success(phoneNoInfo);
    }


    @ApiOperation("验证码及openid登录, 《根据手机号+验证码登陆》，《根据openid+昵称登陆+》, 《手机号+密码登陆》")
    @PostMapping("/login")
    public AjaxResult login(@RequestBody @Validated UserLoginBodyRequest loginBody) {
        return recruitUserInfoService.login(loginBody);
    }

    @ApiOperation("退出登陆")
    @PostMapping("/logOut")
    public AjaxResult logOut(HttpServletRequest request) {
        LoginUser loginUser = tokenService.getLoginUser(request);
        return success(recruitUserInfoService.logOut(loginUser.getToken()));
    }

    @ApiOperation(value = "忘记密码,确认")
    @PostMapping("/forgotPassword")
    public AjaxResult forgotPassword(@RequestBody @Validated ForgotPasswordRequest request) {
        return recruitUserInfoService.forgotPassword(request);
    }


    @ApiOperation("根据用户id重置密码")
    @PostMapping("/resettingPassword")
    public AjaxResult resettingPassword(@RequestBody @Validated UserPasswordRequest userPassword){
        return recruitUserInfoService.setPassword(userPassword);
    }


    @ApiOperation(value = "忘记密码,获取验证码，默认验证码《6666》")
    @GetMapping("/getForgotPasswordCode")
    public AjaxResult getForgotPasswordCode(@NotBlank(message = "手机不能为空") String phone){
        //校验手机号
        String verifyPhone = PhoneUtil.verifyPhone(phone);
        if(!StringUtils.equals(verifyPhone, "success")){
            return AjaxResult.error(HttpStatus.UNSUPPORTED_TYPE, verifyPhone);
        }
        RecruitUserInfo recruitUserInfo = recruitUserInfoService.getPhoneUserInfo(phone);
        if(recruitUserInfo == null){
            throw new RuntimeException("该账号不存在！");
        }
        boolean loginQuery = Boolean.parseBoolean(configService.selectConfigByKey("enabled_message"));
        loginService.getForgotPasswordCode(phone, loginQuery);
        return AjaxResult.success(phone);
    }

    @ApiOperation(value = "获取登陆验证码,默认验证码《6666》")
    @GetMapping("/getVerificationCode")
    public AjaxResult getVerificationCode(@NotBlank(message = "手机不能为空") String phone){
        //校验手机号
        String verifyPhone = PhoneUtil.verifyPhone(phone);
        if(!StringUtils.equals(verifyPhone, "success")){
            return AjaxResult.error(HttpStatus.UNSUPPORTED_TYPE, verifyPhone);
        }
        boolean loginQuery = Boolean.parseBoolean(configService.selectConfigByKey("enabled_message"));

        //校验手机号是否在免验证码手机号中
        if(verifyPhoneNumber(phone)){
            loginQuery = true;
        }
        loginService.getSmsCode(phone, loginQuery);
        return AjaxResult.success(phone);
    }

    /**
     * 校验手机号是否在免验证码手机号中
     * @param phone
     * @return
     */
    public boolean verifyPhoneNumber(String phone){
        String verificationCode = configService.selectConfigByKey("no_verification_code");
        String[] ips = verificationCode.split(",");
        Map<String, Boolean> map = new HashMap<>();
        for (String iStr : ips)
        {
            map.put(iStr, true);
        }
        if(map.get(phone) != null){
            return true;
        }else {
            return false;
        }
    }



    @ApiOperation(value = "更改手机号获取验证码,默认验证码《6666》")
    @GetMapping("/getUpdatePhoneCode")
    public AjaxResult getUpdatePhoneCode(@NotBlank(message = "手机不能为空") String phone){
        //校验手机号
        String verifyPhone = PhoneUtil.verifyPhone(phone);
        if(!StringUtils.equals(verifyPhone, "success")){
            return AjaxResult.error(HttpStatus.UNSUPPORTED_TYPE, verifyPhone);
        }
        boolean loginQuery = Boolean.parseBoolean(configService.selectConfigByKey("enabled_message"));
        loginService.getUpdatePhoneCode(phone, loginQuery);
        return AjaxResult.success(phone);
    }

    @ApiOperation("设置微信绑定登陆")
    @PostMapping("/setWeChatBinding")
    public AjaxResult setWeChatBinding(@RequestBody @Validated WeChatBindingRequest request){
        return recruitUserInfoService.setWeChatBinding(request);
    }



    /**
     * 获取二维码
     * @return 二维码字符串
     */
    @ApiOperation(value = "获取二维码")
    @GetMapping("/getQRCode")
    public Map getQRCode() throws Exception {
        Map<String, Object> map = new HashMap<>();
        String webSocketId = String.valueOf(System.currentTimeMillis());
        String accessToken = null;
        try {
            accessToken = this.wxService.getAccessToken(true);
        } catch (WxErrorException e) {
            throw new RuntimeException(e);
        }
        /********* 封装请求参数 **********/
        Map<String, Object> paraMap = new HashMap<>();
        //二维码携带参数 不超过32位 参数类型必须是字符串
        paraMap.put("scene", webSocketId);
        //二维码跳转页面
        paraMap.put("page", "pages/login/wxLogin/index");
        //二维码的宽度
        paraMap.put("width", 450);
        //自动配置线条颜色，如果颜色依然是黑色，则说明不建议配置主色调
        paraMap.put("auto_color", false);
        //是否需要透明底色， is_hyaline 为true时，生成透明底色的小程序码
        paraMap.put("is_hyaline", false);
        //要打开的小程序版本。正式版为 "release"，体验版为 "trial"，开发版为 "develop"
        paraMap.put("env_version", "release");
        //检查page 是否存在，为 true 时 page 必须是已经发布的小程序存在的页面（否则报错）；为 false 时允许小程序未发布或者 page 不存在， 但page 有数量上限（60000个）请勿滥用
        paraMap.put("check_path", false);
        /********* 执行post请求微信 获取二维码数据流 **********/
        byte[] result = WxUtil.getQrCodeByParam("https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token=" + accessToken, paraMap);
        // 将获取的二维码流，存入oss，这步看具体情况，我这里是将返回的流存入了oss，然后获取的是oss给的图片地址，也可以直接返回这个base64位的字节数组，img标签展示的时候，需要加上base64处理，一样可以显示处理二维码
        //log.info(Base64.encodeBase64String(result));
        String res=Base64.encodeBase64String(result);
        if(StringUtils.isNotBlank(res)) {
            String encoded = "data:image/jpg;base64," + res.replaceAll("[\\s*\t\n\r]", "");
            map.put("base64Str",encoded);
            map.put("webSocketId",webSocketId);
            return map;
        }
        return null;
    }

    @ApiOperation("PC端扫码登陆")
    @PostMapping("/pcScanCodeLogin")
    public AjaxResult pcScanCodeLogin(@RequestBody @Validated ScanCodeLoginRequest loginBody) {
        log.info("接收到消息，webSocketId:{}，手机号:{}",loginBody.getWebSocketId(),loginBody.getPhone());
        return recruitUserInfoService.pcScanCodeLogin(loginBody);
    }

}
