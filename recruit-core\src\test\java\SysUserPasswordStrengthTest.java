// 注意：由于这是独立的测试类，我们创建一个简化的SysUser类用于测试
// 在实际项目中，应该导入: import com.recruit.common.core.domain.entity.SysUser;

import java.util.*;
import java.util.regex.Pattern;

/**
 * 系统用户弱口令检测测试类
 *
 * <AUTHOR> Assistant
 * @since 2025-07-30
 */
public class SysUserPasswordStrengthTest {

    /**
     * 简化的SysUser类用于测试
     */
    static class SysUser {
        private String userName;
        private String password;

        public String getUserName() { return userName; }
        public void setUserName(String userName) { this.userName = userName; }
        public String getPassword() { return password; }
        public void setPassword(String password) { this.password = password; }
    }

    private PasswordStrengthValidator validator;

    /**
     * 主方法 - 运行所有测试
     */
    public static void main(String[] args) {
        SysUserPasswordStrengthTest test = new SysUserPasswordStrengthTest();
        test.setUp();

        System.out.println(repeatString("=", 80));
        System.out.println("                    系统用户弱口令检测测试");
        System.out.println(repeatString("=", 80));

        test.testStrongPassword();
        test.testShortPassword();
        test.testLowComplexityPassword();
        test.testCommonWeakPasswords();
        test.testPasswordSameAsUsername();
        test.testPasswordSimilarToUsername();
        test.testVariousPasswordScenarios();

        System.out.println("\n" + repeatString("=", 80));
        System.out.println("                           测试完成");
        System.out.println(repeatString("=", 80));
    }

    public void setUp() {
        validator = new PasswordStrengthValidator();
    }

    public void testStrongPassword() {
        SysUser user = createTestUser("testuser", "TestPass123!");
        PasswordStrengthResult result = validator.validatePassword(user);
        
        printDetailedReport("强密码测试", user, result);
        assert result.isPassed() : "强密码应该通过验证";
    }

    public void testShortPassword() {
        SysUser user = createTestUser("testuser", "Test1!");
        PasswordStrengthResult result = validator.validatePassword(user);
        
        printDetailedReport("短密码测试", user, result);
        assert !result.isPassed() : "短密码不应该通过验证";
    }

    public void testLowComplexityPassword() {
        SysUser user = createTestUser("testuser", "testpassword");
        PasswordStrengthResult result = validator.validatePassword(user);
        
        printDetailedReport("低复杂度密码测试", user, result);
        assert !result.isPassed() : "低复杂度密码不应该通过验证";
    }

    public void testCommonWeakPasswords() {
        String[] weakPasswords = {"123456", "password", "admin", "123456789", "qwerty"};
        
        for (String weakPassword : weakPasswords) {
            SysUser user = createTestUser("testuser", weakPassword);
            PasswordStrengthResult result = validator.validatePassword(user);
            
            printDetailedReport("常见弱口令测试 - " + weakPassword, user, result);
            assert !result.isPassed() : "常见弱口令 " + weakPassword + " 不应该通过验证";
        }
    }

    public void testPasswordSameAsUsername() {
        SysUser user = createTestUser("testuser", "testuser");
        PasswordStrengthResult result = validator.validatePassword(user);
        
        printDetailedReport("密码与用户名相同测试", user, result);
        assert !result.isPassed() : "密码与用户名相同不应该通过验证";
    }

    public void testPasswordSimilarToUsername() {
        SysUser user = createTestUser("testuser", "testuser123");
        PasswordStrengthResult result = validator.validatePassword(user);
        
        printDetailedReport("密码与用户名相似测试", user, result);
        assert !result.isPassed() : "密码与用户名相似不应该通过验证";
    }

    public void testVariousPasswordScenarios() {
        Map<String, String> testCases = new HashMap<>();
        testCases.put("admin", "Admin123!");
        testCases.put("user123", "User123@456");
        testCases.put("testuser", "ComplexPass2024#");
        testCases.put("manager", "12345678");
        testCases.put("developer", "password123");
        
        System.out.println("\n=== 综合密码场景测试 ===");
        for (Map.Entry<String, String> entry : testCases.entrySet()) {
            SysUser user = createTestUser(entry.getKey(), entry.getValue());
            PasswordStrengthResult result = validator.validatePassword(user);
            printDetailedReport("用户: " + entry.getKey(), user, result);
            System.out.println("----------------------------------------");
        }
    }

    /**
     * 创建测试用户
     */
    private SysUser createTestUser(String username, String password) {
        SysUser user = new SysUser();
        user.setUserName(username);
        user.setPassword(password);
        return user;
    }

    /**
     * 打印详细的检测报告
     */
    private void printDetailedReport(String testName, SysUser user, PasswordStrengthResult result) {
        System.out.println("\n=== " + testName + " ===");
        System.out.println("用户名: " + user.getUserName());
        System.out.println("密码: " + maskPassword(user.getPassword()));
        System.out.println("检测结果: " + (result.isPassed() ? "✓ 通过" : "✗ 失败"));
        System.out.println("密码强度评分: " + result.getScore() + "/100");
        
        if (!result.getFailureReasons().isEmpty()) {
            System.out.println("失败原因:");
            for (String reason : result.getFailureReasons()) {
                System.out.println("  • " + reason);
            }
        }
        
        if (!result.getSuggestions().isEmpty()) {
            System.out.println("改进建议:");
            for (String suggestion : result.getSuggestions()) {
                System.out.println("  • " + suggestion);
            }
        }
        
        System.out.println("详细分析:");
        System.out.println("  长度: " + user.getPassword().length() + " 字符");
        System.out.println("  包含大写字母: " + (containsUppercase(user.getPassword()) ? "是" : "否"));
        System.out.println("  包含小写字母: " + (containsLowercase(user.getPassword()) ? "是" : "否"));
        System.out.println("  包含数字: " + (containsDigit(user.getPassword()) ? "是" : "否"));
        System.out.println("  包含特殊字符: " + (containsSpecialChar(user.getPassword()) ? "是" : "否"));
    }

    /**
     * 掩码显示密码
     */
    private String maskPassword(String password) {
        if (password == null || password.length() <= 2) {
            return "***";
        }
        return password.charAt(0) + "***" + password.charAt(password.length() - 1);
    }

    // 辅助方法
    private boolean containsUppercase(String password) {
        return Pattern.compile("[A-Z]").matcher(password).find();
    }

    private boolean containsLowercase(String password) {
        return Pattern.compile("[a-z]").matcher(password).find();
    }

    private boolean containsDigit(String password) {
        return Pattern.compile("[0-9]").matcher(password).find();
    }

    private boolean containsSpecialChar(String password) {
        return Pattern.compile("[!@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>\\/?]").matcher(password).find();
    }

    /**
     * 密码强度验证器
     */
    static class PasswordStrengthValidator {
        
        private static final int MIN_LENGTH = 8;
        private static final Set<String> COMMON_WEAK_PASSWORDS = new HashSet<>(Arrays.asList(
            "123456", "password", "admin", "123456789", "qwerty", "abc123", 
            "password123", "admin123", "root", "user", "test", "guest",
            "welcome", "login", "passw0rd", "letmein", "monkey", "dragon"
        ));

        public PasswordStrengthResult validatePassword(SysUser user) {
            String password = user.getPassword();
            String username = user.getUserName();
            
            PasswordStrengthResult result = new PasswordStrengthResult();
            List<String> failures = new ArrayList<>();
            List<String> suggestions = new ArrayList<>();
            int score = 0;

            // 检查密码长度
            if (password.length() < MIN_LENGTH) {
                failures.add("密码长度不足8位");
                suggestions.add("密码长度至少需要8个字符");
            } else {
                score += 20;
                if (password.length() >= 12) score += 10;
            }

            // 检查字符类型复杂度
            int charTypeCount = 0;
            boolean hasUpper = containsUppercase(password);
            boolean hasLower = containsLowercase(password);
            boolean hasDigit = containsDigit(password);
            boolean hasSpecial = containsSpecialChar(password);

            if (hasUpper) charTypeCount++;
            if (hasLower) charTypeCount++;
            if (hasDigit) charTypeCount++;
            if (hasSpecial) charTypeCount++;

            if (charTypeCount < 3) {
                failures.add("密码复杂度不够，需要包含大写字母、小写字母、数字和特殊字符中的至少3种");
                suggestions.add("建议包含大写字母、小写字母、数字和特殊字符");
            } else {
                score += charTypeCount * 15;
            }

            // 检查是否为常见弱口令
            if (COMMON_WEAK_PASSWORDS.contains(password.toLowerCase())) {
                failures.add("密码是常见的弱口令");
                suggestions.add("避免使用常见的弱口令，如123456、password等");
            } else {
                score += 20;
            }

            // 检查密码与用户名的相似性
            if (password.equalsIgnoreCase(username)) {
                failures.add("密码不能与用户名相同");
                suggestions.add("密码应该与用户名完全不同");
            } else if (password.toLowerCase().contains(username.toLowerCase()) || 
                      username.toLowerCase().contains(password.toLowerCase())) {
                failures.add("密码与用户名过于相似");
                suggestions.add("密码不应包含用户名或与用户名相似");
            } else {
                score += 15;
            }

            // 额外的安全检查
            if (isSequentialChars(password)) {
                failures.add("密码包含连续字符序列");
                suggestions.add("避免使用连续的字符序列，如123、abc等");
            } else {
                score += 10;
            }

            result.setPassed(failures.isEmpty());
            result.setFailureReasons(failures);
            result.setSuggestions(suggestions);
            result.setScore(Math.min(100, score));

            return result;
        }

        private boolean containsUppercase(String password) {
            return Pattern.compile("[A-Z]").matcher(password).find();
        }

        private boolean containsLowercase(String password) {
            return Pattern.compile("[a-z]").matcher(password).find();
        }

        private boolean containsDigit(String password) {
            return Pattern.compile("[0-9]").matcher(password).find();
        }

        private boolean containsSpecialChar(String password) {
            return Pattern.compile("[!@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>\\/?]").matcher(password).find();
        }

        private boolean isSequentialChars(String password) {
            String lower = password.toLowerCase();
            return lower.contains("123") || lower.contains("abc") || 
                   lower.contains("qwe") || lower.contains("asd") ||
                   lower.contains("zxc") || lower.contains("321") ||
                   lower.contains("cba");
        }
    }

    /**
     * 密码强度检测结果
     */
    static class PasswordStrengthResult {
        private boolean passed;
        private List<String> failureReasons = new ArrayList<>();
        private List<String> suggestions = new ArrayList<>();
        private int score;

        // Getters and Setters
        public boolean isPassed() { return passed; }
        public void setPassed(boolean passed) { this.passed = passed; }
        
        public List<String> getFailureReasons() { return failureReasons; }
        public void setFailureReasons(List<String> failureReasons) { this.failureReasons = failureReasons; }
        
        public List<String> getSuggestions() { return suggestions; }
        public void setSuggestions(List<String> suggestions) { this.suggestions = suggestions; }
        
        public int getScore() { return score; }
        public void setScore(int score) { this.score = score; }
    }

    /**
     * Java 8兼容的字符串重复方法
     */
    private static String repeatString(String str, int count) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < count; i++) {
            sb.append(str);
        }
        return sb.toString();
    }
}
