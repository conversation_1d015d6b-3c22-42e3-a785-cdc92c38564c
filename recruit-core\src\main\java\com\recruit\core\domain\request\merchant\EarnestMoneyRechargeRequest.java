package com.recruit.core.domain.request.merchant;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel("保证金充值")
public class EarnestMoneyRechargeRequest {

    @ApiModelProperty("支付渠道 1微信，2支付宝, 3建行")
    private String payChannel;

    @ApiModelProperty("渠道 1小程序，2PC")
    private String channel;

    @ApiModelProperty("保证金")
    private BigDecimal earnestMoney;
}
