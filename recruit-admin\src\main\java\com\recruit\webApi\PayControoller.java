package com.recruit.webApi;

import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import com.recruit.common.core.controller.BaseController;
import com.recruit.common.core.domain.AjaxResult;
import com.recruit.common.core.page.TableDataInfo;
import com.recruit.common.exception.ServiceException;
import com.recruit.common.utils.DateUtils;
import com.recruit.common.utils.SnowFlakeUtil;
import com.recruit.common.utils.StringUtils;
import com.recruit.core.domain.RecruitUserConsumeRecord;
import com.recruit.core.domain.RecruitUserInfo;
import com.recruit.core.domain.RecruitUserOrder;
import com.recruit.core.domain.RecruitUserProp;
import com.recruit.core.domain.SysProp;
import com.recruit.core.domain.SysPropPrice;
import com.recruit.core.domain.request.pay.MemberFeeRequest;
import com.recruit.core.domain.request.pay.PurchasePropsRequest;
import com.recruit.core.service.IRecruitPositionService;
import com.recruit.core.service.IRecruitUserConsumeRecordService;
import com.recruit.core.service.IRecruitUserInfoService;
import com.recruit.core.service.IRecruitUserOrderService;
import com.recruit.core.service.IRecruitUserPropService;
import com.recruit.core.service.ISysPayChannelService;
import com.recruit.core.service.ISysPropService;
import com.recruit.system.service.ISysConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.time.temporal.ChronoUnit;
import java.util.*;

/**
 * @Auther: Wu kong
 * @Date: 2023/5/21 16:45
 * @Description:
 */
@Slf4j
@Api(tags= "(5-21)支付配置接口")
@RestController
@RequestMapping("/web/api/payConfig")
public class PayControoller extends BaseController {

    @Autowired
    private ISysConfigService configService;

    @Autowired
    private ISysPropService sysPropService;

    @Autowired
    private ISysPayChannelService sysPayChannelService;

    @Autowired
    private IRecruitUserPropService recruitUserPropService;


    @Autowired
    private IRecruitPositionService recruitPositionService;

    @Autowired
    private IRecruitUserInfoService recruitUserInfoService;

    @Autowired
    private IRecruitUserOrderService recruitUserOrderService;

    @Autowired
    private IRecruitUserConsumeRecordService recruitUserConsumeRecordService;

    @ApiOperation("获取支付开关, true开启充值，false 关闭充值")
    @PostMapping("/getObtainPaySwitch")
    public AjaxResult getObtainPaySwitch() {
        Map<String, Boolean> map = new HashMap<>();
        map.put("obtainPaySwitch", Boolean.valueOf(configService.selectConfigByKey("enable_payment")));
        return success(map);
    }

    @ApiOperation(value = "充值, payChannel支付渠道 1微信，2支付宝,3建行, 平台渠道channel 1小程序，2PC", response = MemberFeeRequest.class)
    @PostMapping("/rechangeMember")
    public AjaxResult rechargeMember(@RequestBody MemberFeeRequest request) {
        request.setOrderType("1");
        request.setDescription("博汇充值");
        // 生成支付订单
        String flowNo = String.valueOf(SnowFlakeUtil.nextId());
        request.setFlowNo(flowNo);
        Map<String, Object> result = sysPayChannelService.rechargeMember(request);
        return AjaxResult.success(result);
    }

    @ApiOperation("查询充值记录 订单状态 0进行中，1已完成，2订单失败")
    @GetMapping(value = "/getRechargeRecord", produces = {"application/json"})
    @ApiResponses({
            @ApiResponse(code = 200, message = "返回实体", response = SysProp.class)
    })
    public TableDataInfo getRechargeRecord(String orderStatus) {
        if(orderStatus != null && !StringUtils.equals(orderStatus, "")) {
            RecruitUserInfo userInfo = recruitUserInfoService.selectRecruitUserInfoById(getUserId());
            RecruitUserOrder userOrder = new RecruitUserOrder();
            userOrder.setUserId(userInfo.getId());
            String orderStatuss = "";
            switch (orderStatus) {
                case "0": //进行中
                    orderStatuss = "WAIT_BUYER_PAY";
                    break;
                case "1": //已完成
                    orderStatuss = "SUCCESS";
                    break;
                case "2": //订单失败
                    orderStatuss = "FAIL";
                    break;
                case "3": //支付中
                    orderStatuss = "PROGRESS";
                    break;
            }
            userOrder.setOrderStatus(orderStatuss);
            userOrder.setOrderType("1");
            startPage();
            List<RecruitUserOrder> lists = recruitUserOrderService.selectRecruitUserOrderListTwo(userOrder);
            return getDataTable(lists);
        }else {
            return getDataTable(new ArrayList<>());
        }
    }


    @ApiOperation("查询用户消费记录 进出，1进账，2消费")
    @GetMapping(value = "/getPurchaseHistory", produces = {"application/json"})
    @ApiResponses({
            @ApiResponse(code = 200, message = "返回实体", response = RecruitUserConsumeRecord.class)
    })
    public TableDataInfo getPurchaseHistory(String access) {

        RecruitUserInfo userInfo = recruitUserInfoService.selectRecruitUserInfoById(getUserId());
        RecruitUserConsumeRecord consumeRecord = new RecruitUserConsumeRecord();
        consumeRecord.setUserId(userInfo.getId());
        consumeRecord.setAccess(access);
        List<RecruitUserConsumeRecord> lists = recruitUserConsumeRecordService.selectRecruitUserConsumeRecordList(consumeRecord);
        return getDataTable(lists);
    }


    @ApiOperation("购买道具 道具ID 购买类型 1博汇币，2积分")
    @Transactional
    @PostMapping("/purchaseProps")
    public AjaxResult purchaseProps(@RequestBody PurchasePropsRequest request) {
        //根据道具表id查询道具信息
        SysPropPrice prop = sysPropService.selectSysPropByPropPriceId(request.getSysPropPriceId());
        if(prop == null){
            throw new ServiceException("该道具信息不存在！");
        }
        //查询用户信息
        RecruitUserInfo userInfo = recruitUserInfoService.selectRecruitUserInfoById(getUserId());
        if(userInfo == null){
            throw new ServiceException("该用户信息不存在！");
        }
        //博汇币
        if(StringUtils.equals(request.getPurchaseType(), "1")){
            //金额, 账户金额大于或等于使用金额执行下列方法
            if(userInfo.getAccountAmount().compareTo(prop.getPropAmount()) >= 0){
                //账户余额进去使用金额
                userInfo.setAccountAmount(userInfo.getAccountAmount().subtract(prop.getPropAmount()));
            }else {
                throw new ServiceException("账户余额不足，请充值！", 9999);
            }
        }else //积分
            if(StringUtils.equals(request.getPurchaseType(), "2")){
                //积分, 账户积分大于或等于使用积分执行下列方法
                BigDecimal accountIntegral = new BigDecimal(userInfo.getAccountIntegral());
                if(accountIntegral.compareTo(new BigDecimal(prop.getPropIntegral())) >= 0){
                    userInfo.setAccountIntegral(String.valueOf(accountIntegral.subtract(new BigDecimal(prop.getPropIntegral()))));
                }else {
                    throw new ServiceException("账户积分不足，请充值！", 9999);
                }
        }
        //更新账户
        recruitUserInfoService.updateRecruitUserInfo(userInfo);
        //用户道具表
        RecruitUserProp userProp = new RecruitUserProp();
        //道具表id
        userProp.setSysPropId(prop.getId());
        //用户id
        userProp.setUserId(userInfo.getId());
        //道具类型
        userProp.setPropType(prop.getPropType());
        //类型 1：求职者 2：招聘者
        userProp.setChatType(prop.getChatType());
        //道具描述
        userProp.setPropDescribe(prop.getPropDescribe());
        //使用状态 0未使用，1使用中，2已使用，3过期
        userProp.setOnState("0");
        //时间卡，时间数
        userProp.setSubExpirationNum(prop.getSubExpirationNum());
        Date expirationDate = DateUtils.getObtainPostTime(Math.toIntExact(prop.getExpirationNum())+1, ChronoUnit.DAYS);
        userProp.setExpirationDate(expirationDate);
        //用户道具表
        recruitUserPropService.insertRecruitUserProp(userProp);

        RecruitUserConsumeRecord recruitUserConsumeRecord = new RecruitUserConsumeRecord();
        recruitUserConsumeRecord.setUserId(userInfo.getId());
        ////博汇币
        if(StringUtils.equals(request.getPurchaseType(), "1")){
            //判断是否折扣，如果有折扣按照折扣价格算
            if (prop.getDiscount() != null && !StringUtils.equals(prop.getDiscount(), "0")) {
                BigDecimal ss = new BigDecimal(prop.getDiscount()).divide(new BigDecimal(10));
                recruitUserConsumeRecord.setAmount(prop.getPropAmount().multiply(ss));
            }else {
                recruitUserConsumeRecord.setAmount(prop.getPropAmount());
            }
        }else if(StringUtils.equals(request.getPurchaseType(), "2")){
            if (prop.getDiscount() != null && !StringUtils.equals(prop.getDiscount(), "0")) {
                BigDecimal ss = new BigDecimal(prop.getDiscount()).divide(new BigDecimal(10));
                BigDecimal aa = new BigDecimal(prop.getPropIntegral());
                recruitUserConsumeRecord.setIntegral(String.valueOf(aa.multiply(ss)));
            }else {
                recruitUserConsumeRecord.setIntegral(prop.getPropIntegral());
            }
        }

        recruitUserConsumeRecord.setPropType(prop.getPropType());
        recruitUserConsumeRecord.setAccess("2");
        recruitUserConsumeRecord.setDescribe("购买道具");
        //用户消费记录表
        recruitUserConsumeRecordService.insertRecruitUserConsumeRecord(recruitUserConsumeRecord);
        return AjaxResult.success();
    }

    @ApiOperation("查询用户道具, onState 0未使用，1使用中，2已使用，3过期")
    @GetMapping(value = "/getUserProp", produces = {"application/json"})
    @ApiResponses({
            @ApiResponse(code = 200, message = "返回实体", response = RecruitUserProp.class)
    })
    public TableDataInfo getUserProp(String onState) {
        RecruitUserInfo userInfo = recruitUserInfoService.selectRecruitUserInfoById(getUserId());
        RecruitUserProp userProp = new RecruitUserProp();
        userProp.setUserId(userInfo.getId());
        userProp.setChatType(userInfo.getType());
        userProp.setOnState(onState);
        List<RecruitUserProp> lists = recruitUserPropService.selectRecruitUserPropList(userProp);
        lists.forEach(e->{
            if(e.getPositionCode() != null && !StringUtils.equals(e.getPositionCode(), "")) {
                e.setPositionInfoName(recruitPositionService.getMap(e.getPositionCode()));
            }
        });
        return getDataTable(lists);
    }


    @ApiOperation("查询支付状态")
    @PostMapping(value = "/getPayStatus", produces = {"application/json"})
    public AjaxResult getPayStatus(@RequestBody MemberFeeRequest request) {

        RecruitUserOrder userOrder = recruitUserOrderService.selectRecruitUserOrderTwo(new RecruitUserOrder(){{
            setPayChannel(request.getPayChannel());
            setOrderAmount(request.getRechargeAmount());
            setUserId(getUserId());
        }});
        if(userOrder != null){
            if(userOrder.getOrderStatus().equals("SUCCESS")) {
                return success(true);
            }
        }
        return success(false);
    }

}
