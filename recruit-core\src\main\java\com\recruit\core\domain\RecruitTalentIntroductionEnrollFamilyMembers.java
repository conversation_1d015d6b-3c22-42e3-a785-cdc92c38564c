package com.recruit.core.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.recruit.common.annotation.Excel;
import com.recruit.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 人才引进报名家庭成员对象 recruit_talent_introduction_enroll_family_members
 *
 * <AUTHOR>
 * @date 2024-03-15
 */
@Data
@ApiModel("人才引进报名家庭成员")
public class RecruitTalentIntroductionEnrollFamilyMembers extends BaseEntity
{
    private static final long serialVersionUID = 1L;


    private Long id;


    @ApiModelProperty("人才引进报名id")
    private Long talentIntroductionEnrollId;


    @ApiModelProperty("序号")
    private Integer index;


    @Excel(name = "序号", cellType = Excel.ColumnType.NUMERIC)
    @ApiModelProperty("序号")
    private String sequence;


    @Excel(name = "称谓")
    @ApiModelProperty("称谓")
    private String appellation;


    @Excel(name = "姓名")
    @ApiModelProperty("姓名")
    private String moniker;


    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "出生年月", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("出生年月")
    private Date dateOfBirth;


    @Excel(name = "工作单位及学校")
    @ApiModelProperty("工作单位及学校")
    private String occupation;


    @Excel(name = "职务")
    @ApiModelProperty("职务")
    private String duties;


}
