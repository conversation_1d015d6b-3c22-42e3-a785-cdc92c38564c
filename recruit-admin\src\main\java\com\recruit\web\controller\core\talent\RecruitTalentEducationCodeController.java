package com.recruit.web.controller.core.talent;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.recruit.common.annotation.Log;
import com.recruit.common.core.controller.BaseController;
import com.recruit.common.core.domain.AjaxResult;
import com.recruit.common.enums.BusinessType;
import org.springframework.web.multipart.MultipartFile;
import com.recruit.core.domain.RecruitTalentEducationCode;
import com.recruit.core.service.IRecruitTalentEducationCodeService;
import com.recruit.common.utils.poi.ExcelUtil;
import com.recruit.common.core.page.TableDataInfo;

/**
 * 专业代码Controller
 *
 * <AUTHOR>
 * @date 2024-07-26
 */
@RestController
@RequestMapping("/core/recruitTalentEducationCode")
public class RecruitTalentEducationCodeController extends BaseController
{
    @Autowired
    private IRecruitTalentEducationCodeService recruitTalentEducationCodeService;

    /**
     * 查询专业代码列表
     */
    @PreAuthorize("@ss.hasPermi('core:recruitTalentEducationCode:list')")
    @GetMapping("/list")
    public TableDataInfo list(RecruitTalentEducationCode recruitTalentEducationCode)
    {
        startPage();
        List<RecruitTalentEducationCode> list = recruitTalentEducationCodeService.selectRecruitTalentEducationCodeList(recruitTalentEducationCode);
        return getDataTable(list);
    }

    @ApiOperation("查询专业代码")
    @GetMapping("/educationCodelistAll")
    public TableDataInfo educationCodelistAll(RecruitTalentEducationCode recruitTalentEducationCode)
    {
        List<RecruitTalentEducationCode> list = recruitTalentEducationCodeService.selectRecruitTalentEducationCodeList(recruitTalentEducationCode);
        return getDataTable(list);
    }



    /**
     * 导出专业代码列表
     */
    @PreAuthorize("@ss.hasPermi('core:recruitTalentEducationCode:export')")
    @Log(title = "专业代码", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RecruitTalentEducationCode recruitTalentEducationCode)
    {
        List<RecruitTalentEducationCode> list = recruitTalentEducationCodeService.selectRecruitTalentEducationCodeList(recruitTalentEducationCode);
        ExcelUtil<RecruitTalentEducationCode> util = new ExcelUtil<RecruitTalentEducationCode>(RecruitTalentEducationCode.class);
        util.exportExcel(response, list, "专业代码数据");
    }

    /**
     * 获取专业代码详细信息
     */
    @PreAuthorize("@ss.hasPermi('core:recruitTalentEducationCode:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(recruitTalentEducationCodeService.selectRecruitTalentEducationCodeById(id));
    }

    /**
     * 新增专业代码
     */
    @PreAuthorize("@ss.hasPermi('core:recruitTalentEducationCode:add')")
    @Log(title = "专业代码", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RecruitTalentEducationCode recruitTalentEducationCode)
    {
        return toAjax(recruitTalentEducationCodeService.insertRecruitTalentEducationCode(recruitTalentEducationCode));
    }

    /**
     * 修改专业代码
     */
    @PreAuthorize("@ss.hasPermi('core:recruitTalentEducationCode:edit')")
    @Log(title = "专业代码", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RecruitTalentEducationCode recruitTalentEducationCode)
    {
        return toAjax(recruitTalentEducationCodeService.updateRecruitTalentEducationCode(recruitTalentEducationCode));
    }

    /**
     * 删除专业代码
     */
    @PreAuthorize("@ss.hasPermi('core:recruitTalentEducationCode:remove')")
    @Log(title = "专业代码", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(recruitTalentEducationCodeService.deleteRecruitTalentEducationCodeByIds(ids));
    }


    /**
     * 导入专业代码
     * @param file
     * @param updateSupport
     * @return
     * @throws Exception
     */
    @PreAuthorize("@ss.hasPermi('core:recruitTalentEducationCode:import')")
    @Log(title = "专业代码", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<RecruitTalentEducationCode> util = new ExcelUtil<>(RecruitTalentEducationCode.class);
        List<RecruitTalentEducationCode> lists = util.importExcel(file.getInputStream());
        String message = recruitTalentEducationCodeService.importRecruitTalentEducationCode(lists, updateSupport);
        return AjaxResult.success(message);
    }


}
