package com.recruit.webApi;

import com.recruit.common.core.controller.BaseController;
import com.recruit.common.core.domain.AjaxResult;
import com.recruit.core.domain.Friend;
import com.recruit.core.domain.RecruitCollect;
import com.recruit.core.domain.RecruitEnterpriseReceivesResume;
import com.recruit.core.domain.RecruitEnterpriseUsersRel;
import com.recruit.core.domain.RecruitExploreFootsteps;
import com.recruit.core.domain.RecruitInterviewInfo;
import com.recruit.core.domain.RecruitPositionInfo;
import com.recruit.core.domain.RecruitUserInfo;
import com.recruit.core.domain.response.EnterprisePersonalCenterResponse;
import com.recruit.core.domain.response.PersonalCenterResponse;
import com.recruit.core.service.IRecruitCollectService;
import com.recruit.core.service.IRecruitEnterpriseReceivesResumeService;
import com.recruit.core.service.IRecruitEnterpriseUsersRelService;
import com.recruit.core.service.IRecruitExploreFootstepsService;
import com.recruit.core.service.IRecruitInterviewInfoService;
import com.recruit.core.service.IRecruitPositionInfoService;
import com.recruit.core.service.IRecruitUserInfoService;
import com.recruit.core.service.IFriendService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import java.util.List;

/**
 * @Auther: Wu kong
 * @Date: 2023/4/18 11:49
 * @Description:
 */
@Api(tags= "(04-18)个人中心接口")
@RestController
@RequestMapping("/web/api/personalCenter")
public class PersonalCenterController extends BaseController {

    @Autowired
    private IFriendService friendService;

    @Autowired
    private IRecruitCollectService recruitCollectService;

    @Autowired
    private IRecruitUserInfoService recruitUserInfoService;

    @Autowired
    private IRecruitPositionInfoService recruitPositionInfoService;


    @Autowired
    private IRecruitInterviewInfoService recruitInterviewInfoService;

    @Autowired
    private IRecruitEnterpriseUsersRelService recruitEnterpriseUsersRelService;

    @Autowired
    private IRecruitExploreFootstepsService recruitExploreFootstepsService;

    @Autowired
    private IRecruitEnterpriseReceivesResumeService recruitEnterpriseReceivesResumeService;



    @ApiOperation("查看个人个人中心")
    @GetMapping(value = "/getUserPersonalCenter", produces = {"application/json"})
    @ApiResponses({
            @ApiResponse(code = 200, message = "返回实体", response = PersonalCenterResponse.class)
    })
    public AjaxResult getUserPersonalCenter()
    {
        PersonalCenterResponse response = new PersonalCenterResponse();
        //查看屏蔽公司数量
        /*RecruitBlockingEnterprises blockingEnterprises = new RecruitBlockingEnterprises();
        blockingEnterprises.setUserId(getUserId());
        response.setEnterprisesNum(recruitBlockingEnterprisesService.getEnterprisesNum(blockingEnterprises));*/

        //查看个人投递数
        RecruitPositionInfo positionInfo = new RecruitPositionInfo();
        positionInfo.setUserId(getUserId());
        response.setPersonalDeliveryNum(recruitPositionInfoService.getPersonalDeliveryNum(positionInfo));

        //查看求职者被查看数
        RecruitExploreFootsteps footsteps = new RecruitExploreFootsteps();
        footsteps.setUserId(getUserId());
        footsteps.setType("2");
        response.setPositionViewedNum(recruitExploreFootstepsService.getPositionBrowsingInfoNum(footsteps));

        //查询收藏公司数
        /*RecruitCollect collect = new RecruitCollect();
        collect.setUserId(getUserId());
        collect.setCollectType("3");
        response.setCollectionCompanyNum(recruitCollectService.getCollectionCompanyNum(collect));*/

        //查询收藏职位数
        RecruitCollect collects = new RecruitCollect();
        collects.setUserId(getUserId());
        collects.setCollectType("1");
        response.setCollectionPositionNum(recruitCollectService.getCollectionPositionNum(collects));

        //查看沟通数量
        RecruitUserInfo userInfo = recruitUserInfoService.selectRecruitUserInfoById(getUserId());
        List<Friend> friends = friendService.findFriendByUserId(getUserId(), userInfo.getType());
        response.setCommunicateNum(friends.size());


        RecruitInterviewInfo recruitInterviewInfo = new RecruitInterviewInfo();
        recruitInterviewInfo.setInviteeId(getUserId());
        List<RecruitInterviewInfo> list = recruitInterviewInfoService.selectRecruitInterviewInfoList(recruitInterviewInfo);
        //查看面试数量
        response.setInterviewNum(list.size());
        return success(response);
    }



    @ApiOperation("查看企业个人中心")
    @GetMapping(value = "/getEnterprisePersonalCenterResponse", produces = {"application/json"})
    @ApiResponses({
            @ApiResponse(code = 200, message = "返回实体", response = EnterprisePersonalCenterResponse.class)
    })
    public AjaxResult getEnterprisePersonalCenterResponse()
    {
        EnterprisePersonalCenterResponse response = new EnterprisePersonalCenterResponse();
        //查看企业收到简历数
        RecruitEnterpriseReceivesResume receivesResume = new RecruitEnterpriseReceivesResume();
        receivesResume.setEnterpriseId(getUserId());
        response.setEnterpriseReceivingNum(recruitEnterpriseReceivesResumeService.getEnterpriseReceivingNum(receivesResume));
        //企业收藏用户数量
        RecruitCollect collect = new RecruitCollect();
        collect.setPublisherId(getUserId());
        collect.setCollectType("2");
        response.setFavoriteUsersNum(recruitCollectService.getFavoriteUsersNum(collect));
        //企业看过的个人信息记录数
        RecruitExploreFootsteps footsteps = new RecruitExploreFootsteps();
        footsteps.setPublisherId(getUserId());
        footsteps.setType("1");
        response.setUserBrowsingInfoNum(recruitExploreFootstepsService.getUserBrowsingInfoNum(footsteps));

        //查看沟通数量
        RecruitUserInfo userInfo = recruitUserInfoService.selectRecruitUserInfoById(getUserId());
        List<Friend> friends = friendService.findFriendByUserId(getUserId(), userInfo.getType());
        response.setCommunicateNum(friends.size());

        RecruitEnterpriseUsersRel enterpriseUsersRel = recruitEnterpriseUsersRelService.selectEnterpriseUsersRelUserId(getUserId());
        RecruitInterviewInfo recruitInterviewInfo = new RecruitInterviewInfo();
        recruitInterviewInfo.setInviterId(getUserId());
        if(enterpriseUsersRel != null) {
            recruitInterviewInfo.setEnterpriseId(enterpriseUsersRel.getEnterpriseId());
        }
        List<RecruitInterviewInfo> list = recruitInterviewInfoService.selectRecruitInterviewInfoList(recruitInterviewInfo);
        //查看面试数量
        response.setInterviewNum(list.size());
        return success(response);
    }



}
