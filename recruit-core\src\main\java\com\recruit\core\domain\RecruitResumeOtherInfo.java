package com.recruit.core.domain;

import com.recruit.common.annotation.Excel;
import com.recruit.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 简历其它信息对象 recruit_resume_other_info
 *
 * <AUTHOR>
 * @date 2023-03-21
 */
@Data
@ApiModel("简历其它信息")
public class RecruitResumeOtherInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 用户id */
    @Excel(name = "用户id")
    @ApiModelProperty("用户id")
    private Long userId;

    /** 自我评价 */
    @Excel(name = "自我评价")
    @ApiModelProperty("自我评价")
    private String selfEvaluation;


}
