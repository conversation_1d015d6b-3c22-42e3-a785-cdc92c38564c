package com.recruit.webApi;

import com.recruit.common.core.controller.BaseController;
import com.recruit.common.core.domain.AjaxResult;
import com.recruit.common.core.domain.entity.SysDictType;
import com.recruit.common.core.page.TableDataInfo;
import com.recruit.core.domain.CompanyDynamics;
import com.recruit.core.domain.CompanyInfo;
import com.recruit.core.domain.ExaminationService;
import com.recruit.core.service.ICompanyDynamicsService;
import com.recruit.core.service.ICompanyInfoService;
import com.recruit.core.service.IExaminationServiceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @Auther: Wu kong
 * @Date: 2023/4/11 18:24
 * @Description:
 */
@Api(tags= "(04-11)公司信息公共接口")
@RestController
@RequestMapping("/webApi/company")
public class CompanyController extends BaseController {

    @Autowired
    private ICompanyInfoService companyInfoService;
    @Autowired
    private ICompanyDynamicsService companyDynamicsService;

    @Autowired
    private IExaminationServiceService examinationServiceService;


    @ApiOperation("查询公司动态，传分页字段 pageNum pageSize")
    @GetMapping("/getCompanyDynamicsList")
    public TableDataInfo getCompanyDynamicsList(@NotNull(message = "页码不能为空") @RequestParam Integer page,
                                                @NotNull(message = "size不能为空") @RequestParam Integer size)
    {
        startPageTwo(page, size);
        List<CompanyDynamics> list = companyDynamicsService.selectCompanyDynamicsList(new CompanyDynamics());
        list.forEach(e->{
            e.setBriefIntroduction(getText(e.getContent()));
        });
        return getDataTable(list);
    }

    /**
     * 提取富文本中纯文本
     */
    public static String getText(String richText) {
        String regx = "(<.+?>)|(</.+?>)";
        Matcher matcher = Pattern.compile(regx).matcher(richText);
        while (matcher.find()) {
            // 替换图片
            richText = matcher.replaceAll("").replace(" ", "");
        }
        return richText;
    }



    @ApiOperation("查询公司动态详情")
    @GetMapping(value = "/getCompanyDynamics")
    public AjaxResult getCompanyDynamics(@NotNull(message = "公司动态id不能为空") @RequestParam Long id)
    {
        return success(companyDynamicsService.selectCompanyDynamicsById(id));
    }

    @ApiOperation("查询公司信息")
    @GetMapping("/getCompanyInfo")
    public AjaxResult getCompanyInfo()
    {
        List<CompanyInfo> list = companyInfoService.selectCompanyInfoList(new CompanyInfo());
        return success(list.get(0));
    }

    @ApiOperation("查询服务之窗四个选项，传分页字段 pageNum pageSize， type 1法律法规")
    @GetMapping("/getExaminationList")
    public TableDataInfo getExaminationList(
                                            @NotNull(message = "类型不能为空") @RequestParam String type,
                                            @NotNull(message = "页码不能为空") @RequestParam Integer page,
                                            @NotNull(message = "size不能为空") @RequestParam Integer size)
    {
        startPageTwo(page, size);
        ExaminationService examinationService = new ExaminationService();
        examinationService.setType(type);
        List<ExaminationService> list = examinationServiceService.selectExaminationServiceList(examinationService);
        return getDataTable(list);
    }

    @ApiOperation("查询服务之窗四个选项，传分页字段 pageNum pageSize， type 2招聘须知")
    @GetMapping("/getExaminationListTwo")
    public TableDataInfo getExaminationListTwo(
                                            @NotNull(message = "页码不能为空") @RequestParam Integer page,
                                            @NotNull(message = "size不能为空") @RequestParam Integer size)
    {
        startPageTwo(page, size);
        ExaminationService examinationService = new ExaminationService();
        examinationService.setType("2");
        List<ExaminationService> list = examinationServiceService.selectExaminationServiceList(examinationService);
        return getDataTable(list);
    }

    @ApiOperation("查询服务之窗四个选项，传分页字段 pageNum pageSize， type 3求职技巧，4劳务纠纷案例")
    @GetMapping("/getExaminationListThree")
    public TableDataInfo getExaminationListThree(
                                            @NotNull(message = "页码不能为空") @RequestParam Integer page,
                                            @NotNull(message = "size不能为空") @RequestParam Integer size)
    {
        startPageTwo(page, size);
        ExaminationService examinationService = new ExaminationService();
        examinationService.setType("3");
        List<ExaminationService> list = examinationServiceService.selectExaminationServiceList(examinationService);
        return getDataTable(list);
    }

    @ApiOperation("查询服务之窗四个选项，传分页字段 pageNum pageSize， type 4劳务纠纷案例")
    @GetMapping("/getExaminationListFour")
    public TableDataInfo getExaminationListFour(
                                            @NotNull(message = "页码不能为空") @RequestParam Integer page,
                                            @NotNull(message = "size不能为空") @RequestParam Integer size)
    {
        startPageTwo(page, size);
        ExaminationService examinationService = new ExaminationService();
        examinationService.setType("4");
        List<ExaminationService> list = examinationServiceService.selectExaminationServiceList(examinationService);
        return getDataTable(list);
    }

    @ApiOperation("查询服务之窗四个选项详情")
    @GetMapping(value = "/getExaminationInfo")
    public AjaxResult getInfo(@NotNull(message = "id不能为空") @RequestParam Long id)
    {
        return success(examinationServiceService.selectExaminationServiceById(id));
    }

}
