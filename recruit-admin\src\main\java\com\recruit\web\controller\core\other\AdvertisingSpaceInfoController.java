package com.recruit.web.controller.core.other;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.recruit.common.annotation.Log;
import com.recruit.common.core.controller.BaseController;
import com.recruit.common.core.domain.AjaxResult;
import com.recruit.common.enums.BusinessType;
import org.springframework.web.multipart.MultipartFile;
import com.recruit.core.domain.AdvertisingSpaceInfo;
import com.recruit.core.service.IAdvertisingSpaceInfoService;
import com.recruit.common.utils.poi.ExcelUtil;
import com.recruit.common.core.page.TableDataInfo;

/**
 * 广告位信息Controller
 *
 * <AUTHOR>
 * @date 2023-06-18
 */
@RestController
@RequestMapping("/core/advertisingSpaceInfo")
public class AdvertisingSpaceInfoController extends BaseController
{
    @Autowired
    private IAdvertisingSpaceInfoService advertisingSpaceInfoService;

    /**
     * 查询广告位信息列表
     */
    @PreAuthorize("@ss.hasPermi('core:advertisingSpaceInfo:list')")
    @GetMapping("/list")
    public TableDataInfo list(AdvertisingSpaceInfo advertisingSpaceInfo)
    {
        startPage();
        List<AdvertisingSpaceInfo> list = advertisingSpaceInfoService.selectAdvertisingSpaceInfoList(advertisingSpaceInfo);
        return getDataTable(list);
    }

    /**
     * 导出广告位信息列表
     */
    @PreAuthorize("@ss.hasPermi('core:advertisingSpaceInfo:export')")
    @Log(title = "广告位信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AdvertisingSpaceInfo advertisingSpaceInfo)
    {
        List<AdvertisingSpaceInfo> list = advertisingSpaceInfoService.selectAdvertisingSpaceInfoList(advertisingSpaceInfo);
        ExcelUtil<AdvertisingSpaceInfo> util = new ExcelUtil<AdvertisingSpaceInfo>(AdvertisingSpaceInfo.class);
        util.exportExcel(response, list, "广告位信息数据");
    }

    /**
     * 获取广告位信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('core:advertisingSpaceInfo:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(advertisingSpaceInfoService.selectAdvertisingSpaceInfoById(id));
    }

    /**
     * 新增广告位信息
     */
    @PreAuthorize("@ss.hasPermi('core:advertisingSpaceInfo:add')")
    @Log(title = "广告位信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AdvertisingSpaceInfo advertisingSpaceInfo)
    {
        AdvertisingSpaceInfo advertisingSpace =  advertisingSpaceInfoService.selectAdvertisingSpaceInfoByEnterpriseId(advertisingSpaceInfo.getEnterpriseId());
        if(advertisingSpace != null){
            throw new RuntimeException("请勿重复添加企业到广告位！");
        }
        return toAjax(advertisingSpaceInfoService.insertAdvertisingSpaceInfo(advertisingSpaceInfo));
    }

    /**
     * 修改广告位信息
     */
    @PreAuthorize("@ss.hasPermi('core:advertisingSpaceInfo:edit')")
    @Log(title = "广告位信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AdvertisingSpaceInfo advertisingSpaceInfo)
    {

        AdvertisingSpaceInfo advertisingSpace =  advertisingSpaceInfoService.selectAdvertisingSpaceInfoByEnterpriseId(advertisingSpaceInfo.getEnterpriseId());
        if(!advertisingSpaceInfo.getId().equals(advertisingSpace.getId())){
            throw new RuntimeException("请勿重复添加企业到广告位！");
        }
        return toAjax(advertisingSpaceInfoService.updateAdvertisingSpaceInfo(advertisingSpaceInfo));
    }

    /**
     * 删除广告位信息
     */
    @PreAuthorize("@ss.hasPermi('core:advertisingSpaceInfo:remove')")
    @Log(title = "广告位信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(advertisingSpaceInfoService.deleteAdvertisingSpaceInfoByIds(ids));
    }


    /**
     * 导入广告位信息
     * @param file
     * @param updateSupport
     * @return
     * @throws Exception
     */
    @PreAuthorize("@ss.hasPermi('core:advertisingSpaceInfo:import')")
    @Log(title = "广告位信息", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<AdvertisingSpaceInfo> util = new ExcelUtil<>(AdvertisingSpaceInfo.class);
        List<AdvertisingSpaceInfo> lists = util.importExcel(file.getInputStream());
        String message = advertisingSpaceInfoService.importAdvertisingSpaceInfo(lists, updateSupport);
        return AjaxResult.success(message);
    }


}
