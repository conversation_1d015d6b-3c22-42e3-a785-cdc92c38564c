package com.recruit.core.domain;

import com.recruit.common.annotation.Excel;
import com.recruit.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 简历求职意愿对象 recruit_resume_job_desire
 *
 * <AUTHOR>
 * @date 2023-03-21
 */
@Data
@ApiModel("简历求职意愿")
public class RecruitResumeJobDesire extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 用户id */
    @Excel(name = "用户id")
    @ApiModelProperty("用户id")
    private Long userId;

    /** 期望岗位 */
    @Excel(name = "期望岗位")
    @ApiModelProperty("期望岗位")
    private String expectedPosition;

    @ApiModelProperty("期望岗位名称")
    private String expectedPositionName;

    /** 工作职能 */
    @Excel(name = "工作职能")
    @ApiModelProperty("工作职能")
    private String workFunction;

    @ApiModelProperty("工作职能名称")
    private String workFunctionName;

    /** 从事行业 */
    @Excel(name = "从事行业")
    @ApiModelProperty("从事行业")
    private String engagedInIndustry;

    /** 从事行业 */
    @Excel(name = "从事行业名称")
    @ApiModelProperty("从事行业名称")
    private String engagedInIndustryName;

    /** 期望城市 */
    @Excel(name = "期望城市")
    @ApiModelProperty("期望城市")
    private String expectedCity;

    @ApiModelProperty("期望城市")
    private List<String> expectedCitys;

    @Excel(name = "期望城市名称")
    @ApiModelProperty("期望城市名称")
    private String expectedCityName;

    /** 期望薪资 */
    @Excel(name = "最高薪资")
    @ApiModelProperty("最高薪资")
    private String maximumSalary;

    @Excel(name = "最低薪资")
    @ApiModelProperty("最低薪资")
    private String minimumWage;

    /** 到岗时间 */
    @Excel(name = "到岗时间")
    @ApiModelProperty("到岗时间")
    private String timeOfArrival;

    /** 求职状态 */
    @Excel(name = "求职状态")
    @ApiModelProperty("求职状态")
    private String jobStatus;

    @Excel(name = "求职状态名称")
    @ApiModelProperty("求职状态名称")
    private String jobStatusName;

    @Excel(name = "是否隐藏简历")
    @ApiModelProperty("是否隐藏简历")
    private String hideResume;


}
