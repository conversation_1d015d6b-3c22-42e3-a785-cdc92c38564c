package com.recruit.web.controller.core.other;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.recruit.common.annotation.Log;
import com.recruit.common.core.controller.BaseController;
import com.recruit.common.core.domain.AjaxResult;
import com.recruit.common.enums.BusinessType;
import com.recruit.core.domain.RecruitCollect;
import com.recruit.core.service.IRecruitCollectService;
import com.recruit.common.utils.poi.ExcelUtil;
import com.recruit.common.core.page.TableDataInfo;

/**
 * 收藏Controller
 *
 * <AUTHOR>
 * @date 2023-04-12
 */
@RestController
@RequestMapping("/core/collect")
public class RecruitCollectController extends BaseController
{
    @Autowired
    private IRecruitCollectService recruitCollectService;

    /**
     * 查询收藏列表
     */
    @PreAuthorize("@ss.hasPermi('core:collect:list')")
    @GetMapping("/list")
    public TableDataInfo list(RecruitCollect recruitCollect)
    {
        startPage();
        List<RecruitCollect> list = recruitCollectService.selectRecruitCollectList(recruitCollect);
        return getDataTable(list);
    }

    /**
     * 导出收藏列表
     */
    @PreAuthorize("@ss.hasPermi('core:collect:export')")
    @Log(title = "收藏", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RecruitCollect recruitCollect)
    {
        List<RecruitCollect> list = recruitCollectService.selectRecruitCollectList(recruitCollect);
        ExcelUtil<RecruitCollect> util = new ExcelUtil<RecruitCollect>(RecruitCollect.class);
        util.exportExcel(response, list, "收藏数据");
    }

    /**
     * 获取收藏详细信息
     */
    @PreAuthorize("@ss.hasPermi('core:collect:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(recruitCollectService.selectRecruitCollectById(id));
    }

    /**
     * 新增收藏
     */
    @PreAuthorize("@ss.hasPermi('core:collect:add')")
    @Log(title = "收藏", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RecruitCollect recruitCollect)
    {
        return toAjax(recruitCollectService.insertRecruitCollect(recruitCollect));
    }

    /**
     * 修改收藏
     */
    @PreAuthorize("@ss.hasPermi('core:collect:edit')")
    @Log(title = "收藏", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RecruitCollect recruitCollect)
    {
        return toAjax(recruitCollectService.updateRecruitCollect(recruitCollect));
    }

    /**
     * 删除收藏
     */
    @PreAuthorize("@ss.hasPermi('core:collect:remove')")
    @Log(title = "收藏", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(recruitCollectService.deleteRecruitCollectByIds(ids));
    }
}
