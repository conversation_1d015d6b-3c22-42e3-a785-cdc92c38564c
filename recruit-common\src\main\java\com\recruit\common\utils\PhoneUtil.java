package com.recruit.common.utils;

import java.util.regex.Pattern;

/**
 * @Auther: <PERSON> kong
 * @Date: 2023/3/16 9:25
 * @Description:
 */
public class PhoneUtil {


    public static String verifyPhone(String phone) {
        String regex = "^[1]([3-9])[0-9]{9}$";
        String ss = "";
        boolean isMatch = false;
        if (StringUtils.isEmpty(phone)) {
            ss = "手机号不能为空";
        } else if (phone.length() != 11) {
            ss = "手机号应为11位数";
        } else {
            isMatch = Pattern.matches(regex, phone);
            if(isMatch){
                ss = "success";
            }
        }
        return ss;
    }
}
