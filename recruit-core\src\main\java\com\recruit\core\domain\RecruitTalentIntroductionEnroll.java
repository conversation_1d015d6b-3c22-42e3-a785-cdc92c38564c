package com.recruit.core.domain;

import java.util.List;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.recruit.common.annotation.Excel;
import com.recruit.common.annotation.Excels;
import com.recruit.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.poi.ss.usermodel.IndexedColors;

/**
 * 人才引进报名信息对象 recruit_talent_introduction_enroll
 *
 * <AUTHOR>
 * @date 2024-03-15
 */
@Data
@ApiModel("人才引进报名信息")
public class RecruitTalentIntroductionEnroll extends BaseEntity
{
    private static final long serialVersionUID = 1L;


    private Long id;


    @ApiModelProperty("人才引进id")
    private Long talentIntroductionId;


    @ApiModelProperty("人才引进单位id")
    private Long talentIntroductionInfoId;


    @ApiModelProperty("用户id")
    private Long userId;

    @ApiModelProperty("微信openid")
    private String openId;

    @ApiModelProperty("报名次数")
    private Integer applicationNum;


    @Excel(name = "姓名", sort = 1, needMerge = true)
    @ApiModelProperty("姓名")
    private String userName;


    @Excel(name = "性别", dictType = "sys_user_sex", sort = 2, needMerge = true)
    @ApiModelProperty("性别")
    private String sex;


    @Excel(name = "民族", sort = 3, needMerge = true)
    @ApiModelProperty("民族")
    private String nation;


    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "出生年月", width = 30, dateFormat = "yyyy-MM-dd", sort = 4, needMerge = true)
    @ApiModelProperty("出生年月")
    private Date dateOfBirth;

    @Excel(name = "政治面貌", dictType = "political_outlook", sort = 5, needMerge = true)
    @ApiModelProperty("政治面貌")
    private String politicalLandscape;

    @Excel(name = "身份证号", sort = 6, width = 50, needMerge = true)
    @ApiModelProperty("身份证号")
    private String idCard;

    @Excel(name = "移动电话", sort = 7, needMerge = true)
    @ApiModelProperty("移动电话")
    private String phone;

    @Excel(name = "籍贯", sort = 8, width = 50, needMerge = true)
    @ApiModelProperty("籍贯")
    private String hometown;


    @Excel(name = "生源地", sort = 9, width = 50, needMerge = true)
    @ApiModelProperty("生源地")
    private String birthplace;

    @Excel(name = "联系地址省市区", sort = 10, width = 50, needMerge = true)
    @ApiModelProperty("联系地址省市区")
    private String region;

    @Excel(name = "联系地址", sort = 11, width = 50, needMerge = true)
    @ApiModelProperty("联系地址")
    private String contactAddress;

    @Excel(name = "爱好特长", sort = 12, width = 50, needMerge = true)
    @ApiModelProperty("爱好特长")
    private String hobbiesAndSpecialties;

    @Excel(name = "自我评价", sort = 13, width = 50, needMerge = true)
    @ApiModelProperty("自我评价")
    private String selfEvaluation;

    @Excel(name = "论文、课题、社会实践经历", sort = 14, width = 100, needMerge = true)
    @ApiModelProperty("论文、课题、社会实践经历")
    private String enrollOverview;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "报名时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss", sort = 15, needMerge = true)
    @ApiModelProperty("报名时间")
    private Date enrollTime;

    @ApiModelProperty("学历")
    private String education;


    @ApiModelProperty("固定电话")
    private String fixedTelephone;


    @ApiModelProperty("邮箱")
    private String mailbox;

    @ApiModelProperty("主修课程")
    private String majorCourses;


 /*   @Excel(name = "应聘岗位")
    @ApiModelProperty("应聘岗位")
    private String jobApplication;*/


    @ApiModelProperty("面谈地点")
    private String interviewLocation;

    @Excel(name = "审核状态", dictType = "talent_introduction_enroll_type", sort = 16, needMerge = true)
    @ApiModelProperty("状态 ")
    private Integer status;

    @ApiModelProperty("审核人")
    private String reviewedBy;


    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "审核时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss", sort = 17, needMerge = true)
    @ApiModelProperty("审核时间")
    private Date reviewedTime;


    @Excel(name = "审核意见", sort = 29, needMerge = true)
    @ApiModelProperty("审核意见")
    private String reviewComments;


    @ApiModelProperty("人才引进信息")
    private RecruitTalentIntroductionInfo introductionInfo;

    /** 人才引进报名家庭成员 */
    private List<RecruitTalentIntroductionEnrollFamilyMembers> talentIntroductionEnrollFamilyMembersList;

    /** 人才引进报名毕业学校信息 */
    @Excels({
            @Excel(name = "本科毕业学校", targetAttr = "graduationSchool", type = Excel.Type.EXPORT),
            @Excel(name = "专业", targetAttr = "major", type = Excel.Type.EXPORT),
            @Excel(name = "主修课程", targetAttr = "mainCourse", type = Excel.Type.EXPORT),
            @Excel(name = "毕业证照片", targetAttr = "graduationImg", type = Excel.Type.EXPORT),
            @Excel(name = "学位证照片", targetAttr = "degreeImg", type = Excel.Type.EXPORT),
            @Excel(name = "学信网照片", targetAttr = "chsiImg", type = Excel.Type.EXPORT),
            @Excel(name = "学位时间", targetAttr = "degreeTime", type = Excel.Type.EXPORT, dateFormat = "yyyy-MM-dd"),
    })
    private RecruitTalentIntroductionEnrollGraduationSchool graduationSchool;

    private List<RecruitTalentIntroductionEnrollGraduationSchool> talentIntroductionEnrollGraduationSchoolList;

    /** 人才引进报名毕业学校信息 */
    @Excels({
            @Excel(name = "研究生毕业学校", targetAttr = "graduationSchool", type = Excel.Type.EXPORT),
            @Excel(name = "专业", targetAttr = "major", type = Excel.Type.EXPORT),
            @Excel(name = "主修课程", targetAttr = "mainCourse", type = Excel.Type.EXPORT),
            @Excel(name = "毕业证照片", targetAttr = "graduationImg", type = Excel.Type.EXPORT),
            @Excel(name = "学位证照片", targetAttr = "degreeImg", type = Excel.Type.EXPORT),
            @Excel(name = "学信网照片", targetAttr = "chsiImg", type = Excel.Type.EXPORT),
            @Excel(name = "学位时间", targetAttr = "degreeTime", type = Excel.Type.EXPORT, dateFormat = "yyyy-MM-dd"),
    })
    private RecruitTalentIntroductionEnrollGraduationSchool graduationSchoolTwo;
    private List<RecruitTalentIntroductionEnrollGraduationSchool> talentIntroductionEnrollGraduationSchoolListTwo;

    /** 人才引进报名毕业学校信息 */
    @Excels({
            @Excel(name = "博士毕业学校", targetAttr = "graduationSchool", type = Excel.Type.EXPORT),
            @Excel(name = "专业", targetAttr = "major", type = Excel.Type.EXPORT),
            @Excel(name = "主修课程", targetAttr = "mainCourse", type = Excel.Type.EXPORT),
            @Excel(name = "毕业证照片", targetAttr = "graduationImg", type = Excel.Type.EXPORT),
            @Excel(name = "学位证照片", targetAttr = "degreeImg", type = Excel.Type.EXPORT),
            @Excel(name = "学信网照片", targetAttr = "chsiImg", type = Excel.Type.EXPORT),
            @Excel(name = "学位时间", targetAttr = "degreeTime", type = Excel.Type.EXPORT, dateFormat = "yyyy-MM-dd"),
    })
    private RecruitTalentIntroductionEnrollGraduationSchool graduationSchoolThree;
    private List<RecruitTalentIntroductionEnrollGraduationSchool> talentIntroductionEnrollGraduationSchoolListThree;

    /** 人才引进报名毕业学校信息 */
    @Excels({
            @Excel(name = "高中毕业学校", targetAttr = "graduationSchool", type = Excel.Type.EXPORT),
            @Excel(name = "毕业证照片", targetAttr = "graduationImg", type = Excel.Type.EXPORT),
            @Excel(name = "学位时间", targetAttr = "degreeTime", type = Excel.Type.EXPORT, dateFormat = "yyyy-MM-dd"),
    })
    private RecruitTalentIntroductionEnrollGraduationSchool graduationSchoolFour;
    private List<RecruitTalentIntroductionEnrollGraduationSchool> talentIntroductionEnrollGraduationSchoolListFour;

    /** 人才引进报名毕业学校信息 */
    @Excels({
            @Excel(name = "大专毕业学校", targetAttr = "graduationSchool", type = Excel.Type.EXPORT),
            @Excel(name = "专业", targetAttr = "major", type = Excel.Type.EXPORT),
            @Excel(name = "主修课程", targetAttr = "mainCourse", type = Excel.Type.EXPORT),
            @Excel(name = "毕业证照片", targetAttr = "graduationImg", type = Excel.Type.EXPORT),
            @Excel(name = "学信网照片", targetAttr = "chsiImg", type = Excel.Type.EXPORT),
            @Excel(name = "学历时间", targetAttr = "degreeTime", type = Excel.Type.EXPORT, dateFormat = "yyyy-MM-dd"),
    })
    private RecruitTalentIntroductionEnrollGraduationSchool graduationSchoolFive;
    private List<RecruitTalentIntroductionEnrollGraduationSchool> talentIntroductionEnrollGraduationSchoolListFive;

    /** 人才引进报名学习工作 */
    private List<RecruitTalentIntroductionEnrollLearningAndWork> talentIntroductionEnrollLearningAndWorkList;

    /** 人才引进照片 */
    //@Excel(name = "照片信息", sort = 37)
    private List<RecruitTalentIntroductionEnrollPhotos> talentIntroductionEnrollPhotosList;


    @Excel(name = "单位名称", needMerge = true, height = 40, backgroundColor = IndexedColors.YELLOW)
    @ApiModelProperty("单位名称")
    private String nameOfEmployer;

    @Excel(name = "岗位类别", needMerge = true, backgroundColor = IndexedColors.YELLOW)
    @ApiModelProperty("岗位类别")
    private String jobCategory;

    @Excel(name = "岗位名称", needMerge = true, backgroundColor = IndexedColors.YELLOW)
    @ApiModelProperty("岗位名称")
    private String recruitmentPositions;

    @ApiModelProperty("应聘岗位代码")
    private String jobApplicationCode;

    @ApiModelProperty("应聘岗位名称")
    private String jobApplicationName;

    @Excel(name = "岗位代码", needMerge = true, backgroundColor = IndexedColors.YELLOW)
    @ApiModelProperty("岗位代码")
    private String nameOfSuperiorUnit;

    @Excel(name = "招聘人数", needMerge = true, backgroundColor = IndexedColors.YELLOW)
    @ApiModelProperty("招聘人数")
    private String recruitingNum;

    @Excel(name = "招聘年龄", needMerge = true, backgroundColor = IndexedColors.YELLOW)
    @ApiModelProperty("招聘年龄")
    private String ageRequirements;

    @Excel(name = "招聘学历", needMerge = true, backgroundColor = IndexedColors.YELLOW)
    @ApiModelProperty("招聘学历")
    private String educationalRequirements;

    @Excel(name = "招聘学位", needMerge = true, backgroundColor = IndexedColors.YELLOW)
    @ApiModelProperty("招聘学位")
    private String academicDegree;

    @Excel(name = "专业名称", width = 50, needMerge = true, backgroundColor = IndexedColors.YELLOW)
    @ApiModelProperty("专业名称")
    private String professionalName;

    @Excel(name = "专业三级目录限制要求", width = 100, needMerge = true, backgroundColor = IndexedColors.YELLOW)
    @ApiModelProperty("专业三级目录限制要求")
    private String professionalRequirements;

    @Excel(name = "岗位描述", width = 50, needMerge = true, backgroundColor = IndexedColors.YELLOW)
    @ApiModelProperty("岗位描述")
    private String jobDescription;

    @Excel(name = "所在区域", width = 50, needMerge = true, backgroundColor = IndexedColors.YELLOW)
    @ApiModelProperty("所在区域")
    private String regionName;

    @ApiModelProperty("所在区域")
    private String idCardNum;


}
