package com.recruit.core.domain.request.talent;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("学习、工作经历")
public class VitaeRequest {

    @ApiModelProperty("开始时间")
    private String start_date;

    @ApiModelProperty("结束时间")
    private String end_date;

    @ApiModelProperty("单位名称")
    private String organization_name;

    @ApiModelProperty("职务")
    private String job_name;
}
