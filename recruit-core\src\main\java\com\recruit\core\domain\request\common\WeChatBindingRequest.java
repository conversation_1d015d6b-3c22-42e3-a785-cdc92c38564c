package com.recruit.core.domain.request.common;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Auther: <PERSON> kong
 * @Date: 2023/5/24 22:59
 * @Description:
 */
@Data
@ApiModel("设置微信绑定")
public class WeChatBindingRequest {

    @ApiModelProperty("微信昵称")
    private String nickname;

    @ApiModelProperty("微信openId")
    private String openId;
}
