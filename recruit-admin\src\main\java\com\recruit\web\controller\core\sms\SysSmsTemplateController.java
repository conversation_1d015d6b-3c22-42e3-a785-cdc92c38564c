package com.recruit.web.controller.core.sms;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.recruit.common.exception.ServiceException;
import com.recruit.common.utils.StringUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.recruit.common.annotation.Log;
import com.recruit.common.core.controller.BaseController;
import com.recruit.common.core.domain.AjaxResult;
import com.recruit.common.enums.BusinessType;
import com.recruit.core.domain.SysSmsTemplate;
import com.recruit.core.service.ISysSmsTemplateService;
import com.recruit.common.utils.poi.ExcelUtil;
import com.recruit.common.core.page.TableDataInfo;

import static com.recruit.common.utils.SecurityUtils.getUsername;

/**
 * 系统短信模板Controller
 *
 * <AUTHOR>
 * @date 2023-05-12
 */
@RestController
@RequestMapping("/core/smsTemplate")
public class SysSmsTemplateController extends BaseController
{
    @Autowired
    private ISysSmsTemplateService sysSmsTemplateService;

    /**
     * 查询系统短信模板列表
     */
    @PreAuthorize("@ss.hasPermi('core:smsTemplate:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysSmsTemplate sysSmsTemplate)
    {
        startPage();
        List<SysSmsTemplate> list = sysSmsTemplateService.selectSysSmsTemplateList(sysSmsTemplate);
        return getDataTable(list);
    }

    /**
     * 导出系统短信模板列表
     */
    @PreAuthorize("@ss.hasPermi('core:smsTemplate:export')")
    @Log(title = "系统短信模板", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysSmsTemplate sysSmsTemplate)
    {
        List<SysSmsTemplate> list = sysSmsTemplateService.selectSysSmsTemplateList(sysSmsTemplate);
        ExcelUtil<SysSmsTemplate> util = new ExcelUtil<SysSmsTemplate>(SysSmsTemplate.class);
        util.exportExcel(response, list, "系统短信模板数据");
    }

    /**
     * 获取系统短信模板详细信息
     */
    @PreAuthorize("@ss.hasPermi('core:smsTemplate:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(sysSmsTemplateService.selectSysSmsTemplateById(id));
    }

    /**
     * 新增系统短信模板
     */
    @PreAuthorize("@ss.hasPermi('core:smsTemplate:add')")
    @Log(title = "系统短信模板", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SysSmsTemplate sysSmsTemplate)
    {
        sysSmsTemplate.setCreateBy(getUsername());
        return toAjax(sysSmsTemplateService.insertSysSmsTemplate(sysSmsTemplate));
    }

    /**
     * 修改系统短信模板
     */
    @PreAuthorize("@ss.hasPermi('core:smsTemplate:edit')")
    @Log(title = "系统短信模板", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SysSmsTemplate sysSmsTemplate)
    {
        return toAjax(sysSmsTemplateService.updateSysSmsTemplate(sysSmsTemplate));
    }

    /**
     * 删除系统短信模板
     */
    @PreAuthorize("@ss.hasPermi('core:smsTemplate:remove')")
    @Log(title = "系统短信模板", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(sysSmsTemplateService.deleteSysSmsTemplateByIds(ids));
    }

    @GetMapping("/getSmsTemplateList")
    public TableDataInfo getSmsTemplateList()
    {
        SysSmsTemplate sysSmsTemplate = new SysSmsTemplate();
        sysSmsTemplate.setStatusCode("0");
        List<SysSmsTemplate> list = sysSmsTemplateService.getSmsTemplateList(sysSmsTemplate);
        return getDataTable(list);
    }
}
