package com.recruit.core.domain.request.resume;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @Auther: <PERSON> kong
 * @Date: 2023/3/26 21:49
 * @Description:
 */
@Data
@ApiModel("简历其它信息1")
public class ResumeOtherInfoRequest {


    @NotBlank(message = "自我评价不能为空")
    @ApiModelProperty("自我评价")
    private String selfEvaluation;
}
