package com.recruit.web.controller.core.other;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.recruit.common.annotation.Log;
import com.recruit.common.core.controller.BaseController;
import com.recruit.common.core.domain.AjaxResult;
import com.recruit.common.enums.BusinessType;
import com.recruit.core.domain.SysSensitive;
import com.recruit.core.service.ISysSensitiveService;
import com.recruit.common.utils.poi.ExcelUtil;
import com.recruit.common.core.page.TableDataInfo;

/**
 * 系统敏感词Controller
 *
 * <AUTHOR>
 * @date 2023-05-06
 */
@RestController
@RequestMapping("/core/sensitive")
public class SysSensitiveController extends BaseController
{
    @Autowired
    private ISysSensitiveService sysSensitiveService;

    /**
     * 查询系统敏感词列表
     */
    @PreAuthorize("@ss.hasPermi('core:sensitive:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysSensitive sysSensitive)
    {
        startPage();
        List<SysSensitive> list = sysSensitiveService.selectSysSensitiveList(sysSensitive);
        return getDataTable(list);
    }

    /**
     * 导出系统敏感词列表
     */
    @PreAuthorize("@ss.hasPermi('core:sensitive:export')")
    @Log(title = "系统敏感词", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysSensitive sysSensitive)
    {
        List<SysSensitive> list = sysSensitiveService.selectSysSensitiveList(sysSensitive);
        ExcelUtil<SysSensitive> util = new ExcelUtil<SysSensitive>(SysSensitive.class);
        util.exportExcel(response, list, "系统敏感词数据");
    }

    /**
     * 获取系统敏感词详细信息
     */
    @PreAuthorize("@ss.hasPermi('core:sensitive:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(sysSensitiveService.selectSysSensitiveById(id));
    }

    /**
     * 新增系统敏感词
     */
    @PreAuthorize("@ss.hasPermi('core:sensitive:add')")
    @Log(title = "系统敏感词", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SysSensitive sysSensitive)
    {
        return toAjax(sysSensitiveService.insertSysSensitive(sysSensitive));
    }

    /**
     * 修改系统敏感词
     */
    @PreAuthorize("@ss.hasPermi('core:sensitive:edit')")
    @Log(title = "系统敏感词", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SysSensitive sysSensitive)
    {
        return toAjax(sysSensitiveService.updateSysSensitive(sysSensitive));
    }

    /**
     * 删除系统敏感词
     */
    @PreAuthorize("@ss.hasPermi('core:sensitive:remove')")
    @Log(title = "系统敏感词", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(sysSensitiveService.deleteSysSensitiveByIds(ids));
    }
}
