package com.recruit.core.domain.request.merchant;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Auther: <PERSON> kong
 * @Date: 2023/6/7 11:11
 * @Description:
 */
@Data
public class ConfirmPaymentRequest {

    @ApiModelProperty("订单id")
    private Long serviceUserOrderId;

    @ApiModelProperty("订单状态 0预约，1下单，2预约确认，3待支付，4待服务，5订单完成，6取消订单，7退款")
    private String orderStatus;

    @ApiModelProperty("支付渠道 1微信，2支付宝, 3建行")
    private String payChannel;

    @ApiModelProperty("渠道 1小程序，2PC")
    private String channel;
}
