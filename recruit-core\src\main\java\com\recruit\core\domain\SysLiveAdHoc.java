package com.recruit.core.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.recruit.common.annotation.Excel;
import com.recruit.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 系统直播对象 sys_live_ad_hoc
 *
 * <AUTHOR>
 * @date 2023-05-16
 */
@Data
@ApiModel("系统直播")
public class SysLiveAdHoc extends BaseEntity
{
    private static final long serialVersionUID = 1L;


    private Long id;


    @Excel(name = "直播标题")
    @ApiModelProperty("直播标题")
    private String title;


    @Excel(name = "直播地址")
    @ApiModelProperty("直播地址")
    private String liveAddress;


    @Excel(name = "状态，1直播中，2直播结束")
    @ApiModelProperty("状态，1直播中，2直播结束")
    private String state;


    @Excel(name = "小程序图片地址")
    @ApiModelProperty("小程序图片地址")
    private String appPicUrl;


    @Excel(name = "pc图片地址")
    @ApiModelProperty("pc图片地址")
    private String pcPicUrl;


    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("开始时间")
    private Date startTime;


    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("结束时间")
    private Date endTime;


}
