package com.recruit.core.domain.request.common;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Auther: <PERSON> kong
 * @Date: 2023/3/19 13:37
 * @Description:
 */
@Data
@ApiModel("验证码1")
public class UserLoginBodyRequest {

    @ApiModelProperty("手机号")
    private String phone;

    @ApiModelProperty("验证码")
    private String smsCode;

    @ApiModelProperty("openid")
    private String openid;

    @ApiModelProperty("微信昵称")
    private String wxNickname;

    @ApiModelProperty("密码")
    private String password;
}
