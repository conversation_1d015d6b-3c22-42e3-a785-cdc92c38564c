package com.recruit.webApi;

import com.recruit.common.config.PublicConfig;
import com.recruit.common.core.controller.BaseController;
import com.recruit.common.core.domain.AjaxResult;
import com.recruit.common.core.domain.vo.UploadImageVO;
import com.recruit.common.utils.StringUtils;
import com.recruit.common.utils.file.FileUtils;
import com.recruit.system.service.thirdparty.FileService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @Auther: Wu kong
 * @Date: 2023/4/17 22:04
 * @Description:
 */
@Api(tags= "公共上传接口")
@RestController
@RequestMapping("/webApi/common")
public class CommonUploadController extends BaseController {


    private static final String FILE_DELIMETER = ",";

    public static final String REG_IMG_FORMAT = "^.+(.JPEG|.jpeg|.JPG|.jpg|.PNG|.png|.GIF|.gif)$";


    @Autowired
    private FileService fileService;


    /**
     * 通用上传请求（单个）
     */
    @ApiOperation("通用上传请求（单个）file")
    @PostMapping("/upload")
    public AjaxResult uploadFile(MultipartFile file) throws Exception
    {
        try
        {
            // 上传文件路径
            String filePath = PublicConfig.getUploadPath();
            // 上传并返回新文件名称
            //String fileName = FileUploadUtils.upload(filePath, file);
            AjaxResult ajax = AjaxResult.success();
            if(isImage(file)){
                UploadImageVO uploadImage = fileService.uploadImage(file);
                ajax.put("url", uploadImage.getOriginUrl());
                ajax.put("fileName", uploadImage.getOriginUrl());
                ajax.put("newFileName", FileUtils.getName(uploadImage.getOriginUrl()));
                ajax.put("originalFilename", file.getOriginalFilename());
            }else {
                String thumbUrl = fileService.uploadFile(file);
                ajax.put("url", thumbUrl);
                ajax.put("fileName", thumbUrl);
                ajax.put("newFileName", FileUtils.getName(thumbUrl));
                ajax.put("originalFilename", file.getOriginalFilename());
            }
            //String url = serverConfig.getUrl() + fileName;
            return ajax;
        }
        catch (Exception e)
        {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 判断MultipartFile对象是否为图片
     * @param file
     * @return
     */
    public static boolean isImage(MultipartFile file) {
        Matcher matcher = Pattern.compile(REG_IMG_FORMAT).matcher(file.getOriginalFilename());
        return matcher.find();
    }


    /**
     * 通用上传请求（多个）
     */
    @ApiOperation(value = "通用上传请求（多个）",notes = "通用上传请求（多个）",consumes = "multipart/form-data",response = Object.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "file", paramType="form", value = "临时文件", dataType="file", collectionFormat="array", required = true),
    })
    @RequestMapping(value = "/uploads",method = {RequestMethod.POST},headers = "content-type=multipart/form-data")
    public AjaxResult uploadFiles(@ApiParam(value="文件",required=true) @RequestPart("files")MultipartFile[] files) throws Exception
    {
        try
        {
            // 上传文件路径
            String filePath = PublicConfig.getUploadPath();
            List<String> urls = new ArrayList<String>();
            List<String> fileNames = new ArrayList<String>();
            List<String> newFileNames = new ArrayList<String>();
            List<String> originalFilenames = new ArrayList<String>();
            for (MultipartFile file : files)
            {
                // 上传并返回新文件名称
                /*String fileName = FileUploadUtils.upload(filePath, file);
                String url = serverConfig.getUrl() + fileName;*/

                UploadImageVO uploadImage = fileService.uploadImage(file);
                urls.add(uploadImage.getOriginUrl());
                fileNames.add(uploadImage.getOriginUrl());
                newFileNames.add(FileUtils.getName(uploadImage.getOriginUrl()));
                originalFilenames.add(file.getOriginalFilename());
            }
            AjaxResult ajax = AjaxResult.success();
            ajax.put("urls", StringUtils.join(urls, FILE_DELIMETER));
            ajax.put("fileNames", StringUtils.join(fileNames, FILE_DELIMETER));
            ajax.put("newFileNames", StringUtils.join(newFileNames, FILE_DELIMETER));
            ajax.put("originalFilenames", StringUtils.join(originalFilenames, FILE_DELIMETER));
            return ajax;
        }
        catch (Exception e)
        {
            return AjaxResult.error(e.getMessage());
        }
    }

    @ApiOperation(value = "上传图片",notes="上传图片,上传后返回原图和缩略图的url")
    @PostMapping("/image/upload")
    public AjaxResult uploadImage(MultipartFile file) {
        return AjaxResult.success(fileService.uploadImage(file));
    }


    @ApiOperation(value = "上传文件",notes="上传文件，上传后返回文件url")
    @PostMapping("/file/upload")
    public AjaxResult uploadFilea(MultipartFile file) {
        return success(fileService.uploadFile(file));
    }
}
