package com.recruit.web.controller.core.company;

import com.recruit.common.annotation.Log;
import com.recruit.common.core.controller.BaseController;
import com.recruit.common.core.domain.AjaxResult;
import com.recruit.common.core.page.TableDataInfo;
import com.recruit.common.enums.BusinessType;
import com.recruit.common.utils.poi.ExcelUtil;
import com.recruit.core.domain.ExaminationService;
import com.recruit.core.service.IExaminationServiceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 劳动争议
 *
 * <AUTHOR>
 * @date 2023-04-11
 */
@RestController
@RequestMapping("/core/laborDisputes")
public class LaborDisputesController extends BaseController
{
    @Autowired
    private IExaminationServiceService examinationServiceService;

    /**
     * 查询服务之窗列表
     */
    @PreAuthorize("@ss.hasPermi('core:laborDisputes:list')")
    @GetMapping("/list")
    public TableDataInfo list(ExaminationService examinationService)
    {
        startPage();
        List<ExaminationService> list = examinationServiceService.selectExaminationServiceList(examinationService);
        return getDataTable(list);
    }

    /**
     * 导出服务之窗列表
     */
    @PreAuthorize("@ss.hasPermi('core:laborDisputes:export')")
    @Log(title = "服务之窗", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ExaminationService examinationService)
    {
        List<ExaminationService> list = examinationServiceService.selectExaminationServiceList(examinationService);
        ExcelUtil<ExaminationService> util = new ExcelUtil<ExaminationService>(ExaminationService.class);
        util.exportExcel(response, list, "服务之窗数据");
    }

    /**
     * 获取服务之窗详细信息
     */
    @PreAuthorize("@ss.hasPermi('core:laborDisputes:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(examinationServiceService.selectExaminationServiceById(id));
    }

    /**
     * 新增服务之窗
     */
    @PreAuthorize("@ss.hasPermi('core:laborDisputes:add')")
    @Log(title = "服务之窗", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ExaminationService examinationService)
    {
        return toAjax(examinationServiceService.insertExaminationService(examinationService));
    }

    /**
     * 修改服务之窗
     */
    @PreAuthorize("@ss.hasPermi('core:laborDisputes:edit')")
    @Log(title = "服务之窗", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ExaminationService examinationService)
    {
        return toAjax(examinationServiceService.updateExaminationService(examinationService));
    }

    /**
     * 删除服务之窗
     */
    @PreAuthorize("@ss.hasPermi('core:laborDisputes:remove')")
    @Log(title = "服务之窗", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(examinationServiceService.deleteExaminationServiceByIds(ids));
    }
}
