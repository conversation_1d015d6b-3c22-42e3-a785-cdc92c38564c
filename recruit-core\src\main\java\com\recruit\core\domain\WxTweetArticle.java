package com.recruit.core.domain;

import com.google.gson.annotations.SerializedName;
import com.recruit.common.annotation.Excel;
import com.recruit.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import me.chanjar.weixin.mp.bean.draft.WxMpDraftItem;

import java.util.List;

/**
 * 推文生成对象 wx_tweet_article
 *
 * <AUTHOR>
 * @date 2023-10-16
 */
@Data
@ApiModel("推文生成")
public class WxTweetArticle extends BaseEntity
{
    private static final long serialVersionUID = 1L;


    private Long id;


    @Excel(name = "企业id")
    @ApiModelProperty("企业id")
    private String enterpriseInfoId;


    @Excel(name = "职位id")
    @ApiModelProperty("职位id")
    private String positionInfoId;


    @Excel(name = "文章id")
    @ApiModelProperty("文章id")
    private String mediaId;


    @Excel(name = "推文类型")
    @ApiModelProperty("推文类型")
    private String tweetType;


    @Excel(name = "模板类型")
    @ApiModelProperty("模板类型")
    private String templateType;

    @ApiModelProperty("公众号模板")
    private String publicTemplateType;


    @Excel(name = "图文消息的内容")
    @ApiModelProperty("图文消息的内容")
    private String content;


    @Excel(name = "状态0未发布，1已发布")
    @ApiModelProperty("状态0未发布，1已发布")
    private String status;

    @ApiModelProperty("标题")
    private String title;

    //职位id
    private Long[] positionInfoIds;

    //企业id
    private Long[] enterpriseInfoIds;


}
