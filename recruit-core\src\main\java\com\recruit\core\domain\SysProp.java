package com.recruit.core.domain;

import java.util.List;
import com.recruit.common.annotation.Excel;
import com.recruit.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 系统道具对象 sys_prop
 *
 * <AUTHOR>
 * @date 2023-05-24
 */
@Data
@ApiModel("系统道具")
public class SysProp extends BaseEntity
{
    private static final long serialVersionUID = 1L;


    private Long id;


    @Excel(name = "道具类型")
    @ApiModelProperty("道具类型")
    private String propType;

    @ApiModelProperty("道具类型名称")
    private String propTypeName;


    @Excel(name = "有效期")
    @ApiModelProperty("有效期")
    private Long expirationNum;


    @Excel(name = "道具描述")
    @ApiModelProperty("道具描述")
    private String propDescribe;


    @Excel(name = "类型")
    @ApiModelProperty("类型")
    private String chatType;

    /** 道具价格信息 */
    private List<SysPropPrice> sysPropPriceList;


}
