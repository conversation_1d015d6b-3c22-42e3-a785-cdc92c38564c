package com.recruit.core.domain.response;

import com.recruit.core.domain.AdvertisingSpaceInfo;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.List;

/**
 * @Auther: <PERSON> kong
 * @Date: 2023/6/18 23:47
 * @Description:
 */
@Data
@ApiModel("广告位信息")
public class AdvertisingSpaceResponse {

    /**
     * 1号广告位
     */
    private List<AdvertisingSpaceInfo> oneAdvertisingSpaceList;

    /**
     * 2号广告位
     */
    private List<AdvertisingSpaceInfo> twoAdvertisingSpaceList;

    /**
     * 3号广告位
     */
    private List<AdvertisingSpaceInfo> threeAdvertisingSpaceList;

}
