package com.recruit.web.controller.core.merchant;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.recruit.common.annotation.Log;
import com.recruit.common.core.controller.BaseController;
import com.recruit.common.core.domain.AjaxResult;
import com.recruit.common.enums.BusinessType;
import org.springframework.web.multipart.MultipartFile;
import com.recruit.core.domain.TemporaryOrderEvaluate;
import com.recruit.core.service.ITemporaryOrderEvaluateService;
import com.recruit.common.utils.poi.ExcelUtil;
import com.recruit.common.core.page.TableDataInfo;

/**
 * 订单评价Controller
 *
 * <AUTHOR>
 * @date 2023-06-03
 */
@RestController
@RequestMapping("/core/orderEvaluate")
public class TemporaryOrderEvaluateController extends BaseController
{
    @Autowired
    private ITemporaryOrderEvaluateService temporaryOrderEvaluateService;

    /**
     * 查询订单评价列表
     */
    @PreAuthorize("@ss.hasPermi('core:orderEvaluate:list')")
    @GetMapping("/list")
    public TableDataInfo list(TemporaryOrderEvaluate temporaryOrderEvaluate)
    {
        startPage();
        List<TemporaryOrderEvaluate> list = temporaryOrderEvaluateService.selectTemporaryOrderEvaluateList(temporaryOrderEvaluate);
        return getDataTable(list);
    }

    /**
     * 导出订单评价列表
     */
    @PreAuthorize("@ss.hasPermi('core:orderEvaluate:export')")
    @Log(title = "订单评价", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TemporaryOrderEvaluate temporaryOrderEvaluate)
    {
        List<TemporaryOrderEvaluate> list = temporaryOrderEvaluateService.selectTemporaryOrderEvaluateList(temporaryOrderEvaluate);
        ExcelUtil<TemporaryOrderEvaluate> util = new ExcelUtil<TemporaryOrderEvaluate>(TemporaryOrderEvaluate.class);
        util.exportExcel(response, list, "订单评价数据");
    }

    /**
     * 获取订单评价详细信息
     */
    @PreAuthorize("@ss.hasPermi('core:orderEvaluate:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(temporaryOrderEvaluateService.selectTemporaryOrderEvaluateById(id));
    }

    /**
     * 新增订单评价
     */
    @PreAuthorize("@ss.hasPermi('core:orderEvaluate:add')")
    @Log(title = "订单评价", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TemporaryOrderEvaluate temporaryOrderEvaluate)
    {
        return toAjax(temporaryOrderEvaluateService.insertTemporaryOrderEvaluate(temporaryOrderEvaluate));
    }

    /**
     * 修改订单评价
     */
    @PreAuthorize("@ss.hasPermi('core:orderEvaluate:edit')")
    @Log(title = "订单评价", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TemporaryOrderEvaluate temporaryOrderEvaluate)
    {
        return toAjax(temporaryOrderEvaluateService.updateTemporaryOrderEvaluate(temporaryOrderEvaluate));
    }

    /**
     * 删除订单评价
     */
    @PreAuthorize("@ss.hasPermi('core:orderEvaluate:remove')")
    @Log(title = "订单评价", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(temporaryOrderEvaluateService.deleteTemporaryOrderEvaluateByIds(ids));
    }


    /**
     * 导入订单评价
     * @param file
     * @param updateSupport
     * @return
     * @throws Exception
     */
    @PreAuthorize("@ss.hasPermi('core:orderEvaluate:import')")
    @Log(title = "订单评价", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<TemporaryOrderEvaluate> util = new ExcelUtil<>(TemporaryOrderEvaluate.class);
        List<TemporaryOrderEvaluate> lists = util.importExcel(file.getInputStream());
        String message = temporaryOrderEvaluateService.importTemporaryOrderEvaluate(lists, updateSupport);
        return AjaxResult.success(message);
    }


}
