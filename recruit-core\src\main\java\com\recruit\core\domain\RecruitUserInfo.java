package com.recruit.core.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.recruit.common.annotation.Excel;
import com.recruit.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 用户信息对象 recruit_user_info
 *
 * <AUTHOR>
 * @date 2023-03-21
 */
@Data
@ApiModel("用户信息")
public class RecruitUserInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 姓名 */
    @Excel(name = "姓名")
    @ApiModelProperty("姓名")
    private String userName;

    @ApiModelProperty("微信昵称")
    private String nickname;

    /** 头像 */
    @Excel(name = "头像")
    @ApiModelProperty("头像")
    private String headSculpture;

    /** 身份证号 */
    @Excel(name = "认证姓名")
    @ApiModelProperty("认证姓名")
    private String authenticationName;
    /** 身份证号 */
    @Excel(name = "身份证号")
    @ApiModelProperty("身份证号")
    private String idCard;

    /** 性别 */
    @Excel(name = "性别")
    @ApiModelProperty("性别")
    private String sex;

    @Excel(name = "性别名称")
    @ApiModelProperty("性别名称")
    private String sexName;

    /** 手机号 */
    @Excel(name = "手机号")
    @ApiModelProperty("手机号")
    private String phone;

    /** 邮箱 */
    @Excel(name = "邮箱")
    @ApiModelProperty("邮箱")
    private String mailbox;

    /** 密码 */
    @Excel(name = "密码")
    @ApiModelProperty("密码")
    private String password;

    /** 微信登录openid */
    @Excel(name = "微信登录openid")
    @ApiModelProperty("微信登录openid")
    private String openId;

    /** 账号状态 0：正常 1：禁用 2：注销 */
    @Excel(name = "账号状态")
    @ApiModelProperty("账号状态 0：正常 1：禁用 2：注销")
    private Integer status;

    /** 微信号 */
    @Excel(name = "微信号")
    @ApiModelProperty("微信号")
    private String wechatNum;

    /** 出生年月 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "出生年月", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("出生年月")
    private Date dateOfBirth;

    /** 参加工作时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "参加工作时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("参加工作时间")
    private Date workingTime;

    /** 类型 1：求职者 2：招聘者 */
    @Excel(name = "类型 1：求职者 2：招聘者")
    @ApiModelProperty("类型 1：求职者 2：招聘者")
    private String type;

    @ApiModelProperty("企业id")
    private Long enterpriseId;

    @ApiModelProperty("公司名称")
    private String enterpriseName;

    @ApiModelProperty("是否新用户")
    private Integer newUser;

    @ApiModelProperty("求职者 0否，1是")
    private Integer jobSeekers;

    @ApiModelProperty("招聘者 0否，1是")
    private Integer recruiter;

    @ApiModelProperty("订阅状态 0未订阅，1订阅")
    private Integer subscriptionStatus;

    @ApiModelProperty("账户积分")
    private String accountIntegral;

    @ApiModelProperty("账户金额")
    private BigDecimal accountAmount;

    @ApiModelProperty("绑定状态")
    private String bindingStatus;

    @ApiModelProperty("置顶状态")
    private String topping;

    /** 最后登录IP */
    private String loginIp;

    /** 最后登录时间 */
    private Date loginDate;

    /** 最后登录时间 */
    private String loginDateTwo;

    /** 地址 */
    private String address;

    /** 门牌号 */
    private String houseNumber;

    /** 经度 */
    private String longitude;

    /** 纬度 */
    private String latitude;

    /** 联系人 */
    private String liaisonNan;

    @ApiModelProperty("联系人手机号")
    private String liaisonNanPhone;

    @ApiModelProperty("区域")
    private String city;
}
