package com.recruit.core.domain.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Auther: <PERSON> kong
 * @Date: 2023/3/21 19:40
 * @Description:
 */
@Data
@ApiModel("获取联系人信息1")
public class ContactsResponse {

    @ApiModelProperty("头像")
    private String headSculpture;

    @ApiModelProperty("姓名")
    private String userName;

    @ApiModelProperty("职位")
    private String position;

    @ApiModelProperty("企业名称")
    private String enterpriseName;

    @ApiModelProperty("邮箱")
    private String mailbox;

}
