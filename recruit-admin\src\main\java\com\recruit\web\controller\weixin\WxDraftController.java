package com.recruit.web.controller.weixin;

import com.alibaba.fastjson2.JSONArray;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.gson.Gson;
import com.recruit.common.core.controller.BaseController;
import com.recruit.common.core.domain.AjaxResult;
import com.recruit.common.utils.StringUtils;
import com.recruit.core.domain.WxTweetArticle;
import com.recruit.core.domain.request.wx.WxDraftRequest;
import com.recruit.core.domain.response.WxMpDraftInfoResponse;
import com.recruit.core.domain.response.WxMpDraftItemResponse;
import com.recruit.core.domain.response.WxMpDraftResponse;
import com.recruit.core.service.IWxTweetArticleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpDraftService;
import me.chanjar.weixin.mp.api.WxMpFreePublishService;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.draft.*;
import org.apache.commons.beanutils.ConvertUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * 微信草稿箱
 *
 * <AUTHOR>
 * @date 2022-03-10 21:26:35
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/wxdraft")
@Api(value = "wxdraft", tags = "微信草稿箱")
public class WxDraftController extends BaseController {

	private final WxMpService wxService;

	@Autowired
	private IWxTweetArticleService wxTweetArticleService;
	/**
	 * 新增图文消息
	 * @return
	 */
	@ApiOperation(value = "新增草稿箱")
	@Transactional
	@PostMapping
	@PreAuthorize("@ss.hasPermi('wxmp:wxdraft:add')")
	public AjaxResult add(@RequestBody WxDraftRequest request) throws Exception {
		WxTweetArticle wxTweetArticle = new WxTweetArticle();
		StringBuilder positionInfoId = new StringBuilder();
		StringBuilder enterpriseInfoId = new StringBuilder();
		if(StringUtils.isNotEmpty(request.getPositionInfoIds())){
			for(Long ss : request.getPositionInfoIds()){
				if(StringUtils.equals(positionInfoId, "")){
					positionInfoId.append(ss);
				}else {
					positionInfoId.append(",").append(ss);
				}
			}
			wxTweetArticle.setPositionInfoId(String.valueOf(positionInfoId));
		}
		if(StringUtils.isNotEmpty(request.getEnterpriseInfoIds())){
			for(Long ss : request.getEnterpriseInfoIds()){
				if(StringUtils.equals(enterpriseInfoId, "")){
					enterpriseInfoId.append(ss);
				}else {
					enterpriseInfoId.append(",").append(ss);
				}
			}
			wxTweetArticle.setEnterpriseInfoId(String.valueOf(enterpriseInfoId));
		}
		wxTweetArticle.setTweetType(request.getTweetType());
		wxTweetArticle.setTemplateType(request.getTemplateType());
		Gson gson = new Gson();
		String listToJsonString = gson.toJson(request.getArticles());
		wxTweetArticle.setContent(listToJsonString);
		if(StringUtils.equals(wxTweetArticle.getTweetType(), "1")) {
			wxTweetArticle.setTitle(request.getArticles().get(0).getTitle());
		}
		//添加草稿箱
		String rs = "";
		if(StringUtils.equals(request.getTweetType(), "2")) {
			WxMpAddDraft wxMpAddDraft = new WxMpAddDraft();
			wxMpAddDraft.setArticles(request.getArticles());
			WxMpDraftService wxMpDraftService = wxService.getDraftService();
			rs = wxMpDraftService.addDraft(wxMpAddDraft);
			wxTweetArticle.setMediaId(rs);
		}
		wxTweetArticle.setStatus("0");
		wxTweetArticleService.insertWxTweetArticle(wxTweetArticle);
		if(StringUtils.equals(request.getTweetType(), "2")){
			return AjaxResult.success(rs);
		}else {
			return AjaxResult.success("保存成功！");
		}
	}

	/**
	 * 修改微信草稿箱
	 * @param request
	 * @return
	 */
	@ApiOperation(value = "修改微信草稿箱")
	@PutMapping
	@PreAuthorize("@ss.hasPermi('wxmp:wxdraft:edit')")
	public AjaxResult edit(@RequestBody WxDraftRequest request) throws Exception {
		String mediaId = request.getMediaId();
		if(StringUtils.equals(request.getTweetType(), "2")) {
			WxMpDraftService wxMpDraftService = wxService.getDraftService();
			WxMpUpdateDraft wxMpUpdateDraft = new WxMpUpdateDraft();
			wxMpUpdateDraft.setMediaId(mediaId);
			int index = 0;
			for (WxMpDraftArticles article : request.getArticles()) {
				wxMpUpdateDraft.setIndex(index);
				wxMpUpdateDraft.setArticles(article);
				wxMpDraftService.updateDraft(wxMpUpdateDraft);
				index++;
			}
		}
		WxTweetArticle wxTweetArticle = new WxTweetArticle();
		StringBuilder positionInfoId = new StringBuilder();
		StringBuilder enterpriseInfoId = new StringBuilder();
		if(StringUtils.isNotEmpty(request.getPositionInfoIds())){
			for(Long ss : request.getPositionInfoIds()){
				if(StringUtils.equals(positionInfoId, "")){
					positionInfoId.append(ss);
				}else {
					positionInfoId.append(",").append(ss);
				}
			}
			wxTweetArticle.setPositionInfoId(String.valueOf(positionInfoId));
		}
		if(StringUtils.isNotEmpty(request.getEnterpriseInfoIds())){
			for(Long ss : request.getEnterpriseInfoIds()){
				if(StringUtils.equals(enterpriseInfoId, "")){
					enterpriseInfoId.append(ss);
				}else {
					enterpriseInfoId.append(",").append(ss);
				}
			}
			wxTweetArticle.setEnterpriseInfoId(String.valueOf(enterpriseInfoId));
		}
		wxTweetArticle.setMediaId(mediaId);
		wxTweetArticle.setTweetType(request.getTweetType());
        wxTweetArticle.setPublicTemplateType(request.getPublicTemplateType());
		wxTweetArticle.setTemplateType(request.getTemplateType());
		Gson gson = new Gson();
		String listToJsonString = gson.toJson(request.getArticles());
		wxTweetArticle.setContent(listToJsonString);
		if(StringUtils.equals(request.getTweetType(), "1")) {
			wxTweetArticle.setTitle(request.getArticles().get(0).getTitle());
		}
		WxTweetArticle tweetArticle = wxTweetArticleService.selectWxTweetArticleByMediaId(mediaId);
		if(tweetArticle != null){
			wxTweetArticle.setId(tweetArticle.getId());
			wxTweetArticleService.updateWxTweetArticle(wxTweetArticle);
		}else {
			WxTweetArticle tweetArticleTwo = wxTweetArticleService.selectWxTweetArticleById(Long.valueOf(mediaId));
			if(tweetArticleTwo != null){
				wxTweetArticle.setId(tweetArticleTwo.getId());
				wxTweetArticleService.updateWxTweetArticle(wxTweetArticle);
			}else {
				wxTweetArticle.setStatus("0");
				wxTweetArticleService.insertWxTweetArticle(wxTweetArticle);
			}
		}
		return AjaxResult.success();
	}

	/**
	 * 通过id删除微信草稿箱
	 *
	 * @param
	 * @return R
	 */
	@ApiOperation(value = "通过id删除微信草稿箱")
	@DeleteMapping
	@PreAuthorize("@ss.hasPermi('wxmp:wxdraft:del')")
	public AjaxResult del(String id) throws Exception {

		WxTweetArticle tweetArticle = wxTweetArticleService.selectWxTweetArticleByMediaId(id);
		if(tweetArticle != null){
			try {
				WxMpDraftService wxMpDraftService = wxService.getDraftService();
				wxMpDraftService.delDraft(id);
			}catch (Exception e){
				log.error("微信草稿箱已无改信息，无需关注！");
			}
			wxTweetArticleService.deleteWxTweetArticleByMediaId(id);
		}else {
			wxTweetArticleService.deleteWxTweetArticleById(Long.valueOf(id));
		}
		return AjaxResult.success();
	}

	/**
	 * 分页查询
	 *
	 * @param page 分页对象
	 * @param
	 * @return
	 */
	@ApiOperation(value = "分页查询")
	@GetMapping("/page")
	@PreAuthorize("@ss.hasPermi('wxmp:wxdraft:index')")
	public AjaxResult getPage(Page page) throws Exception {
		int count = (int) page.getSize();
		int offset = (int) page.getCurrent() * count - count;
		WxMpDraftService wxMpDraftService = wxService.getDraftService();
		try {
			WxMpDraftList list = wxMpDraftService.listDraft(offset, count);
			list.getItems().forEach(s->{
				WxTweetArticle tweetArticle = wxTweetArticleService.selectWxTweetArticleByMediaId(s.getMediaId());
				if(tweetArticle == null){
					WxTweetArticle wxTweetArticle = new WxTweetArticle();
					wxTweetArticle.setMediaId(s.getMediaId());
					Gson gson = new Gson();
					String listToJsonString = gson.toJson(s.getContent().getNewsItem());
					wxTweetArticle.setContent(listToJsonString);
					wxTweetArticle.setStatus("0");
					wxTweetArticle.setTweetType("2");
					wxTweetArticleService.insertWxTweetArticle(wxTweetArticle);
				}else {
					Gson gson = new Gson();
					String listToJsonString = gson.toJson(s.getContent().getNewsItem());
					tweetArticle.setContent(listToJsonString);
					wxTweetArticleService.updateWxTweetArticle(tweetArticle);
				}
			});
		} catch (WxErrorException ex) {
			log.error("获取微信公众号信息失败！");
		}
		startPageTwo(offset, count);
		List<WxTweetArticle> lists = wxTweetArticleService.selectWxTweetArticleList(new WxTweetArticle());
		WxMpDraftResponse draftResponse = new WxMpDraftResponse();
		draftResponse.setItemCount(lists.size());
		draftResponse.setTotalCount(lists.size());
		List<WxMpDraftItemResponse> items = new ArrayList<>();
		lists.forEach(e->{
			WxMpDraftItemResponse mpDraftItem = new WxMpDraftItemResponse();
			if(e.getMediaId() != null && !StringUtils.equals(e.getMediaId(), "")) {
				mpDraftItem.setMediaId(e.getMediaId());
			}else {
				mpDraftItem.setMediaId(String.valueOf(e.getId()));
			}
			List<WxMpDraftArticles> newsItem = JSONArray.parseArray(e.getContent()).toList(WxMpDraftArticles.class);
			if(StringUtils.equals(e.getTweetType(), "1")){
				newsItem.forEach(a->{
					a.setTitle(e.getTitle());
				});
			}
			WxMpDraftInfoResponse content = new WxMpDraftInfoResponse();
			content.setNewsItem(newsItem);
			mpDraftItem.setContent(content);
			mpDraftItem.setUpdateTime(null);
			mpDraftItem.setStatus(e.getStatus());
			mpDraftItem.setTweetType(e.getTweetType());
			mpDraftItem.setTemplateType(e.getTemplateType());
			mpDraftItem.setPositionInfoId(e.getPositionInfoId());
			if(e.getPositionInfoId() != null && !StringUtils.equals(e.getPositionInfoId(), "")) {
				Long[] positionInfoIds = (Long[]) ConvertUtils.convert(e.getPositionInfoId().split(","), Long.class);
				mpDraftItem.setPositionInfoIds(positionInfoIds);
			}
			mpDraftItem.setEnterpriseInfoId(e.getEnterpriseInfoId());
			if(e.getEnterpriseInfoId() != null && !StringUtils.equals(e.getEnterpriseInfoId(), "")) {
				Long[] enterpriseInfoIds = (Long[]) ConvertUtils.convert(e.getEnterpriseInfoId().split(","), Long.class);
				mpDraftItem.setEnterpriseInfoIds(enterpriseInfoIds);
			}
			items.add(mpDraftItem);
		});
		draftResponse.setItems(items);
		return AjaxResult.success(draftResponse);
	}

	/**
	 * 发布草稿箱
	 * @param id
	 * @return
	 */
	@ApiOperation(value = "发布草稿箱")
	@PostMapping("/publish/{id}")
	@PreAuthorize("@ss.hasPermi('wxmp:wxdraft:publish')")
	public AjaxResult publish(@PathVariable String id) throws Exception {
		WxMpFreePublishService wxMpFreePublishService = wxService.getFreePublishService();
		wxMpFreePublishService.submit(id);
		WxTweetArticle tweetArticle = wxTweetArticleService.selectWxTweetArticleByMediaId(id);
		if(tweetArticle != null) {
			tweetArticle.setStatus("1");
			wxTweetArticleService.updateWxTweetArticle(tweetArticle);
		}
		return AjaxResult.success();
	}
}
