package com.recruit.core.domain.request.position;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @Auther: <PERSON> kong
 * @Date: 2023/3/26 15:48
 * @Description:
 */
@Data
@ApiModel("职位信息2")
public class PostPositionInfoRequest {

    @ApiModelProperty("主键id")
    private Long id;

    @NotBlank(message = "职位名称不能为空")
    @ApiModelProperty("职位编码")
    private String positionCode;

    @NotNull(message = "工作地址不能为空")
    @ApiModelProperty("工作地址id")
    private Long workAddressId;

    @NotBlank(message = "工作经验不能为空")
    @ApiModelProperty("工作经验")
    private String workExperience;

    @NotBlank(message = "最低学历不能为空")
    @ApiModelProperty("最低学历")
    private String minimumEducation;

    @ApiModelProperty("最高薪资")
    private String maximumSalary;

    @ApiModelProperty("最低薪资")
    private String minimumWage;

    @ApiModelProperty("招聘人数")
    private String recruitingNumbers;

    @ApiModelProperty("年龄要求")
    private String ageRequirements;

    @ApiModelProperty("最低年龄要求")
    private String ageMinimum;

    @ApiModelProperty("性别要求")
    private String genderRequirements;

    @NotBlank(message = "招聘类型不能为空")
    @ApiModelProperty("招聘类型")
    private String tecruitmentType;

    @NotBlank(message = "招聘描述不能为空")
    @ApiModelProperty("招聘描述")
    private String jobDescription;

    @ApiModelProperty("职位状态")
    private String positionStatus;

    @ApiModelProperty("福利")
    private String materialBenefits;
}
