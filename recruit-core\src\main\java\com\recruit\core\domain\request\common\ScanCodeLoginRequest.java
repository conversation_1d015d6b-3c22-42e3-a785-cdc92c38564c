package com.recruit.core.domain.request.common;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Auther: <PERSON> kong
 * @Date: 2023/4/26 21:44
 * @Description:
 */
@Data
@ApiModel("验证码2")
public class ScanCodeLoginRequest {

    @ApiModelProperty("openid")
    private String openid;

    @ApiModelProperty("手机号")
    private String phone;

    @ApiModelProperty("webSocketId")
    private String webSocketId;


}
