package com.recruit.core.domain.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Auther: <PERSON> kong
 * @Date: 2023/5/11 0:07
 * @Description:
 */
@Data
@ApiModel("人才引进返回2")
public class TalentIntroductionResponse {

    @ApiModelProperty("标题")
    private String title;

    @ApiModelProperty("招聘公告")
    private String contents;

    @ApiModelProperty("咨询信息")
    private String consultationInfo;

    @ApiModelProperty("小程序图片地址")
    private String appPicTwoUrl;

    @ApiModelProperty("pc图片地址")
    private String pcPicTwoUrl;

    @ApiModelProperty("报名须知")
    private String recruitmentNotice;

    @ApiModelProperty("备注")
    private String remark;

}
