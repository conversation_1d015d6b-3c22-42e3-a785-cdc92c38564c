package com.recruit.core.domain.request.resume;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @Auther: Wu kong
 * @Date: 2023/3/21 12:35
 * @Description:
 */
@Data
@ApiModel("附件简历1")
public class AttachmentResumeRequest {


    @NotBlank(message = "附件简历名称不能为空")
    @ApiModelProperty("附件简历名称")
    private String resumeName;

    @NotBlank(message = "附件简历url不能为空")
    @ApiModelProperty("附件简历url")
    private String attachmentResumeUrl;
}
