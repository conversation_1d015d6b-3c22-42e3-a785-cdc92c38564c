package com.recruit.core.domain.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.recruit.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @Auther: Wu kong
 * @Date: 2023/4/12 17:29
 * @Description:
 */
@Data
@ApiModel("查询收藏职位1")
public class CollectionPositionResponse {

    private Long id;


    @Excel(name = "用户id")
    @ApiModelProperty("用户id")
    private Long userId;

    @Excel(name = "职位id")
    @ApiModelProperty("职位id")
    private Long positionInfoId;


    @Excel(name = "求职者收藏职位1，招聘者求职者2, 求职者收藏企业3")
    @ApiModelProperty("求职者收藏职位1，招聘者求职者2, 求职者收藏企业3")
    private String collectType;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @ApiModelProperty("用户名称")
    private String userName;

    @ApiModelProperty("头像")
    private String headSculpture;

    @ApiModelProperty("职位编码")
    private String positionCode;

    @ApiModelProperty("职位编码名称")
    private String positionName;

    @ApiModelProperty("公司名称")
    private String enterpriseName;

    @ApiModelProperty("工作经验")
    private String workExperience;

    @ApiModelProperty("工作经验名称")
    private String workExperienceName;

    @ApiModelProperty("最低学历")
    private String minimumEducation;

    @ApiModelProperty("最低学历名称")
    private String minimumEducationName;

    @ApiModelProperty("最高薪资")
    private String maximumSalary;

    @ApiModelProperty("最低薪资")
    private String minimumWage;

    @ApiModelProperty("公司福利")
    private String materialBenefits;

    @ApiModelProperty("职位")
    private String position;
}
