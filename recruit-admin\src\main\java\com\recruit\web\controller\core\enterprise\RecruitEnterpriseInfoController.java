package com.recruit.web.controller.core.enterprise;

import java.util.*;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import com.recruit.common.notify.NotifyService;
import com.recruit.common.utils.StringUtils;
import com.recruit.common.utils.bean.BeanUtils;
import com.recruit.core.domain.RecruitEnterpriseBusinessInfo;
import com.recruit.core.domain.RecruitEnterpriseUsersRel;
import com.recruit.core.domain.SysSmsSendingRecord;
import com.recruit.core.domain.SysSmsTemplate;
import com.recruit.core.service.IRecruitEnterpriseBusinessInfoService;
import com.recruit.core.service.IRecruitEnterpriseUsersRelService;
import com.recruit.core.service.ISysSmsSendingRecordService;
import com.recruit.core.service.ISysSmsTemplateService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.recruit.common.annotation.Log;
import com.recruit.common.core.controller.BaseController;
import com.recruit.common.core.domain.AjaxResult;
import com.recruit.common.enums.BusinessType;
import com.recruit.core.domain.RecruitEnterpriseInfo;
import com.recruit.core.service.IRecruitEnterpriseInfoService;
import com.recruit.common.utils.poi.ExcelUtil;
import com.recruit.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 企业简介Controller
 *
 * <AUTHOR>
 * @date 2023-03-21
 */
@RestController
@RequestMapping("/core/EnterpriseInfo")
public class RecruitEnterpriseInfoController extends BaseController
{
    @Resource
    private NotifyService notifyService;

    @Autowired
    private ISysSmsTemplateService sysSmsTemplateService;

    @Autowired
    private ISysSmsSendingRecordService sysSmsSendingRecordService;

    @Autowired
    private IRecruitEnterpriseInfoService recruitEnterpriseInfoService;

    @Autowired
    private IRecruitEnterpriseBusinessInfoService recruitEnterpriseBusinessInfoService;

    @Autowired
    private IRecruitEnterpriseUsersRelService recruitEnterpriseUsersRelService;

    /**
     * 查询企业简介列表
     */
    @PreAuthorize("@ss.hasPermi('core:EnterpriseInfo:list')")
    @GetMapping("/list")
    public TableDataInfo list(RecruitEnterpriseInfo recruitEnterpriseInfo)
    {
        startPage();
        if(StringUtils.equals(recruitEnterpriseInfo.getStatus(), "ALL")){
            recruitEnterpriseInfo.setStatus(null);
        }
        List<RecruitEnterpriseInfo> list = recruitEnterpriseInfoService.selectRecruitEnterpriseInfoList(recruitEnterpriseInfo);
        return getDataTable(list);
    }

    /**
     * 查询审核通过的企业
     * @param recruitEnterpriseInfo
     * @return
     */
    @GetMapping("/getEnterpriseInfoList")
    public TableDataInfo getEnterpriseInfoList(RecruitEnterpriseInfo recruitEnterpriseInfo)
    {
        recruitEnterpriseInfo.setStatus("1");
        List<RecruitEnterpriseInfo> list = recruitEnterpriseInfoService.selectRecruitEnterpriseInfoList(recruitEnterpriseInfo);
        return getDataTable(list);
    }
    @PostMapping("/getEnterpriseInfoListTwo")
    public TableDataInfo getEnterpriseInfoListTwo(@RequestBody RecruitEnterpriseInfo recruitEnterpriseInfo)
    {
        recruitEnterpriseInfo.setStatus("1");
        List<RecruitEnterpriseInfo> list = recruitEnterpriseInfoService.selectRecruitEnterpriseInfoList(recruitEnterpriseInfo);
        return getDataTable(list);
    }

    /**
     * 导出企业简介列表
     */
    @PreAuthorize("@ss.hasPermi('core:EnterpriseInfo:export')")
    @Log(title = "企业简介", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RecruitEnterpriseInfo recruitEnterpriseInfo)
    {
        List<RecruitEnterpriseInfo> list = recruitEnterpriseInfoService.selectRecruitEnterpriseInfoList(recruitEnterpriseInfo);
        ExcelUtil<RecruitEnterpriseInfo> util = new ExcelUtil<RecruitEnterpriseInfo>(RecruitEnterpriseInfo.class);
        util.exportExcel(response, list, "企业简介数据");
    }

    /**
     * 获取企业简介详细信息
     */
    @PreAuthorize("@ss.hasPermi('core:EnterpriseInfo:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        RecruitEnterpriseInfo enterpriseInfo = recruitEnterpriseInfoService.selectRecruitEnterpriseInfoById(id);
        if(enterpriseInfo.getRegion() != null && !StringUtils.equals(enterpriseInfo.getRegion(), "")){
            List<String> regions = new ArrayList<>(Arrays.asList(enterpriseInfo.getRegion().split(",")));
            enterpriseInfo.setRegions(regions);
        }
        return success(enterpriseInfo);
    }

    /**
     * 新增企业简介
     */
    @PreAuthorize("@ss.hasPermi('core:EnterpriseInfo:add')")
    @Log(title = "企业简介", businessType = BusinessType.INSERT)
    @PostMapping
    @Transactional
    public AjaxResult add(@RequestBody RecruitEnterpriseInfo recruitEnterpriseInfo)
    {
        RecruitEnterpriseInfo enterpriseInfo = recruitEnterpriseInfoService.getEnterpriseInfo(recruitEnterpriseInfo.getEnterpriseName());
        if(enterpriseInfo == null) {
            if (recruitEnterpriseInfo.getIndustrys() != null) {
                if (recruitEnterpriseInfo.getIndustrys().size() == 1) {
                    recruitEnterpriseInfo.setIndustry(recruitEnterpriseInfo.getIndustrys().get(0));
                } else if (recruitEnterpriseInfo.getIndustrys().size() == 2) {
                    recruitEnterpriseInfo.setIndustry(recruitEnterpriseInfo.getIndustrys().get(1));
                } else if (recruitEnterpriseInfo.getIndustrys().size() == 3) {
                    recruitEnterpriseInfo.setIndustry(recruitEnterpriseInfo.getIndustrys().get(2));
                }
            }
            if (recruitEnterpriseInfo.getRegions() != null) {
                StringBuilder region = new StringBuilder();
                recruitEnterpriseInfo.getRegions().forEach(e->{
                    if(StringUtils.equals(region, "")){
                        region.append(e);
                    }else {
                        region.append(",").append(e);
                    }
                });
                recruitEnterpriseInfo.setRegion(String.valueOf(region));
            }
            recruitEnterpriseInfo.setRemark("后台添加");
            int ss = recruitEnterpriseInfoService.insertRecruitEnterpriseInfo(recruitEnterpriseInfo);
            if (ss > 0) {
                RecruitEnterpriseBusinessInfo enterpriseBusinessInfo = recruitEnterpriseBusinessInfoService.selectEnterpriseBusinessInfoByEnterpriseId(recruitEnterpriseInfo.getEnterpriseId());
                if (enterpriseBusinessInfo != null) {
                    RecruitEnterpriseBusinessInfo businessInfo = new RecruitEnterpriseBusinessInfo();
                    businessInfo.setId(recruitEnterpriseInfo.getBusinessInfoId());
                    businessInfo.setStatus(recruitEnterpriseInfo.getStatus());
                    recruitEnterpriseBusinessInfoService.updateRecruitEnterpriseBusinessInfo(businessInfo);
                } else {
                    RecruitEnterpriseBusinessInfo businessInfo = new RecruitEnterpriseBusinessInfo();
                    BeanUtils.copyBeanProp(businessInfo, recruitEnterpriseInfo);
                    businessInfo.setEnterpriseId(recruitEnterpriseInfo.getId());
                    recruitEnterpriseBusinessInfoService.insertRecruitEnterpriseBusinessInfo(businessInfo);
                }
            }
            return toAjax(ss);
        }else {
            return AjaxResult.error("企业已存在，请勿查验后添加！");
        }
    }

    /**
     * 修改企业简介
     */
    @PreAuthorize("@ss.hasPermi('core:EnterpriseInfo:edit')")
    @Log(title = "企业简介", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RecruitEnterpriseInfo recruitEnterpriseInfo)
    {
        RecruitEnterpriseInfo enterpriseInfo = recruitEnterpriseInfoService.getEnterpriseInfo(recruitEnterpriseInfo.getEnterpriseName());
        boolean ssss = false;
        if(enterpriseInfo == null){
            ssss = true;
        }else {
            if(enterpriseInfo.getId().equals(recruitEnterpriseInfo.getId())){
                ssss = true;
            }else {
                ssss = false;
            }
        }
        if(ssss) {
            if (recruitEnterpriseInfo.getIndustrys() != null) {
                if (recruitEnterpriseInfo.getIndustrys().size() == 1) {
                    recruitEnterpriseInfo.setIndustry(recruitEnterpriseInfo.getIndustrys().get(0));
                } else if (recruitEnterpriseInfo.getIndustrys().size() == 2) {
                    recruitEnterpriseInfo.setIndustry(recruitEnterpriseInfo.getIndustrys().get(1));
                } else if (recruitEnterpriseInfo.getIndustrys().size() == 3) {
                    recruitEnterpriseInfo.setIndustry(recruitEnterpriseInfo.getIndustrys().get(2));
                }
            }
            if (recruitEnterpriseInfo.getRegions() != null) {
                StringBuilder region = new StringBuilder();
                recruitEnterpriseInfo.getRegions().forEach(e->{
                    if(StringUtils.equals(region, "")){
                        region.append(e);
                    }else {
                        region.append(",").append(e);
                    }
                });
                recruitEnterpriseInfo.setRegion(String.valueOf(region));
            }

            RecruitEnterpriseBusinessInfo enterpriseBusinessInfo = new RecruitEnterpriseBusinessInfo();
            BeanUtils.copyBeanProp(enterpriseBusinessInfo, recruitEnterpriseInfo);
            enterpriseBusinessInfo.setId(recruitEnterpriseInfo.getBusinessInfoId());
            enterpriseBusinessInfo.setStatus(recruitEnterpriseInfo.getStatus());
            int ss = recruitEnterpriseBusinessInfoService.updateRecruitEnterpriseBusinessInfo(enterpriseBusinessInfo);
            if (ss > 0) {
                if (StringUtils.equals(recruitEnterpriseInfo.getStatus(), "1")) {
                    RecruitEnterpriseUsersRel enterpriseUsersRel = new RecruitEnterpriseUsersRel();
                    enterpriseUsersRel.setEnterpriseId(recruitEnterpriseInfo.getEnterpriseId());
                    enterpriseUsersRel.setUserId(recruitEnterpriseInfo.getUserId());
                    List<RecruitEnterpriseUsersRel> list = recruitEnterpriseUsersRelService.selectRecruitEnterpriseUsersRelList(enterpriseUsersRel);
                    list.forEach(e -> {
                        e.setBindingStatus("2");
                        int aa = recruitEnterpriseUsersRelService.updateRecruitEnterpriseUsersRel(e);
                        if (aa > 0) {
                            sendAuthenticationMessage(e.getPhone(), e.getUserId());
                        }
                    });
                }
            } else {
                RecruitEnterpriseBusinessInfo enterpriseBusinessInfos = new RecruitEnterpriseBusinessInfo();
                BeanUtils.copyBeanProp(enterpriseBusinessInfos, recruitEnterpriseInfo);
                enterpriseBusinessInfos.setEnterpriseId(recruitEnterpriseInfo.getId());
                recruitEnterpriseBusinessInfoService.insertRecruitEnterpriseBusinessInfo(enterpriseBusinessInfos);
            }
            return toAjax(recruitEnterpriseInfoService.updateRecruitEnterpriseInfo(recruitEnterpriseInfo));
        }else {
            return AjaxResult.error("企业名称重复，请勿查验后添加！");
        }
    }

    /**
     * 推送认证消息
     */
    private void sendAuthenticationMessage(String phone, Long userId){
        SysSmsTemplate smsTemplate = sysSmsTemplateService.selectSmsTemplateBySendType("4");
        if(smsTemplate != null){
            notifyService.notifySms(phone, smsTemplate.getTemplateContent(), String.valueOf(smsTemplate.getTemplateId()));

            //记录 企业认证消息 记录
            SysSmsSendingRecord smsSendingRecord = new SysSmsSendingRecord();
            smsSendingRecord.setUserId(userId);
            smsSendingRecord.setPhone(phone);
            smsSendingRecord.setNoticeTitle(smsTemplate.getTemplateName());
            smsSendingRecord.setNoticeType("1");
            smsSendingRecord.setNoticeContent(smsTemplate.getTemplateContent());
            smsSendingRecord.setSentType("1");
            smsSendingRecord.setSmsTemplateId(smsTemplate.getId());
            smsSendingRecord.setSendTime(new Date());
            sysSmsSendingRecordService.insertSysSmsSendingRecord(smsSendingRecord);
        }
    }

    /**
     * 删除企业简介
     */
    @PreAuthorize("@ss.hasPermi('core:EnterpriseInfo:remove')")
    @Log(title = "企业简介", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        int ss = recruitEnterpriseInfoService.deleteRecruitEnterpriseInfoByIds(ids);
        if(ss > 0) {
            //删除企业认证信息
            recruitEnterpriseBusinessInfoService.deleteEnterpriseBusinessInfoByEnterpriseId(ids);
            //删除用户企业关联信息
            recruitEnterpriseUsersRelService.deleteUsersRelByEnterpriseId(ids);
        }
        return toAjax(ss);
    }

    /**
     * 查询企业信息
     * @return
     */
    @GetMapping("/queryEnterpriseInfo")
    public TableDataInfo queryEnterpriseInfo(RecruitEnterpriseInfo recruitEnterpriseInfo)
    {
        List<RecruitEnterpriseInfo> lists = recruitEnterpriseInfoService.selectRecruitEnterpriseInfoListTwo(recruitEnterpriseInfo);

        lists = lists.stream().collect(
                Collectors.collectingAndThen(Collectors.toCollection(
                        () -> new TreeSet<>(Comparator.comparing(RecruitEnterpriseInfo::getId))), ArrayList::new));
        return getDataTable(lists);
    }

    /**
     * 根据类型筛选企业
     * @return
     */
    @GetMapping("/getEnterpriseDidNotAddInfo")
    public TableDataInfo getEnterpriseDidNotAddInfo(String type)
    {
        List<RecruitEnterpriseInfo> lists = recruitEnterpriseInfoService.getEnterpriseDidNotAddInfo(type);
        return getDataTable(lists);
    }

    /**
     * 筛选蓝V企业
     * @return
     */
    @GetMapping("/getScreeningBlueVEnterprises")
    public TableDataInfo getScreeningBlueVEnterprises()
    {
        List<RecruitEnterpriseInfo> lists = recruitEnterpriseInfoService.getScreeningBlueVEnterprises();
        return getDataTable(lists);
    }


    /**
     * 导出职位信息模板
     */
    @Log(title = "导出公司信息模板", businessType = BusinessType.EXPORT)
    @GetMapping("/importTemplate")
    public AjaxResult importTemplate()
    {
        ExcelUtil<RecruitEnterpriseInfo> util = new ExcelUtil<>(RecruitEnterpriseInfo.class);
        return util.importTemplateExcel("公司信息模板");
    }


    /**
     * 导入职位信息
     * @param file
     * @param updateSupport
     * @return
     * @throws Exception
     */
    @Log(title = "导入公司信息", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<RecruitEnterpriseInfo> util = new ExcelUtil<>(RecruitEnterpriseInfo.class);
        List<RecruitEnterpriseInfo> lists = util.importExcel(file.getInputStream());
        return success(recruitEnterpriseInfoService.importData(lists));
    }
}
