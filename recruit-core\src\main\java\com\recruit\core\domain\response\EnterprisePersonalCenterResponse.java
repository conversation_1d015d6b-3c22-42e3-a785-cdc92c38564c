package com.recruit.core.domain.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Auther: <PERSON> kong
 * @Date: 2023/4/18 14:43
 * @Description:
 */
@Data
@ApiModel("企业个人中心1")
public class EnterprisePersonalCenterResponse {

    @ApiModelProperty("企业收藏用户数量")
    private Integer favoriteUsersNum;

    @ApiModelProperty("企业被看过的个人信息记录数")
    private Integer userBrowsingInfoNum;

    @ApiModelProperty("查看企业收到简历数")
    private Integer enterpriseReceivingNum;

    @ApiModelProperty("查看沟通数量")
    private Integer communicateNum;

    @ApiModelProperty("查看邀请面试数量")
    private Integer interviewNum;
}
