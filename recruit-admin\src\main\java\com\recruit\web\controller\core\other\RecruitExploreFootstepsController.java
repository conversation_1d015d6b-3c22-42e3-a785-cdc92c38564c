package com.recruit.web.controller.core.other;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.recruit.common.annotation.Log;
import com.recruit.common.core.controller.BaseController;
import com.recruit.common.core.domain.AjaxResult;
import com.recruit.common.enums.BusinessType;
import com.recruit.core.domain.RecruitExploreFootsteps;
import com.recruit.core.service.IRecruitExploreFootstepsService;
import com.recruit.common.utils.poi.ExcelUtil;
import com.recruit.common.core.page.TableDataInfo;

/**
 * 浏览足迹Controller
 *
 * <AUTHOR>
 * @date 2023-04-12
 */
@RestController
@RequestMapping("/core/exploreFootsteps")
public class RecruitExploreFootstepsController extends BaseController
{
    @Autowired
    private IRecruitExploreFootstepsService recruitExploreFootstepsService;

    /**
     * 查询浏览足迹列表
     */
    @PreAuthorize("@ss.hasPermi('core:exploreFootsteps:list')")
    @GetMapping("/list")
    public TableDataInfo list(RecruitExploreFootsteps recruitExploreFootsteps)
    {
        startPage();
        List<RecruitExploreFootsteps> list = recruitExploreFootstepsService.selectRecruitExploreFootstepsList(recruitExploreFootsteps);
        return getDataTable(list);
    }

    /**
     * 导出浏览足迹列表
     */
    @PreAuthorize("@ss.hasPermi('core:exploreFootsteps:export')")
    @Log(title = "浏览足迹", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RecruitExploreFootsteps recruitExploreFootsteps)
    {
        List<RecruitExploreFootsteps> list = recruitExploreFootstepsService.selectRecruitExploreFootstepsList(recruitExploreFootsteps);
        ExcelUtil<RecruitExploreFootsteps> util = new ExcelUtil<RecruitExploreFootsteps>(RecruitExploreFootsteps.class);
        util.exportExcel(response, list, "浏览足迹数据");
    }

    /**
     * 获取浏览足迹详细信息
     */
    @PreAuthorize("@ss.hasPermi('core:exploreFootsteps:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(recruitExploreFootstepsService.selectRecruitExploreFootstepsById(id));
    }

    /**
     * 新增浏览足迹
     */
    @PreAuthorize("@ss.hasPermi('core:exploreFootsteps:add')")
    @Log(title = "浏览足迹", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RecruitExploreFootsteps recruitExploreFootsteps)
    {
        return toAjax(recruitExploreFootstepsService.insertRecruitExploreFootsteps(recruitExploreFootsteps));
    }

    /**
     * 修改浏览足迹
     */
    @PreAuthorize("@ss.hasPermi('core:exploreFootsteps:edit')")
    @Log(title = "浏览足迹", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RecruitExploreFootsteps recruitExploreFootsteps)
    {
        return toAjax(recruitExploreFootstepsService.updateRecruitExploreFootsteps(recruitExploreFootsteps));
    }

    /**
     * 删除浏览足迹
     */
    @PreAuthorize("@ss.hasPermi('core:exploreFootsteps:remove')")
    @Log(title = "浏览足迹", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(recruitExploreFootstepsService.deleteRecruitExploreFootstepsByIds(ids));
    }
}
