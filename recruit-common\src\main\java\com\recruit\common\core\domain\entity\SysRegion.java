package com.recruit.common.core.domain.entity;

import com.recruit.common.annotation.Excel;
import com.recruit.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 系统区域对象 sys_region
 *
 * <AUTHOR>
 * @date 2023-06-13
 */
@Data
@ApiModel("系统区域")
public class SysRegion extends BaseEntity
{
    private static final long serialVersionUID = 1L;


    private Long id;


    @Excel(name = "名称")
    @ApiModelProperty("名称")
    private String name;


    @Excel(name = "父ID")
    @ApiModelProperty("父ID")
    private Long parentId;

    @ApiModelProperty("祖级列表")
    private String ancestors;


    @Excel(name = "等级")
    private Integer grade;

    /** 子部门 */
    private List<SysRegion> children = new ArrayList<SysRegion>();
}
