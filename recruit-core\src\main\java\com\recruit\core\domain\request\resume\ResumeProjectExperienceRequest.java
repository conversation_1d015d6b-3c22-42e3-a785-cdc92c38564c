package com.recruit.core.domain.request.resume;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.recruit.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * @Auther: Wu kong
 * @Date: 2023/3/21 10:24
 * @Description:
 */
@Data
@ApiModel("项目经历1")
public class ResumeProjectExperienceRequest {

    @ApiModelProperty("id")
    private Long id;

    @NotBlank(message = "项目名称不能为空")
    @ApiModelProperty("项目名称")
    private String projectName;

    @NotNull(message = "项目开始时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("项目开始时间")
    private Date startTime;

    @NotNull(message = "项目结束时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("项目结束时间")
    private Date endTime;

    @NotBlank(message = "项目描述不能为空")
    @ApiModelProperty("项目描述")
    private String projectDescription;

}
