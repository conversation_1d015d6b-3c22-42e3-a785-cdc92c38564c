package com.recruit.web.controller.core.company;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.recruit.common.annotation.Log;
import com.recruit.common.core.controller.BaseController;
import com.recruit.common.core.domain.AjaxResult;
import com.recruit.common.enums.BusinessType;
import com.recruit.core.domain.CompanyDynamics;
import com.recruit.core.service.ICompanyDynamicsService;
import com.recruit.common.utils.poi.ExcelUtil;
import com.recruit.common.core.page.TableDataInfo;

/**
 * 公司动态Controller
 *
 * <AUTHOR>
 * @date 2023-04-11
 */
@RestController
@RequestMapping("/core/companyDynamics")
public class CompanyDynamicsController extends BaseController
{
    @Autowired
    private ICompanyDynamicsService companyDynamicsService;

    /**
     * 查询公司动态列表
     */
    @PreAuthorize("@ss.hasPermi('core:companyDynamics:list')")
    @GetMapping("/list")
    public TableDataInfo list(CompanyDynamics companyDynamics)
    {
        startPage();
        List<CompanyDynamics> list = companyDynamicsService.selectCompanyDynamicsList(companyDynamics);
        return getDataTable(list);
    }

    /**
     * 导出公司动态列表
     */
    @PreAuthorize("@ss.hasPermi('core:companyDynamics:export')")
    @Log(title = "公司动态", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CompanyDynamics companyDynamics)
    {
        List<CompanyDynamics> list = companyDynamicsService.selectCompanyDynamicsList(companyDynamics);
        ExcelUtil<CompanyDynamics> util = new ExcelUtil<CompanyDynamics>(CompanyDynamics.class);
        util.exportExcel(response, list, "公司动态数据");
    }

    /**
     * 获取公司动态详细信息
     */
    @PreAuthorize("@ss.hasPermi('core:companyDynamics:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(companyDynamicsService.selectCompanyDynamicsById(id));
    }

    /**
     * 新增公司动态
     */
    @PreAuthorize("@ss.hasPermi('core:companyDynamics:add')")
    @Log(title = "公司动态", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CompanyDynamics companyDynamics)
    {
        return toAjax(companyDynamicsService.insertCompanyDynamics(companyDynamics));
    }

    /**
     * 修改公司动态
     */
    @PreAuthorize("@ss.hasPermi('core:companyDynamics:edit')")
    @Log(title = "公司动态", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CompanyDynamics companyDynamics)
    {
        return toAjax(companyDynamicsService.updateCompanyDynamics(companyDynamics));
    }

    /**
     * 删除公司动态
     */
    @PreAuthorize("@ss.hasPermi('core:companyDynamics:remove')")
    @Log(title = "公司动态", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(companyDynamicsService.deleteCompanyDynamicsByIds(ids));
    }
}
