package com.recruit.web.controller.core.live;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.recruit.common.annotation.Log;
import com.recruit.common.core.controller.BaseController;
import com.recruit.common.core.domain.AjaxResult;
import com.recruit.common.enums.BusinessType;
import com.recruit.core.domain.SysLiveAdHoc;
import com.recruit.core.service.ISysLiveAdHocService;
import com.recruit.common.utils.poi.ExcelUtil;
import com.recruit.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 系统直播Controller
 *
 * <AUTHOR>
 * @date 2023-05-16
 */
@RestController
@RequestMapping("/core/liveAdHoc")
public class SysLiveAdHocController extends BaseController
{
    @Autowired
    private ISysLiveAdHocService sysLiveAdHocService;

    /**
     * 查询系统直播列表
     */
    @PreAuthorize("@ss.hasPermi('core:liveAdHoc:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysLiveAdHoc sysLiveAdHoc)
    {
        startPage();
        List<SysLiveAdHoc> list = sysLiveAdHocService.selectSysLiveAdHocList(sysLiveAdHoc);
        return getDataTable(list);
    }

    /**
     * 导出系统直播列表
     */
    @PreAuthorize("@ss.hasPermi('core:liveAdHoc:export')")
    @Log(title = "系统直播", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysLiveAdHoc sysLiveAdHoc)
    {
        List<SysLiveAdHoc> list = sysLiveAdHocService.selectSysLiveAdHocList(sysLiveAdHoc);
        ExcelUtil<SysLiveAdHoc> util = new ExcelUtil<SysLiveAdHoc>(SysLiveAdHoc.class);
        util.exportExcel(response, list, "系统直播数据");
    }

    /**
     * 获取系统直播详细信息
     */
    @PreAuthorize("@ss.hasPermi('core:liveAdHoc:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(sysLiveAdHocService.selectSysLiveAdHocById(id));
    }

    /**
     * 新增系统直播
     */
    @PreAuthorize("@ss.hasPermi('core:liveAdHoc:add')")
    @Log(title = "系统直播", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SysLiveAdHoc sysLiveAdHoc)
    {
        List<SysLiveAdHoc> list = sysLiveAdHocService.selectSysLiveAdHocList(new SysLiveAdHoc(){{
            setState("1");
        }});
        list.forEach(e->{
            e.setState("2");
            sysLiveAdHocService.updateSysLiveAdHoc(e);
        });
        return toAjax(sysLiveAdHocService.insertSysLiveAdHoc(sysLiveAdHoc));
    }

    /**
     * 修改系统直播
     */
    @PreAuthorize("@ss.hasPermi('core:liveAdHoc:edit')")
    @Log(title = "系统直播", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SysLiveAdHoc sysLiveAdHoc)
    {
        List<SysLiveAdHoc> list = sysLiveAdHocService.selectSysLiveAdHocList(new SysLiveAdHoc(){{
            setState("1");
        }});
        list.forEach(e->{
            e.setState("2");
            sysLiveAdHocService.updateSysLiveAdHoc(e);
        });
        return toAjax(sysLiveAdHocService.updateSysLiveAdHoc(sysLiveAdHoc));
    }

    /**
     * 删除系统直播
     */
    @PreAuthorize("@ss.hasPermi('core:liveAdHoc:remove')")
    @Log(title = "系统直播", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(sysLiveAdHocService.deleteSysLiveAdHocByIds(ids));
    }


    /**
     * 导入系统直播
     * @param file
     * @param updateSupport
     * @return
     * @throws Exception
     */
    @PreAuthorize("@ss.hasPermi('core:liveAdHoc:import')")
    @Log(title = "系统直播", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<SysLiveAdHoc> util = new ExcelUtil<>(SysLiveAdHoc.class);
        List<SysLiveAdHoc> lists = util.importExcel(file.getInputStream());
        String message = sysLiveAdHocService.importSysLiveAdHoc(lists, updateSupport);
        return AjaxResult.success(message);
    }


}
