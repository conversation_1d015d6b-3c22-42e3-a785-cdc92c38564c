package com.recruit.core.domain;

import com.recruit.common.annotation.Excel;
import com.recruit.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 职位信息对象 recruit_position_info
 *
 * <AUTHOR>
 * @date 2023-03-26
 */
@Data
@ApiModel("职位信息")
public class RecruitPositionInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    private Long[] positionInfoId;

    @ApiModelProperty("用户id")
    private Long userId;

    /** 企业id */

    @ApiModelProperty("企业id")
    private Long enterpriseId;

    @Excel(name = "企业名称", sort = 1)
    @ApiModelProperty("企业名称")
    private String enterpriseName;

    @Excel(name = "公司简介", sort = 2)
    @ApiModelProperty("公司简介")
    private String profile;

    @ApiModelProperty("企业logo")
    private String enterpriseLogo;

    /** 发布人id，即用户id */
    @ApiModelProperty("发布人id，即用户id")
    private Long publisherId;

    @Excel(name = "发布人名称", sort = 11)
    @ApiModelProperty("发布人名称")
    private String publisherName;

    @Excel(name = "手机号", sort = 12)
    @ApiModelProperty("手机号")
    private String phone;

    /** 职位名称 */
    @ApiModelProperty("职位编码")
    private String positionCode;

    @Excel(name = "职位名称", sort = 3)
    @ApiModelProperty("职位编码名称")
    private String positionName;

    /** 工作地址id */
    @ApiModelProperty("工作地址id")
    private Long workAddressId;

    @Excel(name = "用工地址", sort = 9)
    @ApiModelProperty("工作地址")
    private String workAddress;

    @ApiModelProperty("门牌号")
    private String houseNumber;

    /** 工作经验 */
    @ApiModelProperty("工作经验")
    private String workExperience;

    @ApiModelProperty("工作经验名称")
    private String workExperienceName;

    /** 最低学历 */
    @Excel(name = "学历", sort = 7, dictType = "background_type")
    @ApiModelProperty("最低学历")
    private String minimumEducation;

    @ApiModelProperty("最低学历名称")
    private String minimumEducationName;

    /** 最高薪资 */
    @Excel(name = "最高薪资", sort = 8)
    @ApiModelProperty("最高薪资")
    private String maximumSalary;

    /** 最低薪资 */
    @Excel(name = "最低薪资", sort = 7)
    @ApiModelProperty("最低薪资")
    private String minimumWage;

    /** 招聘人数 */
    @Excel(name = "招聘人数", sort = 13)
    @ApiModelProperty("招聘人数")
    private String recruitingNumbers;

    /** 年龄要求 */
    @Excel(name = "最高年龄", sort = 6)
    @ApiModelProperty("年龄要求")
    private String ageRequirements;

    @Excel(name = "最低年龄", sort = 5)
    @ApiModelProperty("年龄要求")
    private String ageMinimum;

    /** 性别要求 */
    @ApiModelProperty("性别要求")
    private String genderRequirements;

    @ApiModelProperty("性别要求名称")
    private String genderRequirementsName;

    /** 招聘类型 */
    @ApiModelProperty("招聘类型")
    private String tecruitmentType;

    @ApiModelProperty("招聘类型名称")
    private String tecruitmentTypeName;

    /** 招聘描述 */
    @Excel(name = "职位描述", sort = 4)
    @ApiModelProperty("招聘描述")
    private String jobDescription;

    /** 职位状态 */
    @ApiModelProperty("职位状态")
    private String positionStatus;

    @ApiModelProperty("招聘者职位")
    private String position;

    @ApiModelProperty("企业规模")
    private String scale;

    @ApiModelProperty("企业规模名称")
    private String scaleName;

    @ApiModelProperty("企业性质")
    private String enterpriseNature;

    @ApiModelProperty("企业性质名称")
    private String enterpriseNatureName;

    @ApiModelProperty("所在地区")
    private String region;

    @ApiModelProperty("查询类别，1推荐，2最新，3急聘")
    private String queryCategory;

    @ApiModelProperty("工作地区")
    private String areaName;

    @ApiModelProperty("所属行业")
    private String companyIndustryName;

    @Excel(name = "福利", sort = 10, type = Excel.Type.IMPORT)
    @ApiModelProperty("福利")
    private String materialBenefits;

    @ApiModelProperty("头像")
    private String headSculpture;

    @ApiModelProperty("在招职位数")
    private int positionNum;

    @ApiModelProperty("查询收藏职位")
    private int collectionPosition;

    @ApiModelProperty("收藏表id")
    private Long collectId;

    @ApiModelProperty("详细地址")
    private String detailAddress;

    @ApiModelProperty("企业地图经度")
    private String enterpriseMapLongitude;

    @ApiModelProperty("企业地图纬度")
    private String enterpriseMapLatitude;

    @ApiModelProperty("证件号码")
    private String idCard;

    @ApiModelProperty("认证姓名")
    private String authenticationName;

    @ApiModelProperty("绑定状态")
    private String bindingStatus;

    @ApiModelProperty("营业执照")
    private String businessUrl;

    private Integer grade;

    @ApiModelProperty("地址列表")
    private List<RecruitWorkAddress> workAddressList;


    @ApiModelProperty("蓝V状态")
    private String state;

    @ApiModelProperty("纬度")
    private String latitude;

    @ApiModelProperty("经度")
    private String longitude;

    @ApiModelProperty("直线距离")
    private String linearDistance;

    @ApiModelProperty("急聘字段")
    private String helpWanted;

    @ApiModelProperty("福利")
    private List<String> materialBenefitsList;

    @ApiModelProperty("发布时间")
    private String releaseTime;

    @ApiModelProperty("点击量")
    private String hitsNum;
}
