package com.recruit.core.domain.request.temporary;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Auther: <PERSON> kong
 * @Date: 2023/5/27 17:15
 * @Description:
 */
@Data
public class ApplicationFormRequest {

    @ApiModelProperty("服务行业编码")
    private List<String> serviceTrades;

    @ApiModelProperty("商户名称")
    private String serviceName;

    @ApiModelProperty("申请人姓名")
    private String userName;

    @ApiModelProperty("联系手机号")
    private String phone;

    @ApiModelProperty("身份证号")
    private String idCard;

    @ApiModelProperty("申请理由")
    private String applicationReason;


    @ApiModelProperty("营业执照")
    private String businessLicenseUrl;

    @ApiModelProperty("申请资料(可上传多张图片)")
    private String applicationMaterialsUrl;

    @ApiModelProperty("身份证正面")
    private String idCardZ;

    @ApiModelProperty("身份证反面")
    private String idCardF;

    @ApiModelProperty("所在地区")
    private String region;

    @ApiModelProperty("详细地址")
    private String address;

    @ApiModelProperty("经度")
    private String longitude;

    @ApiModelProperty("纬度")
    private String latitude;

}
