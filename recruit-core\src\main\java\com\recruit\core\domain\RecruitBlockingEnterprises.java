package com.recruit.core.domain;

import com.recruit.common.annotation.Excel;
import com.recruit.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 屏蔽公司对象 recruit_blocking_enterprises
 *
 * <AUTHOR>
 * @date 2023-04-12
 */
@Data
@ApiModel("屏蔽公司")
public class RecruitBlockingEnterprises extends BaseEntity
{
    private static final long serialVersionUID = 1L;


    private Long id;


    @Excel(name = "企业id")
    @ApiModelProperty("企业id")
    private Long enterpriseId;
    @Excel(name = "用户id")
    @ApiModelProperty("用户id")
    private Long userId;


    @ApiModelProperty("企业名称")
    private String enterpriseName;

    @ApiModelProperty("企业logo")
    private String enterpriseLogo;
    @ApiModelProperty("企业性质")
    private String enterpriseNature;

    @ApiModelProperty("企业性质名称")
    private String enterpriseNatureName;

    @ApiModelProperty("企业规模")
    private String scale;
    @ApiModelProperty("企业规模名称")
    private String scaleName;

    @ApiModelProperty("所属行业")
    private String companyIndustryName;

    @ApiModelProperty("蓝V状态")
    private String state;
}
