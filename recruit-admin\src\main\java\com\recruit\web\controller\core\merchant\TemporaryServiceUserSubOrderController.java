package com.recruit.web.controller.core.merchant;

import java.math.BigDecimal;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.recruit.common.annotation.Log;
import com.recruit.common.core.controller.BaseController;
import com.recruit.common.core.domain.AjaxResult;
import com.recruit.common.enums.BusinessType;
import org.springframework.web.multipart.MultipartFile;
import com.recruit.core.domain.TemporaryServiceUserSubOrder;
import com.recruit.core.service.ITemporaryServiceUserSubOrderService;
import com.recruit.common.utils.poi.ExcelUtil;
import com.recruit.common.core.page.TableDataInfo;

/**
 * 用户订单子单Controller
 *
 * <AUTHOR>
 * @date 2023-06-02
 */
@RestController
@RequestMapping("/core/serviceUserSubOrder")
public class TemporaryServiceUserSubOrderController extends BaseController
{
    @Autowired
    private ITemporaryServiceUserSubOrderService temporaryServiceUserSubOrderService;

    /**
     * 查询用户订单子单列表
     */
    @GetMapping("/list")
    public TableDataInfo list(TemporaryServiceUserSubOrder temporaryServiceUserSubOrder)
    {
        startPage();
        List<TemporaryServiceUserSubOrder> list = temporaryServiceUserSubOrderService.selectTemporaryServiceUserSubOrderList(temporaryServiceUserSubOrder);
        list.forEach(e->{
            if(e.getOrderPrice() == null){
                e.setOrderPrice(new BigDecimal(0));
            }
        });
        return getDataTable(list);
    }

    /**
     * 导出用户订单子单列表
     */
    @Log(title = "用户订单子单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TemporaryServiceUserSubOrder temporaryServiceUserSubOrder)
    {
        List<TemporaryServiceUserSubOrder> list = temporaryServiceUserSubOrderService.selectTemporaryServiceUserSubOrderList(temporaryServiceUserSubOrder);
        ExcelUtil<TemporaryServiceUserSubOrder> util = new ExcelUtil<TemporaryServiceUserSubOrder>(TemporaryServiceUserSubOrder.class);
        util.exportExcel(response, list, "用户订单子单数据");
    }

    /**
     * 获取用户订单子单详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(temporaryServiceUserSubOrderService.selectTemporaryServiceUserSubOrderById(id));
    }

    /**
     * 新增用户订单子单
     */
    @Log(title = "用户订单子单", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TemporaryServiceUserSubOrder temporaryServiceUserSubOrder)
    {
        return toAjax(temporaryServiceUserSubOrderService.insertTemporaryServiceUserSubOrder(temporaryServiceUserSubOrder));
    }

    /**
     * 修改用户订单子单
     */
    @Log(title = "用户订单子单", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TemporaryServiceUserSubOrder temporaryServiceUserSubOrder)
    {
        return toAjax(temporaryServiceUserSubOrderService.updateTemporaryServiceUserSubOrder(temporaryServiceUserSubOrder));
    }

    /**
     * 删除用户订单子单
     */
    @Log(title = "用户订单子单", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(temporaryServiceUserSubOrderService.deleteTemporaryServiceUserSubOrderByIds(ids));
    }


    /**
     * 导入用户订单子单
     * @param file
     * @param updateSupport
     * @return
     * @throws Exception
     */
    @Log(title = "用户订单子单", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<TemporaryServiceUserSubOrder> util = new ExcelUtil<>(TemporaryServiceUserSubOrder.class);
        List<TemporaryServiceUserSubOrder> lists = util.importExcel(file.getInputStream());
        String message = temporaryServiceUserSubOrderService.importTemporaryServiceUserSubOrder(lists, updateSupport);
        return AjaxResult.success(message);
    }


}
