package com.recruit.core.domain.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.recruit.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @Auther: <PERSON> kong
 * @Date: 2023/4/21 21:24
 * @Description:
 */
@Data
@ApiModel("职位信息返回11")
public class PositionInfoResponse {

    private Long id;

    @ApiModelProperty("头像")
    private String headSculpture;

    @ApiModelProperty("发布人id，即用户id")
    private Long publisherId;

    @ApiModelProperty("发布人名称")
    private String publisherName;

    @ApiModelProperty("招聘者职位")
    private String position;

    @ApiModelProperty("职位编码")
    private String positionCode;

    @ApiModelProperty("职位编码名称")
    private String positionName;

    @ApiModelProperty("最低学历")
    private String minimumEducation;

    @ApiModelProperty("最低学历名称")
    private String minimumEducationName;

    @ApiModelProperty("工作经验")
    private String workExperience;

    @ApiModelProperty("工作经验名称")
    private String workExperienceName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty("最高薪资")
    private String maximumSalary;

    @ApiModelProperty("最低薪资")
    private String minimumWage;

}
