package com.recruit.core.domain.request.resume;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * @Auther: <PERSON> kong
 * @Date: 2023/3/19 21:52
 * @Description:
 */
@Data
@ApiModel("求职者基本资料1")
public class BasicInfoRequest {

    @NotBlank(message = "姓名不能为空")
    @ApiModelProperty("姓名")
    private String userName;

    @ApiModelProperty("性别")
    private String sex;

    @NotNull(message = "出生年月不能为空")
    @ApiModelProperty("出生年月")
    private Date dateOfBirth;


    @ApiModelProperty("邮箱")
    private String mailbox;

    @ApiModelProperty("所在城市")
    private String city;

    @ApiModelProperty("所在城市")
    private List<String> citys;

    @NotBlank(message = "工作经验不能为空")
    @ApiModelProperty("工作经验")
    private String workExperience;

    @NotBlank(message = "最高学历不能为空")
    @ApiModelProperty("最高学历")
    private String education;

    @ApiModelProperty("头像")
    private String headSculpture;

}
