package com.recruit.core.domain.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Auther: <PERSON> kong
 * @Date: 2023/4/11 10:52
 * @Description:
 */
@Data
@ApiModel("校验是否发送手机好微信简历等信息返回")
public class MobileWeChatResumeResponse {

    @ApiModelProperty("是否发送")
    private boolean sendOrNot;

    @ApiModelProperty("手机号")
    private String phone;

    @ApiModelProperty("微信号")
    private String wechatNum;

    @ApiModelProperty("附件简历url")
    private String attachmentResumeUrl;

    @ApiModelProperty("附件简历名称")
    private String resumeName;
}
