package com.recruit.web.controller.core.common;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.recruit.common.core.domain.TreeSelect;
import com.recruit.common.core.domain.TreeSelectTwo;
import com.recruit.common.core.domain.entity.RecruitCompanyIndustry;
import com.recruit.common.core.domain.entity.SysDept;
import com.recruit.common.utils.StringUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.recruit.common.annotation.Log;
import com.recruit.common.core.controller.BaseController;
import com.recruit.common.core.domain.AjaxResult;
import com.recruit.common.enums.BusinessType;
import com.recruit.core.service.IRecruitCompanyIndustryService;
import com.recruit.common.utils.poi.ExcelUtil;

/**
 * 所属行业Controller
 *
 * <AUTHOR>
 * @date 2023-03-28
 */
@RestController
@RequestMapping("/core/companyIndustry")
public class RecruitCompanyIndustryController extends BaseController
{
    @Autowired
    private IRecruitCompanyIndustryService recruitCompanyIndustryService;

    /**
     * 查询所属行业列表
     */
    @PreAuthorize("@ss.hasPermi('core:companyIndustry:list')")
    @GetMapping("/list")
    public AjaxResult list(RecruitCompanyIndustry recruitCompanyIndustry)
    {
        List<RecruitCompanyIndustry> list = recruitCompanyIndustryService.selectRecruitCompanyIndustryList(recruitCompanyIndustry);
        return success(list);
    }

    @GetMapping("/list/exclude/{id}")
    public AjaxResult excludeChild(@PathVariable(value = "id", required = false) Long id)
    {
        List<RecruitCompanyIndustry> depts = recruitCompanyIndustryService.selectRecruitCompanyIndustryList(new RecruitCompanyIndustry());
        depts.removeIf(d -> d.getId().intValue() == id || ArrayUtils.contains(StringUtils.split(d.getAncestors(), ","), id + ""));
        return success(depts);
    }


    /**
     * 导出所属行业列表
     */
    @PreAuthorize("@ss.hasPermi('core:companyIndustry:export')")
    @Log(title = "所属行业", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RecruitCompanyIndustry recruitCompanyIndustry)
    {
        List<RecruitCompanyIndustry> list = recruitCompanyIndustryService.selectRecruitCompanyIndustryList(recruitCompanyIndustry);
        ExcelUtil<RecruitCompanyIndustry> util = new ExcelUtil<RecruitCompanyIndustry>(RecruitCompanyIndustry.class);
        util.exportExcel(response, list, "所属行业数据");
    }

    /**
     * 获取所属行业详细信息
     */
    @PreAuthorize("@ss.hasPermi('core:companyIndustry:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(recruitCompanyIndustryService.selectRecruitCompanyIndustryById(id));
    }

    /**
     * 新增所属行业
     */
    @PreAuthorize("@ss.hasPermi('core:companyIndustry:add')")
    @Log(title = "所属行业", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RecruitCompanyIndustry recruitCompanyIndustry)
    {
        return toAjax(recruitCompanyIndustryService.insertRecruitCompanyIndustry(recruitCompanyIndustry));
    }

    /**
     * 修改所属行业
     */
    @PreAuthorize("@ss.hasPermi('core:companyIndustry:edit')")
    @Log(title = "所属行业", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RecruitCompanyIndustry recruitCompanyIndustry)
    {
        return toAjax(recruitCompanyIndustryService.updateRecruitCompanyIndustry(recruitCompanyIndustry));
    }

    /**
     * 删除所属行业
     */
    @PreAuthorize("@ss.hasPermi('core:companyIndustry:remove')")
    @Log(title = "所属行业", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(recruitCompanyIndustryService.deleteRecruitCompanyIndustryByIds(ids));
    }


    @GetMapping("/getCompanyIndustryList")
    public AjaxResult getCompanyIndustryList()
    {
        List<RecruitCompanyIndustry> lists = recruitCompanyIndustryService.selectRecruitCompanyIndustryList(new RecruitCompanyIndustry());
        List<TreeSelectTwo>  list = recruitCompanyIndustryService.buildtCompanyIndustryTreeSelectTwo(lists);
        return success(list);
    }
}
