package com.recruit.core.domain.request.other;

import com.recruit.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Auther: <PERSON> kong
 * @Date: 2023/4/22 10:52
 * @Description:
 */
@Data
@ApiModel("查询企业信息")
public class JobFairsRequest extends BaseEntity {

    @ApiModelProperty("所在地区")
    private String region;

    @ApiModelProperty("工作经验")
    private String workExperience;

    @ApiModelProperty("最低学历")
    private String minimumEducation;

    @ApiModelProperty("性别要求")
    private String genderRequirements;

    @ApiModelProperty("企业性质")
    private String enterpriseNature;

    @ApiModelProperty("企业规模")
    private String scale;

    @ApiModelProperty("最高薪资")
    private String maximumSalary;

    @ApiModelProperty("最低薪资")
    private String minimumWage;

    @ApiModelProperty("职位")
    private String position;
}
