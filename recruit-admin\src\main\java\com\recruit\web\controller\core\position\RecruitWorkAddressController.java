package com.recruit.web.controller.core.position;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.recruit.common.annotation.Log;
import com.recruit.common.core.controller.BaseController;
import com.recruit.common.core.domain.AjaxResult;
import com.recruit.common.enums.BusinessType;
import com.recruit.core.domain.RecruitWorkAddress;
import com.recruit.core.service.IRecruitWorkAddressService;
import com.recruit.common.utils.poi.ExcelUtil;
import com.recruit.common.core.page.TableDataInfo;

/**
 * 工作地址Controller
 *
 * <AUTHOR>
 * @date 2023-03-26
 */
@RestController
@RequestMapping("/core/workAddress")
public class RecruitWorkAddressController extends BaseController
{
    @Autowired
    private IRecruitWorkAddressService recruitWorkAddressService;

    /**
     * 查询工作地址列表
     */
    @PreAuthorize("@ss.hasPermi('core:workAddress:list')")
    @GetMapping("/list")
    public TableDataInfo list(RecruitWorkAddress recruitWorkAddress)
    {
        startPage();
        List<RecruitWorkAddress> list = recruitWorkAddressService.selectRecruitWorkAddressList(recruitWorkAddress);
        return getDataTable(list);
    }

    /**
     * 导出工作地址列表
     */
    @PreAuthorize("@ss.hasPermi('core:workAddress:export')")
    @Log(title = "工作地址", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RecruitWorkAddress recruitWorkAddress)
    {
        List<RecruitWorkAddress> list = recruitWorkAddressService.selectRecruitWorkAddressList(recruitWorkAddress);
        ExcelUtil<RecruitWorkAddress> util = new ExcelUtil<RecruitWorkAddress>(RecruitWorkAddress.class);
        util.exportExcel(response, list, "工作地址数据");
    }

    /**
     * 获取工作地址详细信息
     */
    @PreAuthorize("@ss.hasPermi('core:workAddress:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(recruitWorkAddressService.selectRecruitWorkAddressById(id));
    }

    /**
     * 新增工作地址
     */
    @PreAuthorize("@ss.hasPermi('core:workAddress:add')")
    @Log(title = "工作地址", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RecruitWorkAddress recruitWorkAddress)
    {
        return toAjax(recruitWorkAddressService.insertRecruitWorkAddress(recruitWorkAddress));
    }

    /**
     * 修改工作地址
     */
    @PreAuthorize("@ss.hasPermi('core:workAddress:edit')")
    @Log(title = "工作地址", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RecruitWorkAddress recruitWorkAddress)
    {
        return toAjax(recruitWorkAddressService.updateRecruitWorkAddress(recruitWorkAddress));
    }

    /**
     * 删除工作地址
     */
    @PreAuthorize("@ss.hasPermi('core:workAddress:remove')")
    @Log(title = "工作地址", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(recruitWorkAddressService.deleteRecruitWorkAddressByIds(ids));
    }


    @GetMapping("/getWorkAddressList")
    public TableDataInfo getWorkAddressList(RecruitWorkAddress recruitWorkAddress)
    {
        List<RecruitWorkAddress> list = recruitWorkAddressService.selectRecruitWorkAddressList(recruitWorkAddress);
        return getDataTable(list);
    }
}
