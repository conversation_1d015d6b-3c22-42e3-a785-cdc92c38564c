package com.recruit.core.domain;

import com.recruit.common.annotation.Excel;
import com.recruit.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 收藏对象 recruit_collect
 *
 * <AUTHOR>
 * @date 2023-04-12
 */
@Data
@ApiModel("收藏")
public class RecruitCollect extends BaseEntity
{
    private static final long serialVersionUID = 1L;


    private Long id;


    @Excel(name = "用户id")
    @ApiModelProperty("用户id")
    private Long userId;


    @Excel(name = "发布人id")
    @ApiModelProperty("发布人id")
    private Long publisherId;


    @Excel(name = "企业id")
    @ApiModelProperty("企业id")
    private Long enterpriseId;


    @Excel(name = "职位id")
    @ApiModelProperty("职位id")
    private Long positionInfoId;


    @Excel(name = "求职者收藏职位1，招聘者求职者2, 求职者收藏企业3")
    @ApiModelProperty("求职者收藏职位1，招聘者求职者2, 求职者收藏企业3")
    private String collectType;


}
