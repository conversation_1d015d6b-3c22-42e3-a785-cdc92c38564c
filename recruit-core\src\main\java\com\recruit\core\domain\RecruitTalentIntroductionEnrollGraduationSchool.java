package com.recruit.core.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.recruit.common.annotation.Excel;
import com.recruit.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 人才引进报名毕业学校对象 recruit_talent_introduction_enroll_graduation_school
 *
 * <AUTHOR>
 * @date 2024-03-15
 */
@Data
@ApiModel("人才引进报名毕业学校")
public class RecruitTalentIntroductionEnrollGraduationSchool extends BaseEntity
{
    private static final long serialVersionUID = 1L;


    private Long id;


    @ApiModelProperty("人才引进报名id")
    private Long talentIntroductionEnrollId;

    @ApiModelProperty("序号")
    private Integer index;

    @ApiModelProperty("序号")
    private String sequence;


    @ApiModelProperty("毕业学校")
    private String graduationSchool;


    @ApiModelProperty("专业")
    private String major;

    @ApiModelProperty("类型")
    private String type;

    @ApiModelProperty("主修课程")
    private String mainCourse;

    @ApiModelProperty("毕业证照片")
    private String graduationImg;

    @ApiModelProperty("学位证照片")
    private String degreeImg;

    @ApiModelProperty("学信网照片")
    private String chsiImg;


    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("学位时间")
    private Date degreeTime;


}
