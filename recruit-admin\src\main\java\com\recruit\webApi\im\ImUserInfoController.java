package com.recruit.webApi.im;

import com.recruit.common.constant.Constants;
import com.recruit.common.constant.RedisKey;
import com.recruit.common.core.controller.BaseController;
import com.recruit.common.core.domain.AjaxResult;
import com.recruit.core.domain.RecruitUserInfo;
import com.recruit.core.service.IRecruitUserInfoService;
import com.recruit.im.netty.IMServerGroup;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.constraints.NotEmpty;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * @Auther: Wu kong
 * @Date: 2023/4/7 11:15
 * @Description:
 */
@Api(tags= "IM用户信息")
@RestController
@RequestMapping("/web/api/ImUser")
public class ImUserInfoController extends BaseController {

    @Resource
    RedisTemplate<String,Object> redisTemplate;

    @Autowired
    private IRecruitUserInfoService recruitUserInfoService;

    @ApiOperation("建立连接，webSocket 连接成功后第一步调用接口，建立用户连接")
    @GetMapping("/establishConnection")
    public AjaxResult establishConnection() {
        String key = RedisKey.IM_USER_SERVER_ID + getUserId();
        redisTemplate.opsForValue().set(key, IMServerGroup.serverId, Constants.ONLINE_TIMEOUT_SECOND, TimeUnit.SECONDS);
        return success();
    }


    @GetMapping("/online")
    @ApiOperation(value = "判断用户是否在线", notes="返回在线的用户id集合")
    public AjaxResult checkOnline(@NotEmpty @RequestParam("userIds") String userIds){
        List<Long> onlineIds = recruitUserInfoService.checkOnline(userIds);
        return success(onlineIds);
    }

    @GetMapping("/self")
    @ApiOperation(value = "获取当前用户信息",notes="获取当前用户信息")
    public AjaxResult findSelfInfo(){
        RecruitUserInfo user = recruitUserInfoService.selectRecruitUserInfoById(getUserId());
        return success(user);
    }


    @GetMapping("/find/{id}")
    @ApiOperation(value = "查找用户",notes="根据id查找用户")
    public AjaxResult findByIde(@NotEmpty @PathVariable("id") long id){
        RecruitUserInfo user = recruitUserInfoService.selectRecruitUserInfoById(id);
        return success(user);
    }

    @GetMapping("/findByNickName")
    @ApiOperation(value = "查找用户",notes="根据昵称查找用户")
    public AjaxResult findByNickName(@NotEmpty(message = "用户昵称不可为空") @RequestParam("nickName") String nickName){
        return success( recruitUserInfoService.findUserByNickName(nickName));
    }

}
