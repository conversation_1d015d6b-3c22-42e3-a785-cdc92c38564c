package com.recruit.web.controller.core.other;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.recruit.common.annotation.Log;
import com.recruit.common.core.controller.BaseController;
import com.recruit.common.core.domain.AjaxResult;
import com.recruit.common.enums.BusinessType;
import com.recruit.core.domain.RecruitBlueVAttestation;
import com.recruit.core.service.IRecruitBlueVAttestationService;
import com.recruit.common.utils.poi.ExcelUtil;
import com.recruit.common.core.page.TableDataInfo;

/**
 * 企业蓝V认证Controller
 *
 * <AUTHOR>
 * @date 2023-05-04
 */
@RestController
@RequestMapping("/core/blueVAttestation")
public class RecruitBlueVAttestationController extends BaseController
{
    @Autowired
    private IRecruitBlueVAttestationService recruitBlueVAttestationService;

    /**
     * 查询企业蓝V认证列表
     */
    @PreAuthorize("@ss.hasPermi('core:blueVAttestation:list')")
    @GetMapping("/list")
    public TableDataInfo list(RecruitBlueVAttestation recruitBlueVAttestation)
    {
        startPage();
        List<RecruitBlueVAttestation> list = recruitBlueVAttestationService.selectRecruitBlueVAttestationList(recruitBlueVAttestation);
        return getDataTable(list);
    }

    /**
     * 导出企业蓝V认证列表
     */
    @PreAuthorize("@ss.hasPermi('core:blueVAttestation:export')")
    @Log(title = "企业蓝V认证", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RecruitBlueVAttestation recruitBlueVAttestation)
    {
        List<RecruitBlueVAttestation> list = recruitBlueVAttestationService.selectRecruitBlueVAttestationList(recruitBlueVAttestation);
        ExcelUtil<RecruitBlueVAttestation> util = new ExcelUtil<RecruitBlueVAttestation>(RecruitBlueVAttestation.class);
        util.exportExcel(response, list, "企业蓝V认证数据");
    }

    /**
     * 获取企业蓝V认证详细信息
     */
    @PreAuthorize("@ss.hasPermi('core:blueVAttestation:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(recruitBlueVAttestationService.selectRecruitBlueVAttestationById(id));
    }

    /**
     * 新增企业蓝V认证
     */
    @PreAuthorize("@ss.hasPermi('core:blueVAttestation:add')")
    @Log(title = "企业蓝V认证", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RecruitBlueVAttestation recruitBlueVAttestation)
    {
        return toAjax(recruitBlueVAttestationService.insertRecruitBlueVAttestation(recruitBlueVAttestation));
    }

    /**
     * 修改企业蓝V认证
     */
    @PreAuthorize("@ss.hasPermi('core:blueVAttestation:edit')")
    @Log(title = "企业蓝V认证", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RecruitBlueVAttestation recruitBlueVAttestation)
    {
        return toAjax(recruitBlueVAttestationService.updateRecruitBlueVAttestation(recruitBlueVAttestation));
    }

    /**
     * 删除企业蓝V认证
     */
    @PreAuthorize("@ss.hasPermi('core:blueVAttestation:remove')")
    @Log(title = "企业蓝V认证", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(recruitBlueVAttestationService.deleteRecruitBlueVAttestationByIds(ids));
    }
}
