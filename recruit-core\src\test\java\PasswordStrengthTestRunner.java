// 注意：使用测试类中定义的SysUser

/**
 * 密码强度测试运行器
 * 用于运行所有密码强度检测测试并生成报告
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-30
 */
public class PasswordStrengthTestRunner {

    public static void main(String[] args) {
        System.out.println(repeatString("=", 80));
        System.out.println("                    系统用户弱口令检测测试报告");
        System.out.println(repeatString("=", 80));
        System.out.println("测试时间: " + new java.util.Date());
        System.out.println("测试目的: 验证系统用户密码强度检测功能的有效性");
        System.out.println(repeatString("=", 80));

        SysUserPasswordStrengthTest test = new SysUserPasswordStrengthTest();
        SysUserPasswordStrengthTest.PasswordStrengthValidator validator = 
            new SysUserPasswordStrengthTest.PasswordStrengthValidator();

        // 执行各种测试场景
        runTestScenarios(validator);
        
        System.out.println("\n" + repeatString("=", 80));
        System.out.println("                           测试总结");
        System.out.println(repeatString("=", 80));
        System.out.println("✓ 强密码验证测试完成");
        System.out.println("✓ 弱密码检测测试完成");
        System.out.println("✓ 常见弱口令检测测试完成");
        System.out.println("✓ 用户名相似性检测测试完成");
        System.out.println("✓ 综合场景测试完成");
        System.out.println("\n建议:");
        System.out.println("1. 在生产环境中启用密码强度检测");
        System.out.println("2. 定期更新常见弱口令字典");
        System.out.println("3. 对用户进行密码安全教育");
        System.out.println("4. 考虑实施密码定期更换策略");
        System.out.println(repeatString("=", 80));
    }

    /**
     * 运行测试场景
     */
    private static void runTestScenarios(SysUserPasswordStrengthTest.PasswordStrengthValidator validator) {
        
        // 测试场景1: 强密码
        System.out.println("\n【测试场景1: 强密码验证】");
        testPassword(validator, "admin", "AdminPass2024!");
        testPassword(validator, "user123", "MySecure@Pass456");
        testPassword(validator, "manager", "Complex#Password789");

        // 测试场景2: 长度不足的密码
        System.out.println("\n【测试场景2: 长度不足的密码】");
        testPassword(validator, "user1", "Pass1!");
        testPassword(validator, "test", "Ab1@");
        testPassword(validator, "admin", "123456");

        // 测试场景3: 复杂度不够的密码
        System.out.println("\n【测试场景3: 复杂度不够的密码】");
        testPassword(validator, "user1", "password");
        testPassword(validator, "test", "12345678");
        testPassword(validator, "admin", "ABCDEFGH");
        testPassword(validator, "manager", "abcdefgh");

        // 测试场景4: 常见弱口令
        System.out.println("\n【测试场景4: 常见弱口令检测】");
        String[] commonWeak = {"123456", "password", "admin", "qwerty", "abc123", "password123"};
        for (String weak : commonWeak) {
            testPassword(validator, "testuser", weak);
        }

        // 测试场景5: 与用户名相关的密码
        System.out.println("\n【测试场景5: 与用户名相关的密码】");
        testPassword(validator, "admin", "admin");
        testPassword(validator, "testuser", "testuser123");
        testPassword(validator, "manager", "managerpwd");
        testPassword(validator, "developer", "developer2024");

        // 测试场景6: 包含连续字符的密码
        System.out.println("\n【测试场景6: 包含连续字符的密码】");
        testPassword(validator, "user1", "Password123");
        testPassword(validator, "test", "Abcdef123!");
        testPassword(validator, "admin", "Qwerty123@");

        // 测试场景7: 边界情况
        System.out.println("\n【测试场景7: 边界情况测试】");
        testPassword(validator, "user", "Aa1!Aa1!"); // 刚好8位
        testPassword(validator, "test", "A1!A1!A1!A1!A1!"); // 很长的密码
        testPassword(validator, "admin", ""); // 空密码
        testPassword(validator, "", "Password123!"); // 空用户名
    }

    /**
     * 测试单个密码
     */
    private static void testPassword(SysUserPasswordStrengthTest.PasswordStrengthValidator validator,
                                   String username, String password) {
        SysUserPasswordStrengthTest.SysUser user = new SysUserPasswordStrengthTest.SysUser();
        user.setUserName(username);
        user.setPassword(password);
        
        SysUserPasswordStrengthTest.PasswordStrengthResult result = validator.validatePassword(user);
        
        System.out.println("\n用户名: " + username);
        System.out.println("密码: " + maskPassword(password));
        System.out.println("结果: " + (result.isPassed() ? "✓ 通过" : "✗ 失败"));
        System.out.println("评分: " + result.getScore() + "/100");
        
        if (!result.isPassed()) {
            System.out.println("失败原因: " + String.join("; ", result.getFailureReasons()));
        }
        
        // 显示密码特征分析
        analyzePasswordFeatures(password);
    }

    /**
     * 分析密码特征
     */
    private static void analyzePasswordFeatures(String password) {
        if (password == null || password.isEmpty()) {
            System.out.println("特征: 空密码");
            return;
        }
        
        StringBuilder features = new StringBuilder("特征: ");
        features.append("长度=").append(password.length());
        
        if (hasUppercase(password)) features.append(", 大写");
        if (hasLowercase(password)) features.append(", 小写");
        if (hasDigit(password)) features.append(", 数字");
        if (hasSpecialChar(password)) features.append(", 特殊字符");
        
        System.out.println(features.toString());
    }

    /**
     * 掩码显示密码
     */
    private static String maskPassword(String password) {
        if (password == null || password.isEmpty()) {
            return "[空]";
        }
        if (password.length() <= 2) {
            return repeatString("*", password.length());
        }
        return password.charAt(0) + repeatString("*", password.length() - 2) + password.charAt(password.length() - 1);
    }

    // 辅助方法
    private static boolean hasUppercase(String password) {
        return password.chars().anyMatch(Character::isUpperCase);
    }

    private static boolean hasLowercase(String password) {
        return password.chars().anyMatch(Character::isLowerCase);
    }

    private static boolean hasDigit(String password) {
        return password.chars().anyMatch(Character::isDigit);
    }

    private static boolean hasSpecialChar(String password) {
        return password.chars().anyMatch(ch -> "!@#$%^&*()_+-=[]{}|;':\"\\,.<>/?".indexOf(ch) >= 0);
    }

    /**
     * Java 8兼容的字符串重复方法
     */
    private static String repeatString(String str, int count) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < count; i++) {
            sb.append(str);
        }
        return sb.toString();
    }
}
