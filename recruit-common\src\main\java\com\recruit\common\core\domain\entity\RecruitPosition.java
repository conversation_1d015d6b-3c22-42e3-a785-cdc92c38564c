package com.recruit.common.core.domain.entity;

import com.recruit.common.annotation.Excel;
import com.recruit.common.core.domain.BaseEntity;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 职位对象 sys_position
 *
 * <AUTHOR>
 * @date 2023-03-20
 */
@Data
public class RecruitPosition extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    private Long parentId;

    /** code */
    @Excel(name = "code")
    private String code;

    /** 名称 */
    @Excel(name = "名称")
    private String name;

    /** 父code */
    @Excel(name = "父code")
    private String fCode;

    @Excel(name = "祖级列表")
    private String ancestors;


    @Excel(name = "显示顺序")
    private Integer orderNum;

    @Excel(name = "等级")
    private Integer grade;

    /** 子部门 */
    private List<RecruitPosition> children = new ArrayList<RecruitPosition>();
}
