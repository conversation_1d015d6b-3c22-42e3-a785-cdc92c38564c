package com.recruit.core.domain.request.resume;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @Auther: <PERSON> kong
 * @Date: 2023/3/21 13:08
 * @Description:
 */
@Data
@ApiModel("职业技能1")
public class ResumeProfessionalSkillsRequest {

    @ApiModelProperty("id")
    private Long id;


    @NotBlank(message = "技能名称不能为空")
    @ApiModelProperty("技能名称")
    private String skillName;


    @NotBlank(message = "熟练程度不能为空")
    @ApiModelProperty("熟练程度")
    private String proficiency;

}
