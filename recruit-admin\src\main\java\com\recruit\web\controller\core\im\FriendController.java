package com.recruit.web.controller.core.im;

import java.util.List;

import com.recruit.common.utils.DateUtils;
import com.recruit.common.utils.StringUtils;
import com.recruit.core.domain.Friend;
import com.recruit.core.domain.PrivateMessage;
import com.recruit.core.service.IRecruitPositionService;
import com.recruit.core.service.IFriendService;
import com.recruit.core.service.IPrivateMessageService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.recruit.common.core.controller.BaseController;
import com.recruit.common.core.page.TableDataInfo;

import javax.validation.constraints.NotNull;

/**
 * 好友Controller
 *
 * <AUTHOR>
 * @date 2023-05-07
 */
@RestController
@RequestMapping("/core/imFriend/")
public class FriendController extends BaseController
{
    @Autowired
    private IFriendService friendService;

    @Autowired
    private IPrivateMessageService privateMessageService;

    @Autowired
    private IRecruitPositionService recruitPositionService;

    /**
     * 查询好友列表
     */
    @PreAuthorize("@ss.hasPermi('core:imFriend:list')")
    @GetMapping("/list")
    public TableDataInfo findFriends(Friend friend){
        startPage();
        List<Friend> friends = friendService.findFriendByUserIdAll(friend);
        friends.forEach(e->{
            if(e.getPositionInfoName() != null && !StringUtils.equals(e.getPositionInfoName(), "")) {
                e.setPositionInfoName(recruitPositionService.getMap(e.getPositionInfoName()));
            }
            PrivateMessage privateMessage = privateMessageService.getTheLatestNews(e.getUserId(), e.getFriendId(), e.getChatType());
            if(privateMessage != null){
                //消息类型 0:文字 1:图片 2:文件 3:语音，4：交换手机号，5：交换微信 10:系统提示
                if(privateMessage.getType() == 0) {
                    e.setLatestNews(privateMessage.getContent());
                }
                switch (privateMessage.getType()){
                    case 0:
                        e.setLatestNews(privateMessage.getContent());
                        break;
                    case 1:
                    case 2:
                        e.setLatestNews("文件消息");
                        break;
                    case 3:
                        e.setLatestNews("语音消息");
                        break;
                    case 4:
                    case 5:
                        e.setLatestNews("交换消息");
                        break;
                }
                //最新消息时间
                e.setLatestNewsTime(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, privateMessage.getSendTime()));
            }
        });
        return getDataTable(friends);
    }

    /**
     * 查看聊天记录
     * @param friendId
     * @param userId
     * @return
     */
    @GetMapping("/history")
    public TableDataInfo recallMessage(@NotNull(message = "好友id不能为空") @RequestParam Long friendId,
                                       @NotNull(message = "用户id不能为空") @RequestParam Long userId){
        return getDataTable(privateMessageService.findHistoryMessageTwo(userId, friendId));
    }

    /**
     * 获取后四位
     * @param cardNum
     * @return
     */
    public static String getCardTailNum(String cardNum){
        StringBuffer tailNum = new StringBuffer();
        if (cardNum != null) {
            int len = cardNum.length();
            for (int i = len - 1; i >= len - 4; i--) {
                tailNum.append(cardNum.charAt(i));
            }
            tailNum.reverse();
        }
        return tailNum.toString();
    }

}
