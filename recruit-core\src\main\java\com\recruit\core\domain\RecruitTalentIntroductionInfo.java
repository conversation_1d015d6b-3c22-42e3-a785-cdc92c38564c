package com.recruit.core.domain;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.recruit.common.annotation.Excel;
import com.recruit.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 人才引进信息对象 recruit_talent_introduction_info
 *
 * <AUTHOR>
 * @date 2023-05-11
 */
@Data
@ApiModel("人才引进信息")
public class RecruitTalentIntroductionInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;


    private Long id;

    @Excel(name = "标题", sort = 1, type = Excel.Type.EXPORT)
    @ApiModelProperty("标题")
    private String title;

    @ApiModelProperty("人才引进id")
    private Long talentIntroductionId;

    @ApiModelProperty("用户id")
    private Long userId;

    @JsonIgnore
    private String searchValue;


    @Excel(name = "岗位代码", sort = 2)
    @ApiModelProperty("岗位代码")
    private String nameOfSuperiorUnit;


    @Excel(name = "单位名称", sort = 3)
    @ApiModelProperty("单位名称")
    private String nameOfEmployer;


    @Excel(name = "岗位类别", sort = 4)
    @ApiModelProperty("岗位类别")
    private String jobCategory;

    @Excel(name = "岗位名称", sort = 5)
    @ApiModelProperty("岗位名称")
    private String recruitmentPositions;

    @Excel(name = "招聘人数", sort = 6)
    @ApiModelProperty("招聘人数")
    private String recruitingNum;

    @Excel(name = "年龄", sort = 7)
    @ApiModelProperty("年龄")
    private String ageRequirements;


    @Excel(name = "学历", sort = 8)
    @ApiModelProperty("学历")
    private String educationalRequirements;
    
    @Excel(name = "学位", sort = 9)
    @ApiModelProperty("学位")
    private String academicDegree;


    @Excel(name = "专业名称", sort = 10)
    @ApiModelProperty("专业名称")
    private String professionalName;

    @Excel(name = "专业三级目录限制要求", sort = 11)
    @ApiModelProperty("专业三级目录限制要求")
    private String professionalRequirements;


    @Excel(name = "岗位描述", sort = 12)
    @ApiModelProperty("岗位描述")
    private String jobDescription;


    @ApiModelProperty("所在区域")
    private List<String> regions;
    private String region;

    @Excel(name = "所在区域", sort = 13)
    @ApiModelProperty("所在区域")
    private String regionName;


    //@Excel(name = "联系人", sort = 11)
    @ApiModelProperty("联系人")
    private String contacts;


    //@Excel(name = "联系方式", sort = 12)
    @ApiModelProperty("联系方式")
    private String contactInfo;

    /** 备注 */
    @Excel(name = "备注", sort = 14)
    private String remarks;


    @ApiModelProperty("分类")
    private String type;

    @ApiModelProperty("状态")
    private String state;

    @ApiModelProperty("状态")
    private String stateTwo;

    @ApiModelProperty("报名状态")
    private String enrollStatus;


}
