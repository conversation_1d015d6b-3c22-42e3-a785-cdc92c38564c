package com.recruit.common.im;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.recruit.common.serializer.DateToLongSerializer;
import lombok.Data;

import java.util.Date;

@Data
public class GroupMessageInfo {

    /*
     * 消息id
     */
    private Long id;

    /*
     * 群聊id
     */
    private Long groupId;

    /*
     * 发送者id
     */
    private Long sendId;

    /*
     * 消息内容
     */
    private String content;

    /*
     * 消息内容类型 具体枚举值由应用层定义
     */
    private Integer type;

    /**
     * 发送时间
     */
    @JsonSerialize(using = DateToLongSerializer.class)
    private Date sendTime;
}
