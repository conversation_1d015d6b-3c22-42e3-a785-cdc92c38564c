package com.recruit.core.domain.request.merchant;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Auther: <PERSON> kong
 * @Date: 2023/6/3 23:20
 * @Description:
 */
@Data
public class OrderEvaluationRequest {

    @ApiModelProperty("用户订单id")
    private Long serviceUserOrderId;

    @ApiModelProperty("评价内容")
    private String evaluationContent;

    @ApiModelProperty("评价星级 1-5")
    private String evaluateStar;
}
