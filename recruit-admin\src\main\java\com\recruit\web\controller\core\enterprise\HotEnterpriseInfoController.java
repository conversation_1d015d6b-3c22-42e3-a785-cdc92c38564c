package com.recruit.web.controller.core.enterprise;

import com.recruit.common.annotation.Log;
import com.recruit.common.core.controller.BaseController;
import com.recruit.common.core.domain.AjaxResult;
import com.recruit.common.core.page.TableDataInfo;
import com.recruit.common.enums.BusinessType;
import com.recruit.common.utils.StringUtils;
import com.recruit.common.utils.bean.BeanUtils;
import com.recruit.core.domain.*;
import com.recruit.core.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 企业简介Controller
 *
 * <AUTHOR>
 * @date 2023-03-21
 */
@RestController
@RequestMapping("/core/hotEnterpriseInfo")
public class HotEnterpriseInfoController extends BaseController
{

    @Autowired
    private IRecruitEnterpriseInfoService recruitEnterpriseInfoService;

    @Autowired
    private IRecruitEnterpriseBusinessInfoService recruitEnterpriseBusinessInfoService;

    @Autowired
    private IRecruitEnterpriseUsersRelService recruitEnterpriseUsersRelService;

    /**
     * 查询企业简介列表
     */
    @PreAuthorize("@ss.hasPermi('core:hotEnterpriseInfo:list')")
    @GetMapping("/list")
    public TableDataInfo list(RecruitEnterpriseInfo recruitEnterpriseInfo)
    {
        startPage();
        if(StringUtils.equals(recruitEnterpriseInfo.getStatus(), "ALL")){
            recruitEnterpriseInfo.setStatus(null);
        }
        List<RecruitEnterpriseInfo> list = recruitEnterpriseInfoService.selectHotEnterpriseInfoList(recruitEnterpriseInfo);
        return getDataTable(list);
    }

    /**
     * 获取企业简介详细信息
     */
    @PreAuthorize("@ss.hasPermi('core:hotEnterpriseInfo:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        RecruitEnterpriseInfo enterpriseInfo = recruitEnterpriseInfoService.selectRecruitEnterpriseInfoById(id);
        if(enterpriseInfo.getRegion() != null && !StringUtils.equals(enterpriseInfo.getRegion(), "")){
            List<String> regions = new ArrayList<>(Arrays.asList(enterpriseInfo.getRegion().split(",")));
            enterpriseInfo.setRegions(regions);
        }
        return success(enterpriseInfo);
    }

    /**
     * 新增企业简介
     */
    @PreAuthorize("@ss.hasPermi('core:hotEnterpriseInfo:add')")
    @Log(title = "企业简介", businessType = BusinessType.INSERT)
    @PostMapping
    @Transactional
    public AjaxResult add(@RequestBody RecruitEnterpriseInfo recruitEnterpriseInfo)
    {
        RecruitEnterpriseInfo enterpriseInfo = recruitEnterpriseInfoService.getEnterpriseInfo(recruitEnterpriseInfo.getEnterpriseName());
        if(enterpriseInfo == null) {
            if (recruitEnterpriseInfo.getIndustrys() != null) {
                if (recruitEnterpriseInfo.getIndustrys().size() == 1) {
                    recruitEnterpriseInfo.setIndustry(recruitEnterpriseInfo.getIndustrys().get(0));
                } else if (recruitEnterpriseInfo.getIndustrys().size() == 2) {
                    recruitEnterpriseInfo.setIndustry(recruitEnterpriseInfo.getIndustrys().get(1));
                } else if (recruitEnterpriseInfo.getIndustrys().size() == 3) {
                    recruitEnterpriseInfo.setIndustry(recruitEnterpriseInfo.getIndustrys().get(2));
                }
            }
            if (recruitEnterpriseInfo.getRegions() != null) {
                StringBuilder region = new StringBuilder();
                recruitEnterpriseInfo.getRegions().forEach(e->{
                    if(StringUtils.equals(region, "")){
                        region.append(e);
                    }else {
                        region.append(",").append(e);
                    }
                });
                recruitEnterpriseInfo.setRegion(String.valueOf(region));
            }
            recruitEnterpriseInfo.setRemark("后台添加");
            int ss = recruitEnterpriseInfoService.insertRecruitEnterpriseInfo(recruitEnterpriseInfo);
            if (ss > 0) {
                RecruitEnterpriseBusinessInfo enterpriseBusinessInfo = recruitEnterpriseBusinessInfoService.selectEnterpriseBusinessInfoByEnterpriseId(recruitEnterpriseInfo.getEnterpriseId());
                if (enterpriseBusinessInfo != null) {
                    RecruitEnterpriseBusinessInfo businessInfo = new RecruitEnterpriseBusinessInfo();
                    businessInfo.setId(recruitEnterpriseInfo.getBusinessInfoId());
                    businessInfo.setStatus(recruitEnterpriseInfo.getStatus());
                    recruitEnterpriseBusinessInfoService.updateRecruitEnterpriseBusinessInfo(businessInfo);
                } else {
                    RecruitEnterpriseBusinessInfo businessInfo = new RecruitEnterpriseBusinessInfo();
                    BeanUtils.copyBeanProp(businessInfo, recruitEnterpriseInfo);
                    businessInfo.setEnterpriseId(recruitEnterpriseInfo.getId());
                    recruitEnterpriseBusinessInfoService.insertRecruitEnterpriseBusinessInfo(businessInfo);
                }
            }
            return toAjax(ss);
        }else {
            return AjaxResult.error("企业已存在，请勿查验后添加！");
        }
    }

    /**
     * 修改企业简介
     */
    @PreAuthorize("@ss.hasPermi('core:hotEnterpriseInfo:edit')")
    @Log(title = "企业简介", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RecruitEnterpriseInfo recruitEnterpriseInfo)
    {
        RecruitEnterpriseInfo enterpriseInfo = recruitEnterpriseInfoService.getEnterpriseInfo(recruitEnterpriseInfo.getEnterpriseName());
        boolean ssss = false;
        if(enterpriseInfo == null){
            ssss = true;
        }else {
            if(enterpriseInfo.getId().equals(recruitEnterpriseInfo.getId())){
                ssss = true;
            }else {
                ssss = false;
            }
        }
        if(ssss) {
            if (recruitEnterpriseInfo.getIndustrys() != null) {
                if (recruitEnterpriseInfo.getIndustrys().size() == 1) {
                    recruitEnterpriseInfo.setIndustry(recruitEnterpriseInfo.getIndustrys().get(0));
                } else if (recruitEnterpriseInfo.getIndustrys().size() == 2) {
                    recruitEnterpriseInfo.setIndustry(recruitEnterpriseInfo.getIndustrys().get(1));
                } else if (recruitEnterpriseInfo.getIndustrys().size() == 3) {
                    recruitEnterpriseInfo.setIndustry(recruitEnterpriseInfo.getIndustrys().get(2));
                }
            }
            if (recruitEnterpriseInfo.getRegions() != null) {
                StringBuilder region = new StringBuilder();
                recruitEnterpriseInfo.getRegions().forEach(e->{
                    if(StringUtils.equals(region, "")){
                        region.append(e);
                    }else {
                        region.append(",").append(e);
                    }
                });
                recruitEnterpriseInfo.setRegion(String.valueOf(region));
            }

            RecruitEnterpriseBusinessInfo enterpriseBusinessInfo = new RecruitEnterpriseBusinessInfo();
            BeanUtils.copyBeanProp(enterpriseBusinessInfo, recruitEnterpriseInfo);
            enterpriseBusinessInfo.setId(recruitEnterpriseInfo.getBusinessInfoId());
            enterpriseBusinessInfo.setStatus(recruitEnterpriseInfo.getStatus());
            int ss = recruitEnterpriseBusinessInfoService.updateRecruitEnterpriseBusinessInfo(enterpriseBusinessInfo);
            if (ss > 0) {
                if (StringUtils.equals(recruitEnterpriseInfo.getStatus(), "1")) {
                    RecruitEnterpriseUsersRel enterpriseUsersRel = new RecruitEnterpriseUsersRel();
                    enterpriseUsersRel.setEnterpriseId(recruitEnterpriseInfo.getEnterpriseId());
                    enterpriseUsersRel.setUserId(recruitEnterpriseInfo.getUserId());
                    List<RecruitEnterpriseUsersRel> list = recruitEnterpriseUsersRelService.selectRecruitEnterpriseUsersRelList(enterpriseUsersRel);
                    list.forEach(e -> {
                        e.setBindingStatus("2");
                        int aa = recruitEnterpriseUsersRelService.updateRecruitEnterpriseUsersRel(e);
                    });
                }
            } else {
                RecruitEnterpriseBusinessInfo enterpriseBusinessInfos = new RecruitEnterpriseBusinessInfo();
                BeanUtils.copyBeanProp(enterpriseBusinessInfos, recruitEnterpriseInfo);
                enterpriseBusinessInfos.setEnterpriseId(recruitEnterpriseInfo.getId());
                recruitEnterpriseBusinessInfoService.insertRecruitEnterpriseBusinessInfo(enterpriseBusinessInfos);
            }
            return toAjax(recruitEnterpriseInfoService.updateRecruitEnterpriseInfo(recruitEnterpriseInfo));
        }else {
            return AjaxResult.error("企业名称重复，请勿查验后添加！");
        }
    }


    /**
     * 删除企业简介
     */
    @PreAuthorize("@ss.hasPermi('core:hotEnterpriseInfo:remove')")
    @Log(title = "企业简介", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        int ss = recruitEnterpriseInfoService.deleteRecruitEnterpriseInfoByIds(ids);
        if(ss > 0) {
            //删除企业认证信息
            recruitEnterpriseBusinessInfoService.deleteEnterpriseBusinessInfoByEnterpriseId(ids);
            //删除用户企业关联信息
            recruitEnterpriseUsersRelService.deleteUsersRelByEnterpriseId(ids);
        }
        return toAjax(ss);
    }


}
