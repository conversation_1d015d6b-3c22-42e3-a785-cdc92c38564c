package com.recruit.webApi;

import com.recruit.common.config.PublicConfig;
import com.recruit.common.core.controller.BaseController;
import com.recruit.common.core.domain.AjaxResult;
import com.recruit.common.core.page.TableDataInfo;
import com.recruit.common.utils.DateUtils;
import com.recruit.common.utils.StringUtils;
import com.recruit.common.utils.WordTemplateUtils;
import com.recruit.common.utils.file.FileUtils;
import com.recruit.core.domain.RecruitAttestationManage;
import com.recruit.core.domain.RecruitEnterpriseInfo;
import com.recruit.core.domain.RecruitEnterpriseUsersRel;
import com.recruit.core.domain.request.enterprise.CertificationReportRequest;
import com.recruit.core.domain.request.enterprise.DelAttestationRequest;
import com.recruit.core.service.IRecruitAttestationManageService;
import com.recruit.core.service.IRecruitEnterpriseInfoService;
import com.recruit.core.service.IRecruitEnterpriseUsersRelService;
import com.recruit.core.service.IRecruitPositionInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URL;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * @Auther: Wu kong
 * @Date: 2023/5/4 21:00
 * @Description:
 */
@Api(tags= "(05-04)企业认证管理")
@Slf4j
@RestController
@RequestMapping("/web/api/attestationManage")
public class AttestationManageController extends BaseController {

    @Autowired
    private IRecruitPositionInfoService recruitPositionInfoService;

    @Autowired
    private IRecruitEnterpriseInfoService recruitEnterpriseInfoService;

    @Autowired
    private IRecruitAttestationManageService recruitAttestationManageService;

    @Autowired
    private IRecruitEnterpriseUsersRelService recruitEnterpriseUsersRelService;


    @ApiOperation("下载企业认证管理报告")
    @GetMapping("/getEnterpriseCertificationReport")
    public void getEnterpriseCertificationReport(@NotNull(message = "企业id不能为空") Long enterpriseId, HttpServletResponse response)
    {
        try {
            RecruitEnterpriseInfo enterpriseInfo = recruitEnterpriseInfoService.selectRecruitEnterpriseInfoById(enterpriseId);

            Map<String, Object> wordDataMap = new HashMap<String, Object>();// 存储报表全部数据
            Map<String, Object> parametersMap = new HashMap<String, Object>();// 存储报表中不循环的数据

            parametersMap.put("enterpriseName", enterpriseInfo.getEnterpriseName());
            parametersMap.put("socialCreditCode", enterpriseInfo.getSocialCreditCode());
            parametersMap.put("downloadDate", DateUtils.getDatetwo());


            wordDataMap.put("parametersMap", parametersMap);
            File file = getFile("https://minio.hmbhrc.com:9002/box-im/attestationManage/企业认证报告.docx");//改成你本地文件所在目录
            // 读取word模板
            FileInputStream fileInputStream = new FileInputStream(file);
            WordTemplateUtils template = new WordTemplateUtils(fileInputStream);
            // 替换数据
            template.replaceDocument(wordDataMap);


            String realFileName = System.currentTimeMillis() + "企业认证报告.docx";
            String filePath = PublicConfig.getDownloadPath() + realFileName;

            //生成文件
            File outputFile = new File(filePath);//改成你本地文件所在目录
            FileOutputStream fos = new FileOutputStream(outputFile);
            template.getDocument().write(fos);
            response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
            FileUtils.setAttachmentResponseHeader(response, realFileName);
            FileUtils.writeBytes(outputFile.getPath(), response.getOutputStream());
            //关流
            fos.close();
            //删除文件
            boolean flag = outputFile.delete();
            if(flag){
                log.info("文件删除成功");
            }else {
                log.info("文件删除失败");
            }
        }catch (Exception e)
        {
            log.error("下载文件失败", e);
        }

    }

    @ApiOperation("查看认证状态, attestationStatus 0未认证，1认证中，2认证成功，3认证失败，未认证和认证失败可重新上传认证，认证中提示认证中，认证成功可以调用查询用户信息接口")
    @GetMapping("/getAuthenticationStatus")
    public AjaxResult getAuthenticationStatus(@NotNull(message = "企业id不能为空") Long enterpriseId)
    {
        //查询用户与企业绑定信息
        RecruitEnterpriseUsersRel enterpriseUsersRel = recruitEnterpriseUsersRelService.selectEnterpriseUsersRelUserIdAndStatus(getUserId());
        //使用企业id和用户id进行查询
        RecruitAttestationManage attestationManage = recruitAttestationManageService.selectAttestationManageByEnterpriseAndUserId(enterpriseId, enterpriseUsersRel.getUserId());
        Map<String, String> map = new HashMap<>();
        if(attestationManage != null){
            map.put("attestationStatus", attestationManage.getAttestationStatus());
        }else {
            map.put("attestationStatus", "0");
        }

        return success(map);
    }

    @ApiOperation("上传企业认证管理报告")
    @PostMapping("/uploadEnterpriseCertificationReport")
    public AjaxResult uploadEnterpriseCertificationReport(@RequestBody @Validated CertificationReportRequest request)
    {
        //查询用户与企业绑定信息
        RecruitEnterpriseUsersRel enterpriseUsersRel = recruitEnterpriseUsersRelService.selectEnterpriseUsersRelUserIdAndStatus(getUserId());
        if(enterpriseUsersRel != null) {
            RecruitAttestationManage attestationManage = new RecruitAttestationManage();
            attestationManage.setAttestationStatus("1");
            attestationManage.setAttestationReportUrl(request.getAttestationReportUrl());
            attestationManage.setEnterpriseId(enterpriseUsersRel.getEnterpriseId());
            attestationManage.setUserId(enterpriseUsersRel.getUserId());
            return success(recruitAttestationManageService.insertRecruitAttestationManageTwo(attestationManage));
        }else {
            throw new RuntimeException("该用户未与企业绑定！");
        }
    }

    @ApiOperation("查询同事管理")
    @GetMapping("/getColleagueManage")
    public TableDataInfo getColleagueManage(@NotNull(message = "企业id不能为空") Long enterpriseId, String userName)
    {
        //使用企业id和用户id进行查询
        RecruitAttestationManage attestationManage = recruitAttestationManageService.selectAttestationManageByEnterpriseAndUserId(enterpriseId, getUserId());
        if(attestationManage != null){
            if(StringUtils.equals(attestationManage.getAttestationStatus(), "2")){
                RecruitEnterpriseUsersRel recruitEnterpriseUsersRel = new RecruitEnterpriseUsersRel();
                recruitEnterpriseUsersRel.setUserName(userName);
                recruitEnterpriseUsersRel.setEnterpriseId(enterpriseId);
                recruitEnterpriseUsersRel.setUserId(getUserId());
                List<RecruitEnterpriseUsersRel> lists = recruitEnterpriseUsersRelService.getColleagueManage(recruitEnterpriseUsersRel);
                return getDataTable(lists);
            }else if(StringUtils.equals(attestationManage.getAttestationStatus(), "1")){
                throw new RuntimeException("认证中");
            }else if(StringUtils.equals(attestationManage.getAttestationStatus(), "3")){
                throw new RuntimeException("认证失败，不可操作");
            }else {
                throw new RuntimeException("该用户未认证企业管理");
            }
        }else {
            throw new RuntimeException("该用户未认证企业管理");
        }
    }

    @PostMapping("/delAttestation")
    @ApiOperation(value = "解除同事绑定关系",notes="解除同事绑定关系")
    public AjaxResult recallMessage(@RequestBody @Validated DelAttestationRequest request){
        //使用企业id和用户id进行查询
        RecruitAttestationManage attestationManage = recruitAttestationManageService.selectAttestationManageByEnterpriseAndUserId(request.getEnterpriseId(), getUserId());
        if(attestationManage != null){
            RecruitEnterpriseUsersRel enterpriseUsersRel = recruitEnterpriseUsersRelService.selectEnterpriseUsersRelUserIdAndStatus(request.getUserId());
            if(enterpriseUsersRel != null) {
                int ss = recruitEnterpriseUsersRelService.deleteRecruitEnterpriseUsersRelById(enterpriseUsersRel.getId());
                if (ss > 0) {
                    recruitPositionInfoService.deletePositionInfoByEnterpriseAndUserId(request.getEnterpriseId(), enterpriseUsersRel.getUserId());
                }
                return toAjax(ss);
            }else {
                throw new RuntimeException("该用户未绑定企业");
            }
        }else {
            throw new RuntimeException("该用户未认证企业管理");
        }
    }


    /**
     * 创建一个临时文件
     * @param url 远端文件Url
     * @return File
     */
    private static File getFile(String url) {
        //对本地文件命名
        String fileName = url.substring(url.lastIndexOf("."), url.length());
        File file = null;

        URL urlfile;
        try {
            // 创建一个临时路径
            file = File.createTempFile("file", fileName);
            log.info("tempFile:->{}",file.getAbsolutePath());
            //下载
            urlfile = new URL(url);

            try (InputStream inStream = urlfile.openStream();
                 OutputStream os =  new FileOutputStream(file);) {
                int bytesRead = 0;
                byte[] buffer = new byte[8192];
                while ((bytesRead = inStream.read(buffer, 0, 8192)) != -1) {
                    os.write(buffer, 0, bytesRead);
                }
            }
        } catch (Exception e) {
            log.error("创建临时文件出错：-》{}",e.getMessage());
        }
        return file;
    }

}
