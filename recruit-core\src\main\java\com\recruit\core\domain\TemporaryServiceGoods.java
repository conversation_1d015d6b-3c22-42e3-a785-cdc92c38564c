package com.recruit.core.domain;

import java.util.List;
import com.recruit.common.annotation.Excel;
import com.recruit.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 服务商品对象 temporary_service_goods
 *
 * <AUTHOR>
 * @date 2023-06-01
 */
@Data
@ApiModel("服务商品")
public class TemporaryServiceGoods extends BaseEntity
{
    private static final long serialVersionUID = 1L;


    private Long id;


    @Excel(name = "服务行业编码")
    @ApiModelProperty("服务行业编码")
    private String serviceTrade;

    @ApiModelProperty("服务行业编码")
    private String serviceTradeName;


    @Excel(name = "行业logo")
    @ApiModelProperty("行业logo")
    private String tradeLogo;


    @Excel(name = "服务描述")
    @ApiModelProperty("服务描述")
    private String serviceDescription;

    @ApiModelProperty("轮播图")
    private String rotationChart;

    @ApiModelProperty("类型")
    private String type;

    /** 服务商品价格信息 */
    private List<TemporaryServiceGoodsPrice> temporaryServiceGoodsPriceList;


}
