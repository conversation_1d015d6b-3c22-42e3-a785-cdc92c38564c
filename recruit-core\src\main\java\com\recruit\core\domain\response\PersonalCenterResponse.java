package com.recruit.core.domain.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Auther: <PERSON> kong
 * @Date: 2023/4/18 14:50
 * @Description:
 */
@Data
@ApiModel("用户个人中心1")
public class PersonalCenterResponse {

    @ApiModelProperty("查询收藏职位数")
    private Integer collectionPositionNum;

    /*@ApiModelProperty("查询收藏公司数")
    private Integer collectionCompanyNum;

*/
    @ApiModelProperty("查看求职者被查看数")
    private Integer positionViewedNum;

    @ApiModelProperty("查看个人投递数")
    private Integer personalDeliveryNum;

    /*@ApiModelProperty("查看屏蔽公司数量")
    private Integer enterprisesNum;*/

    @ApiModelProperty("查看沟通数量")
    private Integer communicateNum;

    @ApiModelProperty("查看被邀面试数量")
    private Integer interviewNum;


}
