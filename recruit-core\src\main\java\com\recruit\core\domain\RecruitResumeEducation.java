package com.recruit.core.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.recruit.common.annotation.Excel;
import com.recruit.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 简历教育经历对象 recruit_resume_education
 *
 * <AUTHOR>
 * @date 2023-03-21
 */
@Data
@ApiModel("简历教育经历")
public class RecruitResumeEducation extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 用户id */
    @Excel(name = "用户id")
    @ApiModelProperty("用户id")
    private Long userId;

    /** 学校名称 */
    @Excel(name = "学校名称")
    @ApiModelProperty("学校名称")
    private String schoolName;

    /** 入校时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "入校时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("入校时间")
    private Date admissionTime;

    /** 离校时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "离校时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("离校时间")
    private Date departureTime;

    /** 所学专业 */
    @Excel(name = "所学专业")
    @ApiModelProperty("所学专业")
    private String major;

    /** 最高学历 */
    @Excel(name = "最高学历")
    @ApiModelProperty("最高学历")
    private String highestEducation;

    @ApiModelProperty("最高学历名称")
    private String highestEducationName;


}
