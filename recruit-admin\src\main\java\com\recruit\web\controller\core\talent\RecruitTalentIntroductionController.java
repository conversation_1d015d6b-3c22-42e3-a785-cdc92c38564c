package com.recruit.web.controller.core.talent;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.recruit.core.domain.RecruitOnlineJobFairs;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.recruit.common.annotation.Log;
import com.recruit.common.core.controller.BaseController;
import com.recruit.common.core.domain.AjaxResult;
import com.recruit.common.enums.BusinessType;
import com.recruit.core.domain.RecruitTalentIntroduction;
import com.recruit.core.service.IRecruitTalentIntroductionService;
import com.recruit.common.utils.poi.ExcelUtil;
import com.recruit.common.core.page.TableDataInfo;

/**
 * 人才引进Controller
 *
 * <AUTHOR>
 * @date 2023-05-10
 */
@RestController
@RequestMapping("/core/talentIntroduction")
public class RecruitTalentIntroductionController extends BaseController
{
    @Autowired
    private IRecruitTalentIntroductionService recruitTalentIntroductionService;

    /**
     * 查询人才引进列表
     */
    @PreAuthorize("@ss.hasPermi('core:talentIntroduction:list')")
    @GetMapping("/list")
    public TableDataInfo list(RecruitTalentIntroduction recruitTalentIntroduction)
    {
        startPage();
        List<RecruitTalentIntroduction> list = recruitTalentIntroductionService.selectRecruitTalentIntroductionList(recruitTalentIntroduction);
        return getDataTable(list);
    }

    /**
     * 导出人才引进列表
     */
    @PreAuthorize("@ss.hasPermi('core:talentIntroduction:export')")
    @Log(title = "人才引进", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RecruitTalentIntroduction recruitTalentIntroduction)
    {
        List<RecruitTalentIntroduction> list = recruitTalentIntroductionService.selectRecruitTalentIntroductionList(recruitTalentIntroduction);
        ExcelUtil<RecruitTalentIntroduction> util = new ExcelUtil<RecruitTalentIntroduction>(RecruitTalentIntroduction.class);
        util.exportExcel(response, list, "人才引进数据");
    }

    /**
     * 获取人才引进详细信息
     */
    @PreAuthorize("@ss.hasPermi('core:talentIntroduction:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(recruitTalentIntroductionService.selectRecruitTalentIntroductionById(id));
    }

    /**
     * 新增人才引进
     */
    @PreAuthorize("@ss.hasPermi('core:talentIntroduction:add')")
    @Log(title = "人才引进", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RecruitTalentIntroduction recruitTalentIntroduction)
    {
        List<RecruitTalentIntroduction> list = recruitTalentIntroductionService.selectRecruitTalentIntroductionList(new RecruitTalentIntroduction(){{
            setState("1");
        }});
        list.forEach(e->{
            e.setState("2");
            recruitTalentIntroductionService.updateRecruitTalentIntroduction(e);
        });
        return toAjax(recruitTalentIntroductionService.insertRecruitTalentIntroduction(recruitTalentIntroduction));
    }

    /**
     * 修改人才引进
     */
    @PreAuthorize("@ss.hasPermi('core:talentIntroduction:edit')")
    @Log(title = "人才引进", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RecruitTalentIntroduction recruitTalentIntroduction)
    {
        List<RecruitTalentIntroduction> list = recruitTalentIntroductionService.selectRecruitTalentIntroductionList(new RecruitTalentIntroduction(){{
            setState("1");
        }});
        list.forEach(e->{
            e.setState("2");
            recruitTalentIntroductionService.updateRecruitTalentIntroduction(e);
        });
        return toAjax(recruitTalentIntroductionService.updateRecruitTalentIntroduction(recruitTalentIntroduction));
    }

    /**
     * 删除人才引进
     */
    @PreAuthorize("@ss.hasPermi('core:talentIntroduction:remove')")
    @Log(title = "人才引进", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(recruitTalentIntroductionService.deleteRecruitTalentIntroductionByIds(ids));
    }


    @GetMapping("/getTalentIntroductionListAll")
    public TableDataInfo getTalentIntroductionListAll(RecruitTalentIntroduction recruitTalentIntroduction)
    {
        List<RecruitTalentIntroduction> list = recruitTalentIntroductionService.selectRecruitTalentIntroductionList(recruitTalentIntroduction);
        return getDataTable(list);
    }
}
