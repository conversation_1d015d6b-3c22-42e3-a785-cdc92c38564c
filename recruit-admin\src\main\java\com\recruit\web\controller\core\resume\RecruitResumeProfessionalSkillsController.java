package com.recruit.web.controller.core.resume;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.recruit.common.annotation.Log;
import com.recruit.common.core.controller.BaseController;
import com.recruit.common.core.domain.AjaxResult;
import com.recruit.common.enums.BusinessType;
import com.recruit.core.domain.RecruitResumeProfessionalSkills;
import com.recruit.core.service.IRecruitResumeProfessionalSkillsService;
import com.recruit.common.utils.poi.ExcelUtil;
import com.recruit.common.core.page.TableDataInfo;

/**
 * 简历职业技能Controller
 *
 * <AUTHOR>
 * @date 2023-03-19
 */
@RestController
@RequestMapping("/core/resumeProfessionalSkills")
public class RecruitResumeProfessionalSkillsController extends BaseController
{
    @Autowired
    private IRecruitResumeProfessionalSkillsService recruitResumeProfessionalSkillsService;

    /**
     * 查询简历职业技能列表
     */
    @PreAuthorize("@ss.hasPermi('core:resumeProfessionalSkills:list')")
    @GetMapping("/list")
    public TableDataInfo list(RecruitResumeProfessionalSkills recruitResumeProfessionalSkills)
    {
        startPage();
        List<RecruitResumeProfessionalSkills> list = recruitResumeProfessionalSkillsService.selectRecruitResumeProfessionalSkillsList(recruitResumeProfessionalSkills);
        return getDataTable(list);
    }

    /**
     * 导出简历职业技能列表
     */
    @PreAuthorize("@ss.hasPermi('core:resumeProfessionalSkills:export')")
    @Log(title = "简历职业技能", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RecruitResumeProfessionalSkills recruitResumeProfessionalSkills)
    {
        List<RecruitResumeProfessionalSkills> list = recruitResumeProfessionalSkillsService.selectRecruitResumeProfessionalSkillsList(recruitResumeProfessionalSkills);
        ExcelUtil<RecruitResumeProfessionalSkills> util = new ExcelUtil<RecruitResumeProfessionalSkills>(RecruitResumeProfessionalSkills.class);
        util.exportExcel(response, list, "简历职业技能数据");
    }

    /**
     * 获取简历职业技能详细信息
     */
    @PreAuthorize("@ss.hasPermi('core:resumeProfessionalSkills:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(recruitResumeProfessionalSkillsService.selectRecruitResumeProfessionalSkillsById(id));
    }

    /**
     * 新增简历职业技能
     */
    @PreAuthorize("@ss.hasPermi('core:resumeProfessionalSkills:add')")
    @Log(title = "简历职业技能", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RecruitResumeProfessionalSkills recruitResumeProfessionalSkills)
    {
        return toAjax(recruitResumeProfessionalSkillsService.insertRecruitResumeProfessionalSkills(recruitResumeProfessionalSkills));
    }

    /**
     * 修改简历职业技能
     */
    @PreAuthorize("@ss.hasPermi('core:resumeProfessionalSkills:edit')")
    @Log(title = "简历职业技能", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RecruitResumeProfessionalSkills recruitResumeProfessionalSkills)
    {
        return toAjax(recruitResumeProfessionalSkillsService.updateRecruitResumeProfessionalSkills(recruitResumeProfessionalSkills));
    }

    /**
     * 删除简历职业技能
     */
    @PreAuthorize("@ss.hasPermi('core:resumeProfessionalSkills:remove')")
    @Log(title = "简历职业技能", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(recruitResumeProfessionalSkillsService.deleteRecruitResumeProfessionalSkillsByIds(ids));
    }
}
