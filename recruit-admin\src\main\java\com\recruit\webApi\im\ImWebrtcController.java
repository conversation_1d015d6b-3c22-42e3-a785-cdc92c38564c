package com.recruit.webApi.im;

import com.recruit.common.client.IMClient;
import com.recruit.common.config.ICEServerConfig;
import com.recruit.common.core.controller.BaseController;
import com.recruit.common.core.domain.AjaxResult;
import com.recruit.common.enums.MessageType;
import com.recruit.common.im.PrivateMessageInfo;
import com.recruit.core.domain.request.im.CandidateRequest;
import com.recruit.core.domain.request.im.HandupRequest;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Auther: <PERSON> kong
 * @Date: 2023/4/7 15:13
 * @Description:
 */
@Api(tags= "IM webrtc视频单人通话")
@RestController
@RequestMapping("/web/api/ImWebrtc")
public class ImWebrtcController extends BaseController {
    @Autowired
    private IMClient imClient;

    @Autowired
    private ICEServerConfig iceServerConfig;

    @ApiOperation(httpMethod = "POST", value = "呼叫视频通话")
    @PostMapping("/call")
    public AjaxResult call(@RequestBody @Validated CandidateRequest request) {
        PrivateMessageInfo message = new PrivateMessageInfo();
        message.setType(MessageType.RTC_CALL.code());
        message.setRecvId(request.getUid());
        message.setSendId(getUserId());
        message.setContent(request.getCandidate());
        imClient.sendPrivateMessage(request.getUid(),message);
        return success();
    }

    @ApiOperation(httpMethod = "POST", value = "接受视频通话")
    @PostMapping("/accept")
    public AjaxResult accept(@RequestBody @Validated CandidateRequest request) {
        PrivateMessageInfo message = new PrivateMessageInfo();
        message.setType(MessageType.RTC_ACCEPT.code());
        message.setRecvId(request.getUid());
        message.setSendId(getUserId());
        message.setContent(request.getCandidate());
        imClient.sendPrivateMessage(request.getUid(),message);
        return success();
    }


    @ApiOperation(httpMethod = "POST", value = "拒绝视频通话")
    @PostMapping("/reject")
    public AjaxResult reject(@RequestBody @Validated HandupRequest request) {
        PrivateMessageInfo message = new PrivateMessageInfo();
        message.setType(MessageType.RTC_REJECT.code());
        message.setRecvId(request.getUid());
        message.setSendId(getUserId());
        imClient.sendPrivateMessage(request.getUid(),message);
        return success();
    }

    @ApiOperation(httpMethod = "POST", value = "取消呼叫")
    @PostMapping("/cancel")
    public AjaxResult cancel(@RequestBody @Validated HandupRequest request) {
        PrivateMessageInfo message = new PrivateMessageInfo();
        message.setType(MessageType.RTC_CANCEL.code());
        message.setRecvId(request.getUid());
        message.setSendId(getUserId());
        imClient.sendPrivateMessage(request.getUid(),message);
        return success();
    }

    @ApiOperation(httpMethod = "POST", value = "呼叫失败")
    @PostMapping("/failed")
    public AjaxResult failed(@RequestBody @Validated CandidateRequest request) {
        PrivateMessageInfo message = new PrivateMessageInfo();
        message.setType(MessageType.RTC_FAILED.code());
        message.setRecvId(request.getUid());
        message.setSendId(getUserId());
        message.setContent(request.getCandidate());
        imClient.sendPrivateMessage(request.getUid(),message);
        return success();
    }

    @ApiOperation(httpMethod = "POST", value = "挂断")
    @PostMapping("/handup")
    public AjaxResult leave(@RequestBody @Validated HandupRequest request) {
        PrivateMessageInfo message = new PrivateMessageInfo();
        message.setType(MessageType.RTC_HANDUP.code());
        message.setRecvId(request.getUid());
        message.setSendId(getUserId());
        imClient.sendPrivateMessage(request.getUid(),message);
        return success();
    }


    @PostMapping("/candidate")
    @ApiOperation(httpMethod = "POST", value = "同步candidate")
    public AjaxResult candidate(@RequestBody @Validated CandidateRequest request) {
        PrivateMessageInfo message = new PrivateMessageInfo();
        message.setType(MessageType.RTC_CANDIDATE.code());
        message.setRecvId(request.getUid());
        message.setSendId(getUserId());
        message.setContent(request.getCandidate());
        imClient.sendPrivateMessage(request.getUid(), message);
        return success();
    }

    @GetMapping("/iceservers")
    @ApiOperation(httpMethod = "GET", value = "获取iceservers")
    public AjaxResult iceservers() {
        return success(iceServerConfig.getIceServers());
    }

}
