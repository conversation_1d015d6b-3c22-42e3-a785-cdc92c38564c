package com.recruit.core.domain.response;

import me.chanjar.weixin.common.bean.ToJson;
import me.chanjar.weixin.common.util.json.WxGsonBuilder;
import me.chanjar.weixin.mp.bean.draft.WxMpDraftArticles;
import me.chanjar.weixin.mp.bean.draft.WxMpDraftInfo;

import java.io.Serializable;
import java.util.List;

public class WxMpDraftInfoResponse implements ToJson, Serializable {

    private static final long serialVersionUID = 6111694033486314392L;
    private List<WxMpDraftArticles> newsItem;

    public static WxMpDraftInfoResponse fromJson(String json) {
        return (WxMpDraftInfoResponse) WxGsonBuilder.create().fromJson(json, WxMpDraftInfoResponse.class);
    }

    public String toJson() {
        return WxGsonBuilder.create().toJson(this);
    }

    public static WxMpDraftInfoResponse.WxMpDraftInfoBuilder builder() {
        return new WxMpDraftInfoResponse.WxMpDraftInfoBuilder();
    }

    public List<WxMpDraftArticles> getNewsItem() {
        return this.newsItem;
    }

    public WxMpDraftInfoResponse setNewsItem(List<WxMpDraftArticles> newsItem) {
        this.newsItem = newsItem;
        return this;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        } else if (!(o instanceof WxMpDraftInfoResponse)) {
            return false;
        } else {
            WxMpDraftInfoResponse other = (WxMpDraftInfoResponse)o;
            if (!other.canEqual(this)) {
                return false;
            } else {
                Object this$newsItem = this.getNewsItem();
                Object other$newsItem = other.getNewsItem();
                if (this$newsItem == null) {
                    if (other$newsItem != null) {
                        return false;
                    }
                } else if (!this$newsItem.equals(other$newsItem)) {
                    return false;
                }

                return true;
            }
        }
    }

    protected boolean canEqual(Object other) {
        return other instanceof WxMpDraftInfo;
    }

    public int hashCode() {
        boolean PRIME = true;
        int result = 1;
        Object $newsItem = this.getNewsItem();
        result = result * 59 + ($newsItem == null ? 43 : $newsItem.hashCode());
        return result;
    }

    public String toString() {
        return "WxMpDraftInfo(newsItem=" + this.getNewsItem() + ")";
    }

    public WxMpDraftInfoResponse() {
    }

    public WxMpDraftInfoResponse(List<WxMpDraftArticles> newsItem) {
        this.newsItem = newsItem;
    }

    public static class WxMpDraftInfoBuilder {
        private List<WxMpDraftArticles> newsItem;

        WxMpDraftInfoBuilder() {
        }

        public WxMpDraftInfoResponse.WxMpDraftInfoBuilder newsItem(List<WxMpDraftArticles> newsItem) {
            this.newsItem = newsItem;
            return this;
        }

        public WxMpDraftInfo build() {
            return new WxMpDraftInfo(this.newsItem);
        }

        public String toString() {
            return "WxMpDraftInfo.WxMpDraftInfoBuilder(newsItem=" + this.newsItem + ")";
        }
    }
}
