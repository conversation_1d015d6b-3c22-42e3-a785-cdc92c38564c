package com.recruit.core.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.recruit.common.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;


/**
 * 消息自动回复
 *
 * <AUTHOR>
 * @date 2019-04-18 15:40:39
 */
@Data
@TableName("wx_auto_reply")
@EqualsAndHashCode(callSuper = true)
public class WxAutoReply extends Model<WxAutoReply> {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    private String delFlag;


    @Excel(name = "类型", readConverterExp = "1=、关注时回复；2、消息回复；3、关键词回复")
    @ApiModelProperty("类型")
    private String type;


    @Excel(name = "关键词")
    @ApiModelProperty("关键词")
    private String reqKey;


    @Excel(name = "请求消息类型", readConverterExp = "t=ext：文本；image：图片；voice：语音；video：视频；shortvideo：小视频；location：地理位置")
    @ApiModelProperty("请求消息类型")
    private String reqType;


    @Excel(name = "回复消息类型", readConverterExp = "t=ext：文本；image：图片；voice：语音；video：视频；music：音乐；news：图文")
    @ApiModelProperty("回复消息类型")
    private String repType;


    @Excel(name = "回复类型文本匹配类型", readConverterExp = "1=、全匹配，2、半匹配")
    @ApiModelProperty("回复类型文本匹配类型")
    private String repMate;


    @Excel(name = "回复类型文本保存文字")
    @ApiModelProperty("回复类型文本保存文字")
    private String repContent;


    @Excel(name = "回复类型imge、voice、news、video的mediaID或音乐缩略图的媒体id")
    @ApiModelProperty("回复类型imge、voice、news、video的mediaID或音乐缩略图的媒体id")
    private String repMediaId;


    @Excel(name = "回复的素材名、视频和音乐的标题")
    @ApiModelProperty("回复的素材名、视频和音乐的标题")
    private String repName;


    @Excel(name = "视频和音乐的描述")
    @ApiModelProperty("视频和音乐的描述")
    private String repDesc;


    @Excel(name = "链接")
    @ApiModelProperty("链接")
    private String repUrl;


    @Excel(name = "高质量链接")
    @ApiModelProperty("高质量链接")
    private String repHqUrl;


    @Excel(name = "缩略图的媒体id")
    @ApiModelProperty("缩略图的媒体id")
    private String repThumbMediaId;


    @Excel(name = "缩略图url")
    @ApiModelProperty("缩略图url")
    private String repThumbUrl;


    @Excel(name = "图文消息的内容")
    @ApiModelProperty("图文消息的内容")
    private String content;

    /**
     * 创建者
     */
    private String createBy;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新者
     */
    private String updateBy;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 备注
     */
    private String remark;
}
