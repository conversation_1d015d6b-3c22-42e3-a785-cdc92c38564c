package com.recruit.core.domain.request.position;

import com.recruit.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Auther: <PERSON> kong
 * @Date: 2023/3/30 9:45
 * @Description:
 */
@Data
@ApiModel("查询求职信息2")
public class ResumeListRequest extends BaseEntity {

    @ApiModelProperty("查询类别，1推荐，2最新，3置顶")
    private String queryCategory;

    @ApiModelProperty("所在城市")
    private String city;

    @ApiModelProperty("工作经验")
    private String workExperience;

    @ApiModelProperty("性别")
    private String sex;

    @ApiModelProperty("最高学历")
    private String education;

    @ApiModelProperty("最高薪资")
    private String maximumSalary;

    @ApiModelProperty("最低薪资")
    private String minimumWage;

    @ApiModelProperty("到岗时间，数据字典")
    private String jobStatus;

    @ApiModelProperty("岗位")
    private String expectedPosition;

    private Integer grade;

    @ApiModelProperty("发布人")
    private Long publisherId;

}
