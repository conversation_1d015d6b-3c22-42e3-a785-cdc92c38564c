package com.recruit.core.domain;

import com.recruit.common.annotation.Excel;
import com.recruit.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 人才引进公告对象 recruit_talent_introduction_notice
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
@Data
@ApiModel("人才引进公告")
public class RecruitTalentIntroductionNotice extends BaseEntity
{
    private static final long serialVersionUID = 1L;


    private Long noticeId;


    @Excel(name = "人才引进ID")
    @ApiModelProperty("人才引进ID")
    private Long talentIntroductionId;


    @Excel(name = "公告标题")
    @ApiModelProperty("公告标题")
    private String noticeTitle;


    @Excel(name = "公告类型", readConverterExp = "1=通知,2=公告")
    @ApiModelProperty("公告类型")
    private String noticeType;


    @Excel(name = "公告内容")
    @ApiModelProperty("公告内容")
    private String noticeContent;


    @Excel(name = "公告状态", readConverterExp = "0=正常,1=关闭")
    @ApiModelProperty("公告状态")
    private String status;


}
