package com.recruit.common.core.domain;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.recruit.common.core.domain.entity.RecruitCompanyIndustry;
import com.recruit.common.core.domain.entity.RecruitPosition;
import com.recruit.common.core.domain.entity.SysServiceTrade;

import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Auther: Wu kong
 * @Date: 2023/3/31 0:49
 * @Description:
 */
public class TreeSelectTwo implements Serializable {

    private String label;
    private String value;

    private String type;

    private String dataDescribe;

    private String tradeLogo;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<TreeSelectTwo> children;

    public TreeSelectTwo()
    {

    }

    public TreeSelectTwo(RecruitCompanyIndustry menu)
    {
        this.value = String.valueOf(menu.getId());
        this.label = menu.getName();
        this.children = menu.getChildren().stream().map(TreeSelectTwo::new).collect(Collectors.toList());
    }

    public TreeSelectTwo(RecruitPosition menu)
    {
        this.value = String.valueOf(menu.getId());
        this.label = menu.getName();
        this.children = menu.getChildren().stream().map(TreeSelectTwo::new).collect(Collectors.toList());
    }

    public TreeSelectTwo(SysServiceTrade menu)
    {
        this.value = String.valueOf(menu.getId());
        this.label = menu.getName();
        this.type = menu.getType();
        this.dataDescribe = menu.getDataDescribe();
        this.tradeLogo = menu.getTradeLogo();
        this.children = menu.getChildren().stream().map(TreeSelectTwo::new).collect(Collectors.toList());
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getDataDescribe() {
        return dataDescribe;
    }

    public void setDataDescribe(String dataDescribe) {
        this.dataDescribe = dataDescribe;
    }

    public String getTradeLogo() {
        return tradeLogo;
    }

    public void setTradeLogo(String tradeLogo) {
        this.tradeLogo = tradeLogo;
    }

    public List<TreeSelectTwo> getChildren() {
        return children;
    }

    public void setChildren(List<TreeSelectTwo> children) {
        this.children = children;
    }
}
