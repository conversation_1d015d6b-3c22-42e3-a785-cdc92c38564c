package com.recruit.core.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.recruit.common.annotation.Excel;
import com.recruit.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 用户订单对象 recruit_user_order
 *
 * <AUTHOR>
 * @date 2023-05-22
 */
@Data
@ApiModel("用户订单")
public class RecruitUserOrder extends BaseEntity
{
    private static final long serialVersionUID = 1L;


    private Long id;


    @Excel(name = "用户id")
    @ApiModelProperty("用户id")
    private Long userId;

    @ApiModelProperty("用户名称")
    private String userName;


    @Excel(name = "外部订单流水号")
    @ApiModelProperty("外部订单流水号")
    private String bizPayNo;


    @Excel(name = "第三方系统的订单号(paypal)")
    @ApiModelProperty("第三方系统的订单号(paypal)")
    private String bizOrderNo;


    @Excel(name = "订单类型")
    @ApiModelProperty("订单类型")
    private String orderType;


    @Excel(name = "订单金额")
    @ApiModelProperty("订单金额")
    private BigDecimal orderAmount;


    @Excel(name = "订单返现积分")
    @ApiModelProperty("订单返现积分")
    private String oderCashbackIntegral;


    @Excel(name = "订单状态 0进行中，1已完成，2订单失败")
    @ApiModelProperty("订单状态 0进行中，1已完成，2订单失败")
    private String orderStatus;


    @Excel(name = "订单信息")
    @ApiModelProperty("订单信息")
    private String orderMsg;


    @Excel(name = "回调内容")
    @ApiModelProperty("回调内容")
    private String callbackContent;


    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "回调时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("回调时间")
    private Date callbackTime;


    @Excel(name = "支付渠道 1微信，2支付宝")
    @ApiModelProperty("支付渠道 1微信，2支付宝")
    private String payChannel;

    @ApiModelProperty("是否充值账户")
    private String rechargeAccount;


}
