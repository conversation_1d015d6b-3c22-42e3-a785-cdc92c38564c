package com.recruit.web.controller.core.enterprise;

import java.util.Date;
import java.util.List;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import com.recruit.common.notify.NotifyService;
import com.recruit.common.utils.StringUtils;
import com.recruit.core.domain.SysSmsSendingRecord;
import com.recruit.core.domain.SysSmsTemplate;
import com.recruit.core.service.IRecruitPositionInfoService;
import com.recruit.core.service.ISysSmsSendingRecordService;
import com.recruit.core.service.ISysSmsTemplateService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.recruit.common.annotation.Log;
import com.recruit.common.core.controller.BaseController;
import com.recruit.common.core.domain.AjaxResult;
import com.recruit.common.enums.BusinessType;
import com.recruit.core.domain.RecruitEnterpriseUsersRel;
import com.recruit.core.service.IRecruitEnterpriseUsersRelService;
import com.recruit.common.utils.poi.ExcelUtil;
import com.recruit.common.core.page.TableDataInfo;

/**
 * 企业与用户关联Controller
 *
 * <AUTHOR>
 * @date 2023-03-21
 */
@RestController
@RequestMapping("/core/enterpriseUsersRel")
public class RecruitEnterpriseUsersRelController extends BaseController
{
    @Resource
    private NotifyService notifyService;

    @Autowired
    private ISysSmsTemplateService sysSmsTemplateService;

    @Autowired
    private ISysSmsSendingRecordService sysSmsSendingRecordService;

    @Autowired
    private IRecruitPositionInfoService recruitPositionInfoService;

    @Autowired
    private IRecruitEnterpriseUsersRelService recruitEnterpriseUsersRelService;

    /**
     * 查询企业与用户关联列表
     */
    @PreAuthorize("@ss.hasPermi('core:enterpriseUsersRel:list')")
    @GetMapping("/list")
    public TableDataInfo list(RecruitEnterpriseUsersRel recruitEnterpriseUsersRel)
    {
        startPage();
        if(StringUtils.equals(recruitEnterpriseUsersRel.getBindingStatus(), "ALL")){
            recruitEnterpriseUsersRel.setBindingStatus(null);
        }
        List<RecruitEnterpriseUsersRel> list = recruitEnterpriseUsersRelService.selectRecruitEnterpriseUsersRelList(recruitEnterpriseUsersRel);
        return getDataTable(list);
    }

    /**
     * 导出企业与用户关联列表
     */
    @PreAuthorize("@ss.hasPermi('core:enterpriseUsersRel:export')")
    @Log(title = "企业与用户关联", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RecruitEnterpriseUsersRel recruitEnterpriseUsersRel)
    {
        List<RecruitEnterpriseUsersRel> list = recruitEnterpriseUsersRelService.selectRecruitEnterpriseUsersRelList(recruitEnterpriseUsersRel);
        ExcelUtil<RecruitEnterpriseUsersRel> util = new ExcelUtil<RecruitEnterpriseUsersRel>(RecruitEnterpriseUsersRel.class);
        util.exportExcel(response, list, "企业与用户关联数据");
    }

    /**
     * 获取企业与用户关联详细信息
     */
    @PreAuthorize("@ss.hasPermi('core:enterpriseUsersRel:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(recruitEnterpriseUsersRelService.selectRecruitEnterpriseUsersRelById(id));
    }

    /**
     * 新增企业与用户关联
     */
    @PreAuthorize("@ss.hasPermi('core:enterpriseUsersRel:add')")
    @Log(title = "企业与用户关联", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RecruitEnterpriseUsersRel recruitEnterpriseUsersRel)
    {
        return toAjax(recruitEnterpriseUsersRelService.insertRecruitEnterpriseUsersRel(recruitEnterpriseUsersRel));
    }

    /**
     * 修改企业与用户关联
     */
    @PreAuthorize("@ss.hasPermi('core:enterpriseUsersRel:edit')")
    @Log(title = "企业与用户关联", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RecruitEnterpriseUsersRel recruitEnterpriseUsersRel)
    {
        int ss = recruitEnterpriseUsersRelService.updateRecruitEnterpriseUsersRel(recruitEnterpriseUsersRel);
        if(ss > 0){
            if(StringUtils.equals(recruitEnterpriseUsersRel.getBindingStatus(), "2")) {
                sendAuthenticationMessage(recruitEnterpriseUsersRel.getPhone(), recruitEnterpriseUsersRel.getUserId());
            }
        }
        return toAjax(ss);
    }

    /**
     * 推送认证消息
     */
    private void sendAuthenticationMessage(String phone, Long userId){
        SysSmsTemplate smsTemplate = sysSmsTemplateService.selectSmsTemplateBySendType("4");
        if(smsTemplate != null){
            notifyService.notifySms(phone, smsTemplate.getTemplateContent(), String.valueOf(smsTemplate.getTemplateId()));

            //记录 企业认证消息 记录
            SysSmsSendingRecord smsSendingRecord = new SysSmsSendingRecord();
            smsSendingRecord.setUserId(userId);
            smsSendingRecord.setPhone(phone);
            smsSendingRecord.setNoticeTitle(smsTemplate.getTemplateName());
            smsSendingRecord.setNoticeType("1");
            smsSendingRecord.setNoticeContent(smsTemplate.getTemplateContent());
            smsSendingRecord.setSentType("1");
            smsSendingRecord.setSmsTemplateId(smsTemplate.getId());
            smsSendingRecord.setSendTime(new Date());
            sysSmsSendingRecordService.insertSysSmsSendingRecord(smsSendingRecord);
        }
    }

    /**
     * 修改企业与用户关联
     */
    @PreAuthorize("@ss.hasPermi('core:enterpriseUsersRel:unbinding')")
    @Log(title = "企业与用户关联", businessType = BusinessType.UPDATE)
    @DeleteMapping("/unbinding/{id}")
    public AjaxResult unbinding(@PathVariable("id") Long id)
    {
        RecruitEnterpriseUsersRel enterpriseUsersRel = recruitEnterpriseUsersRelService.selectRecruitEnterpriseUsersRelById(id);
        int ss = recruitEnterpriseUsersRelService.deleteRecruitEnterpriseUsersRelById(id);
        if(ss > 0){
            recruitPositionInfoService.deletePositionInfoByEnterpriseAndUserId(enterpriseUsersRel.getEnterpriseId(), enterpriseUsersRel.getUserId());
        }
        return toAjax(ss);
    }
    /**
     * 审核企业与用户关联
     */
    @PreAuthorize("@ss.hasPermi('core:enterpriseUsersRel:process')")
    @Log(title = "企业与用户关联", businessType = BusinessType.PROCESS)
    @PutMapping("/process")
    public AjaxResult process(@RequestBody RecruitEnterpriseUsersRel recruitEnterpriseUsersRel)
    {
        return toAjax(recruitEnterpriseUsersRelService.updateRecruitEnterpriseUsersRel(recruitEnterpriseUsersRel));
    }

    /**
     * 删除企业与用户关联
     */
    @PreAuthorize("@ss.hasPermi('core:enterpriseUsersRel:remove')")
    @Log(title = "企业与用户关联", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(recruitEnterpriseUsersRelService.deleteRecruitEnterpriseUsersRelByIds(ids));
    }
}
