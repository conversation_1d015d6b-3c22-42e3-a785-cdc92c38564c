package com.recruit.core.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.recruit.common.annotation.Excel;
import com.recruit.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 人才引进对象 recruit_talent_introduction
 *
 * <AUTHOR>
 * @date 2023-05-10
 */
@Data
@ApiModel("人才引进")
public class RecruitTalentIntroduction extends BaseEntity
{
    private static final long serialVersionUID = 1L;


    private Long id;


    @Excel(name = "期数")
    @ApiModelProperty("期数")
    private String numberOfPeriods;


    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("开始时间")
    private Date startTime;


    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("结束时间")
    private Date endTime;


    @Excel(name = "状态，1发布中，2发布结束")
    @ApiModelProperty("状态，1发布中，2发布结束")
    private String state;


    @Excel(name = "小程序图片地址")
    @ApiModelProperty("小程序图片地址")
    private String appPicUrl;


    @Excel(name = "pc图片地址")
    @ApiModelProperty("pc图片地址")
    private String pcPicUrl;


    @ApiModelProperty("小程序图片地址")
    private String appPicTwoUrl;


    @ApiModelProperty("pc图片地址")
    private String pcPicTwoUrl;


    @Excel(name = "标题")
    @ApiModelProperty("标题")
    private String title;

    @Excel(name = "招聘公告")
    @ApiModelProperty("招聘公告")
    private String contents;

    @ApiModelProperty("咨询信息")
    private String consultationInfo;

    @ApiModelProperty("报名须知")
    private String recruitmentNotice;


}
