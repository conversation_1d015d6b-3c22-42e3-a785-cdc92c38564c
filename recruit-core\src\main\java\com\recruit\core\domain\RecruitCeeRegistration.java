package com.recruit.core.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.recruit.common.annotation.Excel;
import com.recruit.common.core.domain.BaseEntity;
import lombok.Data;

/**
 * 高考报名管理对象 recruit_cee_registration
 *
 * <AUTHOR>
 * @date 2024-05-06
 */
@Data
public class RecruitCeeRegistration extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 学生姓名 */
    @Excel(name = "学生姓名")
    private String name;

    /** 性别 */
    @Excel(name = "性别")
    private String sex;

    /** 年龄 */
    @Excel(name = "年龄")
    private Long age;

    /** 专业 */
    @Excel(name = "专业")
    private String major;

    /** 家长电话 */
    @Excel(name = "家长电话")
    private String phone;


    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("name", getName())
            .append("sex", getSex())
            .append("age", getAge())
            .append("major", getMajor())
            .append("phone", getPhone())
            .toString();
    }
}
