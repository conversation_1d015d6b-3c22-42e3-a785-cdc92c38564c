package com.recruit.common.utils.seeker.processor;


import com.recruit.common.utils.seeker.KeyWord;

/**
 *
 * 替换内容的片段处理方式
 *
 * <AUTHOR>
 *
 */
public class IgnoreFragment extends AbstractFragment {

    private String ignore = "";

    public IgnoreFragment() {
    }

    public IgnoreFragment(String ignore) {
        this.ignore = ignore;
    }

    @Override
    public String format(KeyWord word) {
        return ignore;
    }

}
