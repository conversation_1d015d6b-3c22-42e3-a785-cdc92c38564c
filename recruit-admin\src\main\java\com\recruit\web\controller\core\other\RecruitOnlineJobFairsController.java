package com.recruit.web.controller.core.other;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.recruit.common.annotation.Log;
import com.recruit.common.core.controller.BaseController;
import com.recruit.common.core.domain.AjaxResult;
import com.recruit.common.enums.BusinessType;
import com.recruit.core.domain.RecruitOnlineJobFairs;
import com.recruit.core.service.IRecruitOnlineJobFairsService;
import com.recruit.common.utils.poi.ExcelUtil;
import com.recruit.common.core.page.TableDataInfo;

/**
 * 网络招聘会Controller
 *
 * <AUTHOR>
 * @date 2023-05-03
 */
@RestController
@RequestMapping("/core/jobFairs")
public class RecruitOnlineJobFairsController extends BaseController
{
    @Autowired
    private IRecruitOnlineJobFairsService recruitOnlineJobFairsService;

    /**
     * 查询网络招聘会列表
     */
    @PreAuthorize("@ss.hasPermi('core:jobFairs:list')")
    @GetMapping("/list")
    public TableDataInfo list(RecruitOnlineJobFairs recruitOnlineJobFairs)
    {
        startPage();
        List<RecruitOnlineJobFairs> list = recruitOnlineJobFairsService.selectRecruitOnlineJobFairsList(recruitOnlineJobFairs);
        return getDataTable(list);
    }

    /**
     * 导出网络招聘会列表
     */
    @PreAuthorize("@ss.hasPermi('core:jobFairs:export')")
    @Log(title = "网络招聘会", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RecruitOnlineJobFairs recruitOnlineJobFairs)
    {
        List<RecruitOnlineJobFairs> list = recruitOnlineJobFairsService.selectRecruitOnlineJobFairsList(recruitOnlineJobFairs);
        ExcelUtil<RecruitOnlineJobFairs> util = new ExcelUtil<RecruitOnlineJobFairs>(RecruitOnlineJobFairs.class);
        util.exportExcel(response, list, "网络招聘会数据");
    }

    /**
     * 获取网络招聘会详细信息
     */
    @PreAuthorize("@ss.hasPermi('core:jobFairs:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(recruitOnlineJobFairsService.selectRecruitOnlineJobFairsById(id));
    }

    /**
     * 新增网络招聘会
     */
    @PreAuthorize("@ss.hasPermi('core:jobFairs:add')")
    @Log(title = "网络招聘会", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RecruitOnlineJobFairs recruitOnlineJobFairs)
    {

        List<RecruitOnlineJobFairs> list = recruitOnlineJobFairsService.selectRecruitOnlineJobFairsList(new RecruitOnlineJobFairs(){{
            setState("1");
        }});
        list.forEach(e->{
            e.setState("2");
            recruitOnlineJobFairsService.updateRecruitOnlineJobFairs(e);
        });
        return toAjax(recruitOnlineJobFairsService.insertRecruitOnlineJobFairs(recruitOnlineJobFairs));
    }

    /**
     * 修改网络招聘会
     */
    @PreAuthorize("@ss.hasPermi('core:jobFairs:edit')")
    @Log(title = "网络招聘会", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RecruitOnlineJobFairs recruitOnlineJobFairs)
    {
        List<RecruitOnlineJobFairs> list = recruitOnlineJobFairsService.selectRecruitOnlineJobFairsList(new RecruitOnlineJobFairs(){{
            setState("1");
        }});
        list.forEach(e->{
            e.setState("2");
            recruitOnlineJobFairsService.updateRecruitOnlineJobFairs(e);
        });
        return toAjax(recruitOnlineJobFairsService.updateRecruitOnlineJobFairs(recruitOnlineJobFairs));
    }

    /**
     * 删除网络招聘会
     */
    @PreAuthorize("@ss.hasPermi('core:jobFairs:remove')")
    @Log(title = "网络招聘会", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(recruitOnlineJobFairsService.deleteRecruitOnlineJobFairsByIds(ids));
    }


    @GetMapping("/onlineJobFairsListAll")
    public TableDataInfo onlineJobFairsListAll()
    {
        List<RecruitOnlineJobFairs> list = recruitOnlineJobFairsService.selectRecruitOnlineJobFairsList(new RecruitOnlineJobFairs());
        return getDataTable(list);
    }
}
