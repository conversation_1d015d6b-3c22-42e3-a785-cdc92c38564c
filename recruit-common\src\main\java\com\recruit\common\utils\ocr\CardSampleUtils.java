package com.recruit.common.utils.ocr;

import okhttp3.*;
import java.io.*;
import org.json.JSONObject;

/**
 * @Auther: <PERSON> kong
 * @Date: 2023/6/16 10:00
 * @Description:
 */
public class CardSampleUtils {

    public static final String API_KEY = "38KmV3RDm8xlhODphhvqXD7z";
    public static final String SECRET_KEY = "heicKrATb8X25wEEFnPh35KnicCNpDPy";

    static final OkHttpClient HTTP_CLIENT = new OkHttpClient().newBuilder().build();


    /**
     * 校验身份证
     * @param url
     * @return
     */
    public static String verifyIDCard(String url) throws Exception{
        /*MediaType mediaType = MediaType.parse("application/x-www-form-urlencoded");
        RequestBody body = RequestBody.create(mediaType, "id_card_side=front&url="+url);
        Request request = new Request.Builder()
                .url("https://aip.baidubce.com/rest/2.0/ocr/v1/idcard?access_token=" + getAccessToken())
                .method("POST", body)
                .addHeader("Content-Type", "application/x-www-form-urlencoded")
                .addHeader("Accept", "application/json")
                .build();
        Response response = HTTP_CLIENT.newCall(request).execute();*/

        String ss = "{\n" +
                "    \"words_result\": {\n" +
                "        \"姓名\": {\n" +
                "            \"location\": {\n" +
                "                \"top\": 179,\n" +
                "                \"left\": 485,\n" +
                "                \"width\": 277,\n" +
                "                \"height\": 120\n" +
                "            },\n" +
                "            \"words\": \"刘超\"\n" +
                "        },\n" +
                "        \"民族\": {\n" +
                "            \"location\": {\n" +
                "                \"top\": 396,\n" +
                "                \"left\": 961,\n" +
                "                \"width\": 78,\n" +
                "                \"height\": 98\n" +
                "            },\n" +
                "            \"words\": \"汉\"\n" +
                "        },\n" +
                "        \"住址\": {\n" +
                "            \"location\": {\n" +
                "                \"top\": 780,\n" +
                "                \"left\": 478,\n" +
                "                \"width\": 1027,\n" +
                "                \"height\": 241\n" +
                "            },\n" +
                "            \"words\": \"山东省莱西市河头店镇捉马台村76号\"\n" +
                "        },\n" +
                "        \"公民身份号码\": {\n" +
                "            \"location\": {\n" +
                "                \"top\": 1270,\n" +
                "                \"left\": 885,\n" +
                "                \"width\": 1337,\n" +
                "                \"height\": 119\n" +
                "            },\n" +
                "            \"words\": \"370285199405095618\"\n" +
                "        },\n" +
                "        \"出生\": {\n" +
                "            \"location\": {\n" +
                "                \"top\": 577,\n" +
                "                \"left\": 463,\n" +
                "                \"width\": 808,\n" +
                "                \"height\": 103\n" +
                "            },\n" +
                "            \"words\": \"19940509\"\n" +
                "        },\n" +
                "        \"性别\": {\n" +
                "            \"location\": {\n" +
                "                \"top\": 393,\n" +
                "                \"left\": 472,\n" +
                "                \"width\": 89,\n" +
                "                \"height\": 103\n" +
                "            },\n" +
                "            \"words\": \"男\"\n" +
                "        }\n" +
                "    },\n" +
                "    \"words_result_num\": 6,\n" +
                "    \"idcard_number_type\": 1,\n" +
                "    \"image_status\": \"normal\",\n" +
                "    \"log_id\": \"1669531086424254956\"\n" +
                "}";
        return ss;
    }


    /**
     * 从用户的AK，SK生成鉴权签名（Access Token）
     *
     * @return 鉴权签名（Access Token）
     * @throws IOException IO异常
     */
    static String getAccessToken() throws IOException {
        MediaType mediaType = MediaType.parse("application/x-www-form-urlencoded");
        RequestBody body = RequestBody.create(mediaType, "grant_type=client_credentials&client_id=" + API_KEY
                + "&client_secret=" + SECRET_KEY);
        Request request = new Request.Builder()
                .url("https://aip.baidubce.com/oauth/2.0/token")
                .method("POST", body)
                .addHeader("Content-Type", "application/x-www-form-urlencoded")
                .build();
        Response response = HTTP_CLIENT.newCall(request).execute();
        return new JSONObject(response.body().string()).getString("access_token");
    }
}
