package com.recruit.core.domain.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.recruit.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @Auther: Wu kong
 * @Date: 2023/4/12 16:31
 * @Description:
 */
@Data
@ApiModel("查询收藏公司1")
public class CollectionCompanyResponse {

    private Long id;


    @Excel(name = "用户id")
    @ApiModelProperty("用户id")
    private Long userId;

    @Excel(name = "企业id")
    @ApiModelProperty("企业id")
    private Long enterpriseId;

    @Excel(name = "求职者收藏职位1，招聘者求职者2, 求职者收藏企业3")
    @ApiModelProperty("求职者收藏职位1，招聘者求职者2, 求职者收藏企业3")
    private String collectType;

    @ApiModelProperty("公司名称")
    private String enterpriseName;

    @ApiModelProperty("公司logo")
    private String enterpriseLogo;

    @ApiModelProperty("公司性质")
    private String enterpriseNature;

    @ApiModelProperty("公司性质名称")
    private String enterpriseNatureName;

    @ApiModelProperty("公司规模")
    private String scale;

    @ApiModelProperty("公司规模名称")
    private String scaleName;

    @ApiModelProperty("所属行业码")
    private String industry;

    @ApiModelProperty("所属行业")
    private String companyIndustryName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @ApiModelProperty("蓝V状态")
    private String state;

}
